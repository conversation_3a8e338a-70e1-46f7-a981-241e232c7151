# Moment Search - 智能新标签页搜索扩展

> 一个现代化的浏览器新标签页扩展，提供智能搜索、快捷方式管理和个性化定制功能

## 🚀 项目概述

Moment Search V3 是一个功能丰富的Chrome扩展，旨在替换默认的新标签页，为用户提供：

- **🔍 智能搜索系统** - 支持多平台搜索和别名快捷搜索
- **⚡ 快捷方式管理** - 多页面快捷方式，支持拖拽排序
- **⏰ 时间显示** - 可自定义的时间和日期显示
- **🎨 个性化主题** - 多种主题和背景选择
- **🛠️ 自定义平台** - 智能URL解析，添加任意搜索网站

## 📁 项目结构

```
moment_web/
├── moment-search-v3/          # V3主要代码
│   ├── index.html            # 主页面
│   ├── script.js             # 核心逻辑
│   ├── style.css             # 样式文件
│   ├── manifest.json         # 扩展配置
│   └── background.js         # 后台脚本
├── docs/                     # 项目文档
│   ├── quick_implementation_guide.md      # 快速实施指南
│   ├── platform_management_implementation.md  # 平台管理实施方案
│   ├── plugin_system_design.md           # 插件系统设计
│   └── v3_enhancement_plan.md             # 功能增强计划
└── README.md                 # 项目说明
```

## 🌿 开发分支

### **分支1: enhancement-branch (增强分支)**
**目标**: 基于 `docs/quick_implementation_guide.md` 进行功能增强
**包含功能**:
- **Phase 1**: IndexedDB壁纸存储 + Material You主题 + 快捷方式预设扩展
- **Phase 2**: 拖拽排序功能 + 动画效果增强
- **Phase 3**: 双模式搜索系统 (常规搜索 + 快捷搜索)

**开发周期**: 4-6周
**风险等级**: 低 (基于现有功能增强)

### **分支2: platform-management-branch (平台管理分支)**
**目标**: 基于 `docs/platform_management_implementation.md` 实现自定义搜索平台管理
**包含功能**:
- **URLParser**: 智能URL解析引擎，支持20+种搜索参数
- **PlatformManager**: 完整的平台CRUD管理系统
- **设置面板集成**: 用户友好的平台管理界面
- **别名系统**: 自动更新搜索别名映射

**开发周期**: 3-4周
**风险等级**: 中 (新功能开发)

## 🛠️ 开发指南

### **环境要求**
- Chrome 88+ 或 Edge 88+
- 支持ES6+的现代浏览器
- 开发工具：VS Code (推荐)

### **本地开发**
1. **克隆仓库**
   ```bash
   git clone https://github.com/pawaovo/moment-search.git
   cd moment-search
   ```

2. **切换到开发分支**
   ```bash
   # 增强分支
   git checkout enhancement-branch
   
   # 或平台管理分支
   git checkout platform-management-branch
   ```

3. **加载扩展**
   - 打开Chrome扩展管理页面 `chrome://extensions/`
   - 启用"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `moment-search-v3` 目录

### **开发流程**
1. **选择分支** - 根据要开发的功能选择对应分支
2. **查看文档** - 阅读对应的实施文档
3. **按阶段开发** - 按照文档中的Phase进行开发
4. **测试验证** - 使用文档中的检查清单进行测试
5. **提交代码** - 提交到对应分支

## 📋 功能特性

### **当前功能 (V3.0.0)**
- ✅ 多平台搜索 (9个内置平台)
- ✅ 智能别名匹配
- ✅ 快捷方式管理 (8页面 x 20个)
- ✅ 时间显示系统
- ✅ 基础主题切换
- ✅ 设置面板
- ✅ 数据导入导出

### **计划功能 (分支1)**
- 🔄 IndexedDB壁纸存储
- 🔄 Material You主题系统
- 🔄 30个快捷方式预设
- 🔄 拖拽排序功能
- 🔄 双模式搜索系统
- 🔄 高级动画效果

### **计划功能 (分支2)**
- 🔄 智能URL解析引擎
- 🔄 自定义搜索平台管理
- 🔄 平台CRUD操作
- 🔄 别名系统增强
- 🔄 设置面板集成

## 📚 文档说明

| 文档 | 用途 | 目标分支 |
|------|------|----------|
| `quick_implementation_guide.md` | 快速实施指南 | enhancement-branch |
| `platform_management_implementation.md` | 平台管理实施方案 | platform-management-branch |
| `plugin_system_design.md` | 插件系统设计 | 未来版本 |
| `v3_enhancement_plan.md` | 功能增强计划 | 两个分支通用 |

## 🎯 开发优先级

### **立即开始 (本周)**
- **分支1**: Phase 1 功能实施 (IndexedDB + Material You + 预设扩展)
- **分支2**: URLParser核心引擎开发

### **短期目标 (2-4周)**
- **分支1**: 完成Phase 1-2，开始Phase 3
- **分支2**: 完成PlatformManager和UI集成

### **长期目标 (1-2个月)**
- 两个分支功能合并
- 插件系统开发
- 性能优化和用户体验提升

## 🤝 贡献指南

1. **选择分支** - 根据要贡献的功能选择对应分支
2. **阅读文档** - 仔细阅读对应的实施文档
3. **遵循规范** - 保持代码风格一致
4. **测试充分** - 确保功能正常工作
5. **文档更新** - 更新相关文档

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🔗 相关链接

- **GitHub仓库**: https://github.com/pawaovo/moment-search
- **问题反馈**: https://github.com/pawaovo/moment-search/issues
- **功能建议**: https://github.com/pawaovo/moment-search/discussions

---

**项目版本**: V3.0.0  
**最后更新**: 2025-01-19  
**维护团队**: Moment Search Team
