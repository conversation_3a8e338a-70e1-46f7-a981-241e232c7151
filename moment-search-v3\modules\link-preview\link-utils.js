/**
 * 链接预览工具类
 * 提供共享的工具方法，避免代码重复
 */
class LinkUtils {
    /**
     * 检查链接是否有效
     */
    static isValidLink(link) {
        const href = link.href;
        
        // 排除无效链接
        if (!href || 
            href.startsWith('javascript:') || 
            href.startsWith('mailto:') || 
            href.startsWith('tel:') ||
            href.startsWith('#') ||
            href === window.location.href) {
            return false;
        }

        // 排除下载链接
        const downloadExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar', '.exe'];
        const url = href.toLowerCase();
        if (downloadExtensions.some(ext => url.includes(ext))) {
            return false;
        }

        return true;
    }

    /**
     * 检查URL是否有效
     */
    static isValidUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
        } catch {
            return false;
        }
    }

    /**
     * 标准化URL
     */
    static normalizeUrl(url) {
        try {
            const urlObj = new URL(url);
            // 移除查询参数和片段，只保留主要部分
            return `${urlObj.protocol}//${urlObj.hostname}${urlObj.pathname}`;
        } catch {
            return url;
        }
    }

    /**
     * 提取域名
     */
    static extractDomain(url) {
        try {
            return new URL(url).hostname;
        } catch {
            return 'unknown';
        }
    }

    /**
     * 获取显示URL
     */
    static getDisplayUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
        } catch {
            return url.length > 50 ? url.substring(0, 50) + '...' : url;
        }
    }

    /**
     * 防抖函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 生成唯一ID
     */
    static generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 深度合并对象
     */
    static deepMerge(target, source) {
        const result = JSON.parse(JSON.stringify(target));
        
        function merge(target, source) {
            for (const key in source) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    if (!target[key]) target[key] = {};
                    merge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
        }
        
        merge(result, source);
        return result;
    }

    /**
     * 检查位置是否在视口内
     */
    static isPositionInViewport(left, top, width, height) {
        return left >= 0 && 
               top >= 0 && 
               left + width <= window.innerWidth && 
               top + height <= window.innerHeight;
    }

    /**
     * 限制数值在指定范围内
     */
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }

    /**
     * 格式化文件大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 格式化时间差
     */
    static formatTimeDiff(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        
        const minute = 60 * 1000;
        const hour = 60 * minute;
        const day = 24 * hour;
        const week = 7 * day;
        
        if (diff < minute) return '刚刚';
        if (diff < hour) return `${Math.floor(diff / minute)}分钟前`;
        if (diff < day) return `${Math.floor(diff / hour)}小时前`;
        if (diff < week) return `${Math.floor(diff / day)}天前`;
        return `${Math.floor(diff / week)}周前`;
    }

    /**
     * 复制文本到剪贴板
     */
    static async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (error) {
            // 降级方案
            try {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                return true;
            } catch {
                return false;
            }
        }
    }

    /**
     * 显示提示消息
     */
    static showToast(message, duration = 2000, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `link-preview-toast toast-${type}`;
        toast.textContent = message;
        
        const colors = {
            info: 'rgba(0, 123, 255, 0.9)',
            success: 'rgba(40, 167, 69, 0.9)',
            warning: 'rgba(255, 193, 7, 0.9)',
            error: 'rgba(220, 53, 69, 0.9)'
        };
        
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 99999;
            font-size: 14px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        document.body.appendChild(toast);
        
        // 显示动画
        requestAnimationFrame(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        });
        
        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
        
        return toast;
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.LinkUtils = LinkUtils;
}
