/**
 * 阅读模式增强器
 * 提供智能内容提取、可读性优化、主题设置等功能
 */
class ReadingModeEnhancer {
    constructor(previewWindow) {
        this.previewWindow = previewWindow;
        this.isEnabled = false;
        this.currentTheme = 'light';
        this.fontSize = 16;
        this.lineHeight = 1.6;
        this.maxWidth = 800;
        this.fontFamily = 'system';
        
        // 内容提取配置
        this.extractionConfig = {
            selectors: {
                title: 'h1, .title, .article-title, [class*="title"]',
                content: 'article, .content, .article-content, .post-content, main, [class*="content"]',
                author: '.author, .byline, [class*="author"]',
                date: '.date, .publish-date, time, [class*="date"]',
                tags: '.tags, .categories, [class*="tag"]'
            },
            removeSelectors: [
                'script', 'style', 'nav', 'header', 'footer', 
                '.advertisement', '.ads', '.sidebar', '.comments',
                '[class*="ad"]', '[id*="ad"]', '.social-share'
            ]
        };
        
        // 主题配置
        this.themes = {
            light: {
                background: '#ffffff',
                text: '#333333',
                accent: '#007bff',
                border: '#e0e0e0',
                shadow: 'rgba(0, 0, 0, 0.1)'
            },
            dark: {
                background: '#1a1a1a',
                text: '#e0e0e0',
                accent: '#4dabf7',
                border: '#404040',
                shadow: 'rgba(0, 0, 0, 0.3)'
            },
            sepia: {
                background: '#f4f1ea',
                text: '#5c4b37',
                accent: '#8b4513',
                border: '#d4c5a9',
                shadow: 'rgba(92, 75, 55, 0.1)'
            },
            highContrast: {
                background: '#000000',
                text: '#ffffff',
                accent: '#ffff00',
                border: '#ffffff',
                shadow: 'rgba(255, 255, 255, 0.2)'
            }
        };
        
        this.init();
    }

    /**
     * 初始化阅读模式
     */
    init() {
        this.createReadingModeButton();
        this.loadSettings();
        console.log('✅ 阅读模式增强器初始化完成');
    }

    /**
     * 创建阅读模式按钮
     */
    createReadingModeButton() {
        const button = document.createElement('button');
        button.className = 'reading-mode-toggle';
        button.innerHTML = '📖';
        button.title = '阅读模式';
        button.style.cssText = `
            position: absolute;
            top: 10px;
            right: 80px;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: rgba(0, 0, 0, 0.1);
            color: #666;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            z-index: 1000;
        `;
        
        button.addEventListener('mouseenter', () => {
            button.style.background = 'rgba(0, 0, 0, 0.2)';
            button.style.color = '#333';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.background = 'rgba(0, 0, 0, 0.1)';
            button.style.color = '#666';
        });
        
        button.addEventListener('click', () => {
            this.toggleReadingMode();
        });
        
        this.previewWindow.element.querySelector('.preview-header').appendChild(button);
        this.readingModeButton = button;
    }

    /**
     * 切换阅读模式
     */
    toggleReadingMode() {
        if (this.isEnabled) {
            this.disableReadingMode();
        } else {
            this.enableReadingMode();
        }
    }

    /**
     * 启用阅读模式
     */
    async enableReadingMode() {
        try {
            this.isEnabled = true;
            this.readingModeButton.innerHTML = '📄';
            this.readingModeButton.style.background = 'rgba(0, 123, 255, 0.2)';
            this.readingModeButton.style.color = '#007bff';
            
            // 提取和处理内容
            const extractedContent = await this.extractContent();
            if (extractedContent) {
                this.currentContent = extractedContent;
                this.renderReadingMode(extractedContent);
                this.createReadingControls();
                LinkUtils.showToast('阅读模式已启用', 2000, 'success');
            } else {
                this.disableReadingMode();
                LinkUtils.showToast('无法提取页面内容', 2000, 'warning');
            }
        } catch (error) {
            console.error('启用阅读模式失败:', error);
            this.disableReadingMode();
            LinkUtils.showToast('阅读模式启用失败', 2000, 'error');
        }
    }

    /**
     * 禁用阅读模式
     */
    disableReadingMode() {
        this.isEnabled = false;
        this.readingModeButton.innerHTML = '📖';
        this.readingModeButton.style.background = 'rgba(0, 0, 0, 0.1)';
        this.readingModeButton.style.color = '#666';
        
        // 恢复原始内容
        this.restoreOriginalContent();
        this.removeReadingControls();
        
        LinkUtils.showToast('阅读模式已禁用', 2000, 'info');
    }

    /**
     * 提取页面内容
     */
    async extractContent() {
        const iframe = this.previewWindow.element.querySelector('iframe');
        if (!iframe || !iframe.contentDocument) {
            return null;
        }

        const doc = iframe.contentDocument;
        const content = {
            title: '',
            author: '',
            date: '',
            content: '',
            tags: []
        };

        try {
            // 提取标题
            const titleElement = doc.querySelector(this.extractionConfig.selectors.title);
            content.title = titleElement ? titleElement.textContent.trim() : doc.title || '无标题';

            // 提取作者
            const authorElement = doc.querySelector(this.extractionConfig.selectors.author);
            content.author = authorElement ? authorElement.textContent.trim() : '';

            // 提取日期
            const dateElement = doc.querySelector(this.extractionConfig.selectors.date);
            content.date = dateElement ? this.formatDate(dateElement) : '';

            // 提取主要内容
            const contentElement = doc.querySelector(this.extractionConfig.selectors.content);
            if (contentElement) {
                // 克隆内容元素
                const clonedContent = contentElement.cloneNode(true);
                
                // 移除不需要的元素
                this.extractionConfig.removeSelectors.forEach(selector => {
                    const elements = clonedContent.querySelectorAll(selector);
                    elements.forEach(el => el.remove());
                });
                
                // 清理和优化内容
                this.cleanContent(clonedContent);
                content.content = clonedContent.innerHTML;
            }

            // 提取标签
            const tagElements = doc.querySelectorAll(this.extractionConfig.selectors.tags);
            tagElements.forEach(tagEl => {
                const tags = tagEl.textContent.split(/[,，\s]+/).filter(tag => tag.trim());
                content.tags.push(...tags);
            });

            return content;
        } catch (error) {
            console.error('内容提取失败:', error);
            return null;
        }
    }

    /**
     * 清理内容
     */
    cleanContent(element) {
        // 移除空元素
        const emptyElements = element.querySelectorAll('*:empty:not(img):not(br):not(hr)');
        emptyElements.forEach(el => el.remove());

        // 优化图片
        const images = element.querySelectorAll('img');
        images.forEach(img => {
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
            img.style.display = 'block';
            img.style.margin = '1em auto';
        });

        // 优化链接
        const links = element.querySelectorAll('a');
        links.forEach(link => {
            link.style.color = 'inherit';
            link.style.textDecoration = 'underline';
        });

        // 优化代码块
        const codeBlocks = element.querySelectorAll('pre, code');
        codeBlocks.forEach(code => {
            code.style.background = 'rgba(0, 0, 0, 0.05)';
            code.style.padding = '0.5em';
            code.style.borderRadius = '4px';
            code.style.fontFamily = 'monospace';
        });
    }

    /**
     * 渲染阅读模式
     */
    renderReadingMode(content) {
        const iframe = this.previewWindow.element.querySelector('iframe');
        if (!iframe) return;

        // 保存原始内容
        this.originalContent = iframe.srcdoc || iframe.src;

        // 创建阅读模式HTML
        const readingHTML = this.createReadingHTML(content);
        
        // 应用到iframe
        iframe.srcdoc = readingHTML;
    }

    /**
     * 创建阅读模式HTML
     */
    createReadingHTML(content) {
        const theme = this.themes[this.currentTheme];
        const fontFamilies = {
            system: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            serif: 'Georgia, "Times New Roman", Times, serif',
            sansSerif: '"Helvetica Neue", Helvetica, Arial, sans-serif',
            mono: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, monospace'
        };

        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${content.title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: ${fontFamilies[this.fontFamily]};
            font-size: ${this.fontSize}px;
            line-height: ${this.lineHeight};
            color: ${theme.text};
            background: ${theme.background};
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .reading-container {
            max-width: ${this.maxWidth}px;
            margin: 0 auto;
            background: ${theme.background};
            border-radius: 8px;
            overflow: hidden;
        }
        
        .article-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid ${theme.border};
        }
        
        .article-title {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: ${theme.text};
        }
        
        .article-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.9em;
            color: ${theme.text};
            opacity: 0.7;
        }
        
        .article-content {
            font-size: 1em;
            line-height: ${this.lineHeight};
        }
        
        .article-content h1,
        .article-content h2,
        .article-content h3,
        .article-content h4,
        .article-content h5,
        .article-content h6 {
            margin: 1.5em 0 0.5em 0;
            color: ${theme.text};
        }
        
        .article-content p {
            margin: 1em 0;
        }
        
        .article-content a {
            color: ${theme.accent};
            text-decoration: underline;
        }
        
        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 1em auto;
            border-radius: 4px;
        }
        
        .article-content blockquote {
            margin: 1em 0;
            padding: 1em;
            border-left: 4px solid ${theme.accent};
            background: rgba(0, 0, 0, 0.05);
            border-radius: 0 4px 4px 0;
        }
        
        .article-content pre,
        .article-content code {
            background: rgba(0, 0, 0, 0.05);
            padding: 0.2em 0.4em;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .article-content pre {
            padding: 1em;
            overflow-x: auto;
        }
        
        .article-tags {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid ${theme.border};
        }
        
        .tag {
            display: inline-block;
            background: ${theme.accent};
            color: white;
            padding: 0.2em 0.5em;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 0.2em;
        }
    </style>
</head>
<body>
    <div class="reading-container">
        <header class="article-header">
            <h1 class="article-title">${content.title}</h1>
            <div class="article-meta">
                ${content.author ? `<span>作者: ${content.author}</span>` : ''}
                ${content.date ? `<span>日期: ${content.date}</span>` : ''}
            </div>
        </header>
        
        <main class="article-content">
            ${content.content}
        </main>
        
        ${content.tags.length > 0 ? `
        <footer class="article-tags">
            ${content.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
        </footer>
        ` : ''}
    </div>
</body>
</html>`;
    }

    /**
     * 创建阅读控制面板
     */
    createReadingControls() {
        const controls = document.createElement('div');
        controls.className = 'reading-controls';
        controls.innerHTML = `
            <div class="reading-controls-content">
                <div class="control-group">
                    <label>主题:</label>
                    <select class="theme-selector">
                        <option value="light">明亮</option>
                        <option value="dark">深色</option>
                        <option value="sepia">护眼</option>
                        <option value="highContrast">高对比度</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>字体:</label>
                    <select class="font-selector">
                        <option value="system">系统字体</option>
                        <option value="serif">衬线字体</option>
                        <option value="sansSerif">无衬线字体</option>
                        <option value="mono">等宽字体</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>字号:</label>
                    <input type="range" class="font-size-slider" min="12" max="24" value="${this.fontSize}">
                    <span class="font-size-value">${this.fontSize}px</span>
                </div>
                <div class="control-group">
                    <label>行距:</label>
                    <input type="range" class="line-height-slider" min="1.2" max="2.0" step="0.1" value="${this.lineHeight}">
                    <span class="line-height-value">${this.lineHeight}</span>
                </div>
                <div class="control-group">
                    <label>宽度:</label>
                    <input type="range" class="width-slider" min="600" max="1200" step="50" value="${this.maxWidth}">
                    <span class="width-value">${this.maxWidth}px</span>
                </div>
            </div>
        `;

        controls.style.cssText = `
            position: absolute;
            top: 50px;
            right: 10px;
            width: 250px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            font-size: 14px;
            display: none;
        `;

        this.previewWindow.element.appendChild(controls);
        this.readingControls = controls;

        this.bindControlEvents();
        this.updateControlValues();
    }

    /**
     * 绑定控制面板事件
     */
    bindControlEvents() {
        const controls = this.readingControls;

        // 主题选择
        const themeSelector = controls.querySelector('.theme-selector');
        themeSelector.value = this.currentTheme;
        themeSelector.addEventListener('change', (e) => {
            this.currentTheme = e.target.value;
            this.updateReadingMode();
        });

        // 字体选择
        const fontSelector = controls.querySelector('.font-selector');
        fontSelector.value = this.fontFamily;
        fontSelector.addEventListener('change', (e) => {
            this.fontFamily = e.target.value;
            this.updateReadingMode();
        });

        // 字号调整
        const fontSizeSlider = controls.querySelector('.font-size-slider');
        const fontSizeValue = controls.querySelector('.font-size-value');
        fontSizeSlider.addEventListener('input', (e) => {
            this.fontSize = parseInt(e.target.value);
            fontSizeValue.textContent = `${this.fontSize}px`;
            this.updateReadingMode();
        });

        // 行距调整
        const lineHeightSlider = controls.querySelector('.line-height-slider');
        const lineHeightValue = controls.querySelector('.line-height-value');
        lineHeightSlider.addEventListener('input', (e) => {
            this.lineHeight = parseFloat(e.target.value);
            lineHeightValue.textContent = this.lineHeight;
            this.updateReadingMode();
        });

        // 宽度调整
        const widthSlider = controls.querySelector('.width-slider');
        const widthValue = controls.querySelector('.width-value');
        widthSlider.addEventListener('input', (e) => {
            this.maxWidth = parseInt(e.target.value);
            widthValue.textContent = `${this.maxWidth}px`;
            this.updateReadingMode();
        });

        // 点击阅读模式按钮时切换控制面板
        this.readingModeButton.addEventListener('click', () => {
            if (this.isEnabled) {
                const isVisible = controls.style.display !== 'none';
                controls.style.display = isVisible ? 'none' : 'block';
            }
        });
    }

    /**
     * 更新控制面板值
     */
    updateControlValues() {
        if (!this.readingControls) return;

        const controls = this.readingControls;
        controls.querySelector('.theme-selector').value = this.currentTheme;
        controls.querySelector('.font-selector').value = this.fontFamily;
        controls.querySelector('.font-size-slider').value = this.fontSize;
        controls.querySelector('.font-size-value').textContent = `${this.fontSize}px`;
        controls.querySelector('.line-height-slider').value = this.lineHeight;
        controls.querySelector('.line-height-value').textContent = this.lineHeight;
        controls.querySelector('.width-slider').value = this.maxWidth;
        controls.querySelector('.width-value').textContent = `${this.maxWidth}px`;
    }

    /**
     * 更新阅读模式
     */
    updateReadingMode() {
        if (!this.isEnabled || !this.currentContent) return;

        const readingHTML = this.createReadingHTML(this.currentContent);
        const iframe = this.previewWindow.element.querySelector('iframe');
        if (iframe) {
            iframe.srcdoc = readingHTML;
        }

        this.saveSettings();
    }

    /**
     * 移除阅读控制面板
     */
    removeReadingControls() {
        if (this.readingControls) {
            this.readingControls.remove();
            this.readingControls = null;
        }
    }

    /**
     * 恢复原始内容
     */
    restoreOriginalContent() {
        if (!this.originalContent) return;

        const iframe = this.previewWindow.element.querySelector('iframe');
        if (iframe) {
            if (this.originalContent.startsWith('http')) {
                iframe.src = this.originalContent;
                iframe.srcdoc = '';
            } else {
                iframe.srcdoc = this.originalContent;
            }
        }
    }

    /**
     * 格式化日期
     */
    formatDate(dateElement) {
        const dateText = dateElement.textContent || dateElement.getAttribute('datetime') || '';
        try {
            const date = new Date(dateText);
            if (!isNaN(date.getTime())) {
                return date.toLocaleDateString('zh-CN');
            }
        } catch (error) {
            // 忽略日期解析错误
        }
        return dateText.trim();
    }

    /**
     * 保存设置
     */
    saveSettings() {
        const settings = {
            theme: this.currentTheme,
            fontSize: this.fontSize,
            lineHeight: this.lineHeight,
            maxWidth: this.maxWidth,
            fontFamily: this.fontFamily
        };

        localStorage.setItem('linkPreview_readingMode', JSON.stringify(settings));
    }

    /**
     * 加载设置
     */
    loadSettings() {
        try {
            const saved = localStorage.getItem('linkPreview_readingMode');
            if (saved) {
                const settings = JSON.parse(saved);
                this.currentTheme = settings.theme || 'light';
                this.fontSize = settings.fontSize || 16;
                this.lineHeight = settings.lineHeight || 1.6;
                this.maxWidth = settings.maxWidth || 800;
                this.fontFamily = settings.fontFamily || 'system';
            }
        } catch (error) {
            console.error('加载阅读模式设置失败:', error);
        }
    }

    /**
     * 获取阅读模式状态
     */
    getStatus() {
        return {
            enabled: this.isEnabled,
            theme: this.currentTheme,
            fontSize: this.fontSize,
            lineHeight: this.lineHeight,
            maxWidth: this.maxWidth,
            fontFamily: this.fontFamily
        };
    }

    /**
     * 销毁阅读模式
     */
    destroy() {
        this.disableReadingMode();
        if (this.readingModeButton) {
            this.readingModeButton.remove();
        }
        console.log('💥 阅读模式增强器已销毁');
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.ReadingModeEnhancer = ReadingModeEnhancer;
}
