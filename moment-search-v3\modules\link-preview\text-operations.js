/**
 * 文本操作处理器
 * 提供拖拽文本搜索、翻译等功能
 */
class TextOperationsHandler {
    constructor(linkPreviewManager) {
        this.linkPreviewManager = linkPreviewManager;
        this.isEnabled = true;
        
        // 拖拽状态
        this.dragState = {
            isDragging: false,
            selectedText: '',
            startX: 0,
            startY: 0,
            dropZone: null
        };
        
        // 操作配置
        this.config = {
            searchEngines: {
                google: 'https://www.google.com/search?q=',
                baidu: 'https://www.baidu.com/s?wd=',
                bing: 'https://www.bing.com/search?q=',
                duckduckgo: 'https://duckduckgo.com/?q='
            },
            translationServices: {
                google: 'https://translate.google.com/?sl=auto&tl=zh&text=',
                baidu: 'https://fanyi.baidu.com/#auto/zh/',
                youdao: 'https://fanyi.youdao.com/translate?&doctype=json&type=AUTO&i='
            },
            defaultSearchEngine: 'google',
            defaultTranslationService: 'google',
            enableQuickActions: true,
            enableContextMenu: true
        };
        
        this.init();
    }

    /**
     * 初始化文本操作处理器
     */
    init() {
        this.bindGlobalEvents();
        this.createDropZones();
        this.loadSettings();
        console.log('✅ 文本操作处理器初始化完成');
    }

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 监听文本选择
        document.addEventListener('mouseup', (e) => {
            this.handleTextSelection(e);
        });
        
        // 监听拖拽开始
        document.addEventListener('dragstart', (e) => {
            this.handleDragStart(e);
        });
        
        // 监听拖拽结束
        document.addEventListener('dragend', (e) => {
            this.handleDragEnd(e);
        });
        
        // 监听右键菜单
        if (this.config.enableContextMenu) {
            document.addEventListener('contextmenu', (e) => {
                this.handleContextMenu(e);
            });
        }
    }

    /**
     * 处理文本选择
     */
    handleTextSelection(e) {
        const selection = window.getSelection();
        const selectedText = selection.toString().trim();
        
        if (selectedText && selectedText.length > 0) {
            this.selectedText = selectedText;
            
            if (this.config.enableQuickActions) {
                this.showQuickActions(e, selectedText);
            }
        } else {
            this.hideQuickActions();
        }
    }

    /**
     * 显示快速操作菜单
     */
    showQuickActions(event, text) {
        this.hideQuickActions();
        
        const quickActions = document.createElement('div');
        quickActions.className = 'text-quick-actions';
        quickActions.innerHTML = `
            <div class="quick-action" data-action="search">
                <span class="action-icon">🔍</span>
                <span class="action-text">搜索</span>
            </div>
            <div class="quick-action" data-action="translate">
                <span class="action-icon">🌐</span>
                <span class="action-text">翻译</span>
            </div>
            <div class="quick-action" data-action="copy">
                <span class="action-icon">📋</span>
                <span class="action-text">复制</span>
            </div>
        `;
        
        quickActions.style.cssText = `
            position: fixed;
            left: ${event.clientX}px;
            top: ${event.clientY - 50}px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            z-index: 99999;
            display: flex;
            overflow: hidden;
            font-size: 14px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        // 绑定快速操作事件
        quickActions.addEventListener('click', (e) => {
            e.stopPropagation();
            const action = e.target.closest('.quick-action')?.dataset.action;
            
            switch (action) {
                case 'search':
                    this.searchText(text);
                    break;
                case 'translate':
                    this.translateText(text);
                    break;
                case 'copy':
                    this.copyText(text);
                    break;
            }
            
            this.hideQuickActions();
        });
        
        document.body.appendChild(quickActions);
        this.quickActionsElement = quickActions;
        
        // 调整位置确保在视口内
        this.adjustQuickActionsPosition();
        
        // 自动隐藏
        setTimeout(() => {
            this.hideQuickActions();
        }, 5000);
    }

    /**
     * 隐藏快速操作菜单
     */
    hideQuickActions() {
        if (this.quickActionsElement) {
            this.quickActionsElement.remove();
            this.quickActionsElement = null;
        }
    }

    /**
     * 调整快速操作菜单位置
     */
    adjustQuickActionsPosition() {
        if (!this.quickActionsElement) return;
        
        const rect = this.quickActionsElement.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        let left = parseInt(this.quickActionsElement.style.left);
        let top = parseInt(this.quickActionsElement.style.top);
        
        // 调整水平位置
        if (left + rect.width > viewportWidth) {
            left = viewportWidth - rect.width - 10;
        }
        if (left < 10) left = 10;
        
        // 调整垂直位置
        if (top + rect.height > viewportHeight) {
            top = viewportHeight - rect.height - 10;
        }
        if (top < 10) top = 10;
        
        this.quickActionsElement.style.left = `${left}px`;
        this.quickActionsElement.style.top = `${top}px`;
    }

    /**
     * 处理拖拽开始
     */
    handleDragStart(e) {
        const selectedText = window.getSelection().toString().trim();
        if (selectedText) {
            this.dragState.isDragging = true;
            this.dragState.selectedText = selectedText;
            this.dragState.startX = e.clientX;
            this.dragState.startY = e.clientY;
            
            // 设置拖拽数据
            e.dataTransfer.setData('text/plain', selectedText);
            e.dataTransfer.effectAllowed = 'copy';
            
            // 显示拖拽区域
            this.showDropZones();
        }
    }

    /**
     * 处理拖拽结束
     */
    handleDragEnd(e) {
        this.dragState.isDragging = false;
        this.dragState.selectedText = '';
        this.hideDropZones();
    }

    /**
     * 创建拖拽区域
     */
    createDropZones() {
        // 搜索拖拽区域
        this.searchDropZone = this.createDropZone('search', '🔍', '拖拽文本到此处搜索');
        
        // 翻译拖拽区域
        this.translateDropZone = this.createDropZone('translate', '🌐', '拖拽文本到此处翻译');
        
        document.body.appendChild(this.searchDropZone);
        document.body.appendChild(this.translateDropZone);
    }

    /**
     * 创建单个拖拽区域
     */
    createDropZone(type, icon, text) {
        const dropZone = document.createElement('div');
        dropZone.className = `text-drop-zone text-drop-zone-${type}`;
        dropZone.innerHTML = `
            <div class="drop-zone-content">
                <div class="drop-zone-icon">${icon}</div>
                <div class="drop-zone-text">${text}</div>
            </div>
        `;
        
        const positions = {
            search: { bottom: '100px', left: '50%', transform: 'translateX(-50%)' },
            translate: { bottom: '100px', right: '100px' }
        };
        
        dropZone.style.cssText = `
            position: fixed;
            ${positions[type].bottom ? `bottom: ${positions[type].bottom};` : ''}
            ${positions[type].left ? `left: ${positions[type].left};` : ''}
            ${positions[type].right ? `right: ${positions[type].right};` : ''}
            ${positions[type].transform ? `transform: ${positions[type].transform};` : ''}
            width: 200px;
            height: 80px;
            background: rgba(0, 123, 255, 0.1);
            border: 2px dashed #007bff;
            border-radius: 10px;
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 99998;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.3s ease;
        `;
        
        // 绑定拖拽事件
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.style.background = 'rgba(0, 123, 255, 0.2)';
            dropZone.style.borderColor = '#0056b3';
        });
        
        dropZone.addEventListener('dragleave', () => {
            dropZone.style.background = 'rgba(0, 123, 255, 0.1)';
            dropZone.style.borderColor = '#007bff';
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            const text = e.dataTransfer.getData('text/plain');
            
            if (text) {
                if (type === 'search') {
                    this.searchText(text);
                } else if (type === 'translate') {
                    this.translateText(text);
                }
            }
            
            this.hideDropZones();
        });
        
        return dropZone;
    }

    /**
     * 显示拖拽区域
     */
    showDropZones() {
        if (this.searchDropZone) {
            this.searchDropZone.style.display = 'flex';
        }
        if (this.translateDropZone) {
            this.translateDropZone.style.display = 'flex';
        }
    }

    /**
     * 隐藏拖拽区域
     */
    hideDropZones() {
        if (this.searchDropZone) {
            this.searchDropZone.style.display = 'none';
            this.searchDropZone.style.background = 'rgba(0, 123, 255, 0.1)';
            this.searchDropZone.style.borderColor = '#007bff';
        }
        if (this.translateDropZone) {
            this.translateDropZone.style.display = 'none';
            this.translateDropZone.style.background = 'rgba(0, 123, 255, 0.1)';
            this.translateDropZone.style.borderColor = '#007bff';
        }
    }

    /**
     * 搜索文本
     */
    searchText(text) {
        const searchEngine = this.config.searchEngines[this.config.defaultSearchEngine];
        const searchUrl = searchEngine + encodeURIComponent(text);

        // 使用链接预览功能打开搜索结果
        this.linkPreviewManager.createPreview(searchUrl, {
            triggerType: 'textSearch',
            searchText: text
        });

        LinkUtils.showToast(`正在搜索: ${text}`, 2000, 'info');
    }

    /**
     * 翻译文本
     */
    translateText(text) {
        const translationService = this.config.translationServices[this.config.defaultTranslationService];
        const translateUrl = translationService + encodeURIComponent(text);

        // 使用链接预览功能打开翻译结果
        this.linkPreviewManager.createPreview(translateUrl, {
            triggerType: 'textTranslation',
            originalText: text
        });

        LinkUtils.showToast(`正在翻译: ${text}`, 2000, 'info');
    }

    /**
     * 复制文本
     */
    async copyText(text) {
        const success = await LinkUtils.copyToClipboard(text);
        if (success) {
            LinkUtils.showToast('文本已复制到剪贴板', 2000, 'success');
        } else {
            LinkUtils.showToast('复制失败', 2000, 'error');
        }
    }

    /**
     * 处理右键菜单
     */
    handleContextMenu(e) {
        const selectedText = window.getSelection().toString().trim();
        if (!selectedText) return;

        // 检查是否在预览窗口内
        const previewWindow = e.target.closest('.link-preview-window');
        if (!previewWindow) return;

        e.preventDefault();
        this.showContextMenu(e, selectedText);
    }

    /**
     * 显示右键菜单
     */
    showContextMenu(event, text) {
        this.hideContextMenu();

        const contextMenu = document.createElement('div');
        contextMenu.className = 'text-context-menu';
        contextMenu.innerHTML = `
            <div class="context-menu-item" data-action="search">
                <span class="menu-icon">🔍</span>
                <span class="menu-text">搜索 "${text.length > 20 ? text.substring(0, 20) + '...' : text}"</span>
            </div>
            <div class="context-menu-item" data-action="translate">
                <span class="menu-icon">🌐</span>
                <span class="menu-text">翻译 "${text.length > 20 ? text.substring(0, 20) + '...' : text}"</span>
            </div>
            <div class="context-menu-separator"></div>
            <div class="context-menu-item" data-action="copy">
                <span class="menu-icon">📋</span>
                <span class="menu-text">复制文本</span>
            </div>
            <div class="context-menu-item" data-action="search-engine">
                <span class="menu-icon">⚙️</span>
                <span class="menu-text">搜索引擎设置</span>
            </div>
        `;

        contextMenu.style.cssText = `
            position: fixed;
            left: ${event.clientX}px;
            top: ${event.clientY}px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            z-index: 99999;
            min-width: 200px;
            font-size: 14px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 4px 0;
        `;

        // 绑定菜单事件
        contextMenu.addEventListener('click', (e) => {
            e.stopPropagation();
            const action = e.target.closest('.context-menu-item')?.dataset.action;

            switch (action) {
                case 'search':
                    this.searchText(text);
                    break;
                case 'translate':
                    this.translateText(text);
                    break;
                case 'copy':
                    this.copyText(text);
                    break;
                case 'search-engine':
                    this.showSearchEngineSettings();
                    break;
            }

            this.hideContextMenu();
        });

        document.body.appendChild(contextMenu);
        this.contextMenuElement = contextMenu;

        // 调整菜单位置
        this.adjustContextMenuPosition();

        // 点击其他地方时隐藏菜单
        setTimeout(() => {
            document.addEventListener('click', this.hideContextMenu.bind(this), { once: true });
        }, 0);
    }

    /**
     * 隐藏右键菜单
     */
    hideContextMenu() {
        if (this.contextMenuElement) {
            this.contextMenuElement.remove();
            this.contextMenuElement = null;
        }
    }

    /**
     * 调整右键菜单位置
     */
    adjustContextMenuPosition() {
        if (!this.contextMenuElement) return;

        const rect = this.contextMenuElement.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let left = parseInt(this.contextMenuElement.style.left);
        let top = parseInt(this.contextMenuElement.style.top);

        // 调整水平位置
        if (left + rect.width > viewportWidth) {
            left = viewportWidth - rect.width - 10;
        }

        // 调整垂直位置
        if (top + rect.height > viewportHeight) {
            top = viewportHeight - rect.height - 10;
        }

        this.contextMenuElement.style.left = `${left}px`;
        this.contextMenuElement.style.top = `${top}px`;
    }

    /**
     * 显示搜索引擎设置
     */
    showSearchEngineSettings() {
        // 这里将在后面的配置弹窗中实现
        LinkUtils.showToast('搜索引擎设置功能即将推出', 2000, 'info');
    }

    /**
     * 设置默认搜索引擎
     */
    setDefaultSearchEngine(engine) {
        if (this.config.searchEngines[engine]) {
            this.config.defaultSearchEngine = engine;
            this.saveSettings();
            LinkUtils.showToast(`默认搜索引擎已设置为: ${engine}`, 2000, 'success');
        }
    }

    /**
     * 设置默认翻译服务
     */
    setDefaultTranslationService(service) {
        if (this.config.translationServices[service]) {
            this.config.defaultTranslationService = service;
            this.saveSettings();
            LinkUtils.showToast(`默认翻译服务已设置为: ${service}`, 2000, 'success');
        }
    }

    /**
     * 启用/禁用功能
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        if (!enabled) {
            this.hideQuickActions();
            this.hideContextMenu();
            this.hideDropZones();
        }
        this.saveSettings();
    }

    /**
     * 启用/禁用快速操作
     */
    setQuickActionsEnabled(enabled) {
        this.config.enableQuickActions = enabled;
        if (!enabled) {
            this.hideQuickActions();
        }
        this.saveSettings();
    }

    /**
     * 启用/禁用右键菜单
     */
    setContextMenuEnabled(enabled) {
        this.config.enableContextMenu = enabled;
        if (!enabled) {
            this.hideContextMenu();
        }
        this.saveSettings();
    }

    /**
     * 保存设置
     */
    saveSettings() {
        const settings = {
            config: this.config,
            isEnabled: this.isEnabled
        };

        localStorage.setItem('linkPreview_textOperations', JSON.stringify(settings));
    }

    /**
     * 加载设置
     */
    loadSettings() {
        try {
            const saved = localStorage.getItem('linkPreview_textOperations');
            if (saved) {
                const settings = JSON.parse(saved);
                this.config = { ...this.config, ...settings.config };
                this.isEnabled = settings.isEnabled !== undefined ? settings.isEnabled : true;
            }
        } catch (error) {
            console.error('加载文本操作设置失败:', error);
        }
    }

    /**
     * 获取配置
     */
    getConfig() {
        return {
            ...this.config,
            isEnabled: this.isEnabled
        };
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.saveSettings();
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            isEnabled: this.isEnabled,
            defaultSearchEngine: this.config.defaultSearchEngine,
            defaultTranslationService: this.config.defaultTranslationService,
            quickActionsEnabled: this.config.enableQuickActions,
            contextMenuEnabled: this.config.enableContextMenu,
            availableSearchEngines: Object.keys(this.config.searchEngines),
            availableTranslationServices: Object.keys(this.config.translationServices)
        };
    }

    /**
     * 销毁文本操作处理器
     */
    destroy() {
        this.hideQuickActions();
        this.hideContextMenu();
        this.hideDropZones();

        // 移除拖拽区域
        if (this.searchDropZone && this.searchDropZone.parentNode) {
            this.searchDropZone.parentNode.removeChild(this.searchDropZone);
        }
        if (this.translateDropZone && this.translateDropZone.parentNode) {
            this.translateDropZone.parentNode.removeChild(this.translateDropZone);
        }

        console.log('💥 文本操作处理器已销毁');
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.TextOperationsHandler = TextOperationsHandler;
}
