/**
 * 预览窗口管理器
 * 负责管理多个预览窗口的创建、排列、层级和记忆功能
 */
class PreviewWindowManager {
    constructor(linkPreviewManager) {
        this.linkPreviewManager = linkPreviewManager;
        this.windows = new Map();
        this.windowMemory = typeof WindowMemory !== 'undefined' ? new WindowMemory() : null;
        this.zIndexCounter = 10000;
        this.maxWindows = 5;
        this.focusedWindowId = null;
        
        // 窗口排列配置
        this.arrangement = {
            strategy: 'cascade', // cascade, grid, smart
            cascadeOffset: { x: 30, y: 30 },
            gridColumns: 3,
            minSpacing: 20
        };
        
        this.init();
    }

    /**
     * 初始化窗口管理器
     */
    init() {
        this.bindGlobalEvents();
        console.log('✅ 预览窗口管理器初始化完成');
    }

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 监听窗口大小变化，重新排列窗口
        window.addEventListener('resize', LinkUtils.debounce(() => {
            this.rearrangeWindows();
        }, 300));

        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleGlobalKeydown(e);
        });
    }

    /**
     * 创建新的预览窗口
     */
    async createWindow(url, options = {}) {
        try {
            // 检查窗口数量限制
            if (this.getWindowCount() >= this.maxWindows) {
                this.closeOldestWindow();
            }

            // 检查是否已存在相同URL的窗口
            const existingWindow = this.findWindowByUrl(url);
            if (existingWindow) {
                this.focusWindow(existingWindow.id);
                return existingWindow;
            }

            // 获取窗口记忆配置
            const memoryConfig = this.windowMemory ? this.windowMemory.getWindowConfig(url) : {};
            
            // 计算窗口位置
            const position = this.calculateWindowPosition(options, memoryConfig);
            
            // 准备窗口选项
            const windowOptions = {
                id: this.generateWindowId(),
                zIndex: ++this.zIndexCounter,
                ...memoryConfig,
                ...options,
                ...position
            };

            // 创建预览窗口
            const previewWindow = new PreviewWindow(url, windowOptions);
            await previewWindow.render();

            // 注册窗口
            this.windows.set(previewWindow.id, previewWindow);
            this.focusedWindowId = previewWindow.id;

            // 绑定窗口事件
            this.bindWindowEvents(previewWindow);

            // 更新窗口排列
            this.updateWindowArrangement();

            console.log(`✅ 窗口管理器创建窗口: ${url} (${previewWindow.id})`);
            return previewWindow;

        } catch (error) {
            console.error('❌ 窗口管理器创建窗口失败:', error);
            throw error;
        }
    }

    /**
     * 销毁窗口
     */
    destroyWindow(windowId) {
        const window = this.windows.get(windowId);
        if (!window) return;

        // 保存窗口配置到记忆系统
        if (this.windowMemory) {
            this.windowMemory.saveWindowConfig(window.url, {
                width: window.element.offsetWidth,
                height: window.element.offsetHeight,
                position: {
                    left: parseInt(window.element.style.left),
                    top: parseInt(window.element.style.top)
                }
            });
        }

        // 销毁窗口
        window.destroy();
        this.windows.delete(windowId);

        // 更新焦点
        if (this.focusedWindowId === windowId) {
            this.focusNextAvailableWindow();
        }

        // 重新排列剩余窗口
        this.updateWindowArrangement();

        console.log(`🗑️ 窗口管理器销毁窗口: ${windowId}`);
    }

    /**
     * 销毁所有窗口
     */
    destroyAllWindows() {
        const windowIds = this.getWindowIds();
        windowIds.forEach(id => this.destroyWindow(id));
        console.log('🧹 窗口管理器已销毁所有窗口');
    }

    /**
     * 聚焦窗口
     */
    focusWindow(windowId, fromEvent = false) {
        const window = this.windows.get(windowId);
        if (!window) return;

        // 如果已经是焦点窗口，避免重复操作
        if (this.focusedWindowId === windowId && fromEvent) return;

        // 移除其他窗口的焦点
        this.windows.forEach(w => {
            if (w.id !== windowId) {
                w.blur();
            }
        });

        // 设置当前窗口焦点（只有在不是来自事件时才调用focus）
        if (!fromEvent) {
            window.focus();
        }
        this.focusedWindowId = windowId;

        // 置于最前
        window.zIndex = ++this.zIndexCounter;
        window.element.style.zIndex = window.zIndex;

        console.log(`🔍 窗口已聚焦: ${windowId}`);
    }

    /**
     * 获取窗口ID数组（避免重复创建）
     */
    getWindowIds() {
        return Array.from(this.windows.keys());
    }

    /**
     * 获取当前窗口数量
     */
    getWindowCount() {
        return this.windows.size;
    }

    /**
     * 检查是否有窗口
     */
    hasWindows() {
        return this.windows.size > 0;
    }

    /**
     * 聚焦下一个窗口
     */
    focusNextWindow() {
        const windowIds = this.getWindowIds();
        if (windowIds.length === 0) return;

        const currentIndex = windowIds.indexOf(this.focusedWindowId);
        const nextIndex = (currentIndex + 1) % windowIds.length;
        this.focusWindow(windowIds[nextIndex]);
    }

    /**
     * 聚焦上一个窗口
     */
    focusPrevWindow() {
        const windowIds = this.getWindowIds();
        if (windowIds.length === 0) return;

        const currentIndex = windowIds.indexOf(this.focusedWindowId);
        const prevIndex = currentIndex <= 0 ? windowIds.length - 1 : currentIndex - 1;
        this.focusWindow(windowIds[prevIndex]);
    }

    /**
     * 聚焦下一个可用窗口
     */
    focusNextAvailableWindow() {
        const windowIds = this.getWindowIds();
        if (windowIds.length > 0) {
            this.focusWindow(windowIds[0]);
        } else {
            this.focusedWindowId = null;
        }
    }

    /**
     * 关闭最旧的窗口
     */
    closeOldestWindow() {
        const oldestWindow = Array.from(this.windows.values())[0];
        if (oldestWindow) {
            this.destroyWindow(oldestWindow.id);
        }
    }

    /**
     * 根据URL查找窗口
     */
    findWindowByUrl(url) {
        for (const window of this.windows.values()) {
            if (window.url === url) {
                return window;
            }
        }
        return null;
    }

    /**
     * 计算窗口位置
     */
    calculateWindowPosition(options, memoryConfig) {
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const windowWidth = memoryConfig?.width || options.width || 800;
        const windowHeight = memoryConfig?.height || options.height || 600;

        // 如果有记忆位置且位置有效，使用记忆位置
        if (memoryConfig?.position && this.isPositionValid(memoryConfig.position, windowWidth, windowHeight)) {
            return {
                left: memoryConfig.position.left,
                top: memoryConfig.position.top
            };
        }

        // 根据排列策略计算位置
        switch (this.arrangement.strategy) {
            case 'cascade':
                return this.calculateCascadePosition(windowWidth, windowHeight);
            case 'grid':
                return this.calculateGridPosition(windowWidth, windowHeight);
            case 'smart':
                return this.calculateSmartPosition(windowWidth, windowHeight);
            default:
                return this.calculateCenterPosition(windowWidth, windowHeight);
        }
    }

    /**
     * 计算层叠位置
     */
    calculateCascadePosition(width, height) {
        const windowCount = this.windows.size;
        const offset = this.arrangement.cascadeOffset;
        
        let left = 100 + (windowCount * offset.x);
        let top = 100 + (windowCount * offset.y);

        // 确保窗口在视口内
        const maxLeft = window.innerWidth - width - 20;
        const maxTop = window.innerHeight - height - 20;

        if (left > maxLeft || top > maxTop) {
            // 重置位置
            left = 100;
            top = 100;
        }

        return { left, top };
    }

    /**
     * 计算网格位置
     */
    calculateGridPosition(width, height) {
        const windowCount = this.windows.size;
        const cols = this.arrangement.gridColumns;
        const row = Math.floor(windowCount / cols);
        const col = windowCount % cols;

        const cellWidth = window.innerWidth / cols;
        const cellHeight = window.innerHeight / Math.ceil(this.maxWindows / cols);

        const left = col * cellWidth + (cellWidth - width) / 2;
        const top = row * cellHeight + (cellHeight - height) / 2;

        return { 
            left: Math.max(10, left), 
            top: Math.max(10, top) 
        };
    }

    /**
     * 计算智能位置（避免重叠）
     */
    calculateSmartPosition(width, height) {
        const spacing = this.arrangement.minSpacing;
        const attempts = 20; // 最大尝试次数

        for (let i = 0; i < attempts; i++) {
            const left = Math.random() * (window.innerWidth - width - 40) + 20;
            const top = Math.random() * (window.innerHeight - height - 40) + 20;

            if (this.isPositionFree(left, top, width, height, spacing)) {
                return { left, top };
            }
        }

        // 如果找不到合适位置，使用层叠位置
        return this.calculateCascadePosition(width, height);
    }

    /**
     * 计算居中位置
     */
    calculateCenterPosition(width, height) {
        return {
            left: (window.innerWidth - width) / 2,
            top: (window.innerHeight - height) / 2
        };
    }

    /**
     * 检查位置是否有效
     */
    isPositionValid(position, width, height) {
        return position.left >= 0 &&
               position.top >= 0 &&
               position.left + width <= window.innerWidth &&
               position.top + height <= window.innerHeight;
    }

    /**
     * 检查位置是否空闲（无重叠）
     */
    isPositionFree(left, top, width, height, spacing) {
        for (const window of this.windows.values()) {
            const winRect = window.element.getBoundingClientRect();

            // 检查是否重叠（包含间距）
            if (!(left + width + spacing < winRect.left ||
                  left - spacing > winRect.right ||
                  top + height + spacing < winRect.top ||
                  top - spacing > winRect.bottom)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 绑定窗口事件
     */
    bindWindowEvents(previewWindow) {
        // 窗口关闭事件
        previewWindow.on('close', () => {
            this.destroyWindow(previewWindow.id);
        });

        // 窗口焦点事件
        previewWindow.on('focus', () => {
            this.focusWindow(previewWindow.id, true);
        });

        // 窗口调整大小事件
        previewWindow.on('resized', (data) => {
            if (this.windowMemory) {
                this.windowMemory.saveWindowConfig(previewWindow.url, {
                    width: data.width,
                    height: data.height
                });
            }
        });

        // 窗口移动事件（可以添加防抖）
        previewWindow.on('moved', LinkUtils.debounce((data) => {
            if (this.windowMemory) {
                this.windowMemory.saveWindowConfig(previewWindow.url, {
                    position: data.position
                });
            }
        }, 1000));
    }

    /**
     * 更新窗口排列
     */
    updateWindowArrangement() {
        if (this.getWindowCount() <= 1) return;

        // 根据当前策略重新排列窗口
        const windows = Array.from(this.windows.values());

        windows.forEach((window, index) => {
            if (window.id === this.focusedWindowId) return; // 跳过焦点窗口

            const position = this.calculateWindowPosition({}, {});
            window.element.style.left = `${position.left}px`;
            window.element.style.top = `${position.top}px`;
        });
    }

    /**
     * 重新排列所有窗口
     */
    rearrangeWindows() {
        const windows = Array.from(this.windows.values());

        windows.forEach((window, index) => {
            const width = window.element.offsetWidth;
            const height = window.element.offsetHeight;
            const position = this.calculateWindowPosition({ width, height }, {});

            window.element.style.left = `${position.left}px`;
            window.element.style.top = `${position.top}px`;
        });
    }

    /**
     * 处理全局键盘事件
     */
    handleGlobalKeydown(e) {
        if (!this.hasWindows()) return;

        switch (e.key) {
            case 'Tab':
                if (e.shiftKey) {
                    e.preventDefault();
                    this.focusPrevWindow();
                } else if (this.getWindowCount() > 1) {
                    e.preventDefault();
                    this.focusNextWindow();
                }
                break;

            case 'w':
            case 'W':
                if ((e.ctrlKey || e.metaKey) && this.focusedWindowId) {
                    e.preventDefault();
                    this.destroyWindow(this.focusedWindowId);
                }
                break;

            case 'Escape':
                if (this.hasWindows()) {
                    e.preventDefault();
                    this.destroyAllWindows();
                }
                break;

            // 数字键快速切换窗口
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
                if (e.altKey) {
                    e.preventDefault();
                    const windowIndex = parseInt(e.key) - 1;
                    const windowIds = this.getWindowIds();
                    if (windowIds[windowIndex]) {
                        this.focusWindow(windowIds[windowIndex]);
                    }
                }
                break;
        }
    }

    /**
     * 设置排列策略
     */
    setArrangementStrategy(strategy) {
        this.arrangement.strategy = strategy;
        this.rearrangeWindows();
        console.log(`🔄 窗口排列策略已更改为: ${strategy}`);
    }

    /**
     * 设置最大窗口数量
     */
    setMaxWindows(maxWindows) {
        this.maxWindows = maxWindows;

        // 如果当前窗口数量超过限制，关闭多余的窗口
        while (this.getWindowCount() > this.maxWindows) {
            this.closeOldestWindow();
        }
    }

    /**
     * 生成窗口ID
     */
    generateWindowId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }



    /**
     * 获取所有窗口信息
     */
    getAllWindows() {
        return Array.from(this.windows.values()).map(window => ({
            id: window.id,
            url: window.url,
            title: window.getDisplayUrl(),
            isFocused: window.id === this.focusedWindowId,
            position: {
                left: parseInt(window.element.style.left),
                top: parseInt(window.element.style.top)
            },
            size: {
                width: window.element.offsetWidth,
                height: window.element.offsetHeight
            },
            zIndex: window.zIndex
        }));
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            totalWindows: this.getWindowCount(),
            maxWindows: this.maxWindows,
            focusedWindowId: this.focusedWindowId,
            arrangementStrategy: this.arrangement.strategy,
            zIndexCounter: this.zIndexCounter
        };
    }

    /**
     * 销毁窗口管理器
     */
    destroy() {
        this.destroyAllWindows();
        if (this.windowMemory) {
            this.windowMemory.destroy();
        }
        console.log('💥 预览窗口管理器已销毁');
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.PreviewWindowManager = PreviewWindowManager;
}
