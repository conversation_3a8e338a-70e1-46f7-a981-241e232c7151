# 链接预览功能快速开始指南

## 立即开始开发

本指南帮助您快速开始第一阶段的链接预览功能开发。按照以下步骤，您可以在 30 分钟内搭建基础框架。

## 第一步：创建文件结构

```bash
# 在项目根目录执行
mkdir -p modules/link-preview
touch modules/link-preview/link-preview-manager.js
touch modules/link-preview/preview-window.js
touch modules/link-preview/event-manager.js
touch modules/link-preview/link-preview.css
```

## 第二步：创建基础 CSS 样式

将以下内容保存到 `modules/link-preview/link-preview.css`：

```css
/* 链接预览窗口基础样式 */
.link-preview-window {
    position: fixed;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    user-select: none;
    z-index: 10000;
    min-width: 300px;
    min-height: 200px;
}

.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    cursor: move;
    min-height: 40px;
}

.preview-title {
    flex: 1;
    min-width: 0;
}

.preview-url {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.preview-controls {
    display: flex;
    gap: 4px;
}

.preview-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: background-color 0.2s;
}

.preview-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.close-btn:hover {
    background: #ff5f56;
    color: white;
}

.preview-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.preview-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

.preview-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    z-index: 10;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 12px;
    font-size: 14px;
    color: #666;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.preview-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 16px;
    height: 16px;
    cursor: nw-resize;
    background: linear-gradient(-45deg, transparent 30%, #e0e0e0 30%, #e0e0e0 70%, transparent 70%);
}
```

## 第三步：创建最小可用版本

### 1. 事件管理器 (`modules/link-preview/event-manager.js`)

```javascript
class LinkPreviewEventManager {
    constructor(manager) {
        this.manager = manager;
        this.keyboardState = { alt: false };
    }

    bindGlobalEvents() {
        // 键盘状态跟踪
        document.addEventListener('keydown', (e) => {
            this.keyboardState.alt = e.altKey;
        });

        document.addEventListener('keyup', (e) => {
            this.keyboardState.alt = e.altKey;
        });

        // 链接点击事件
        document.addEventListener('click', (e) => {
            if (this.keyboardState.alt) {
                const link = e.target.closest('a[href]');
                if (link && link.href && !link.href.startsWith('javascript:')) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.manager.createPreview(link.href);
                }
            }
        }, true);

        // ESC 关闭所有窗口
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.manager.destroyAllPreviews();
            }
        });
    }
}
```

### 2. 预览窗口组件 (`modules/link-preview/preview-window.js`)

```javascript
class PreviewWindow {
    constructor(url, options = {}) {
        this.url = url;
        this.id = options.id || Date.now() + Math.random().toString(36).substr(2, 9);
        this.element = null;
        this.iframe = null;
    }

    async render() {
        this.createElement();
        this.applyStyles();
        this.calculatePosition();
        document.body.appendChild(this.element);
        this.bindEvents();
        this.showWithAnimation();
        await this.loadContent();
    }

    createElement() {
        this.element = document.createElement('div');
        this.element.className = 'link-preview-window';
        this.element.dataset.windowId = this.id;
        
        this.element.innerHTML = `
            <div class="preview-header">
                <div class="preview-title">
                    <span class="preview-url">${this.getDisplayUrl()}</span>
                </div>
                <div class="preview-controls">
                    <button class="preview-btn close-btn" title="关闭">✕</button>
                </div>
            </div>
            <div class="preview-content">
                <div class="preview-loading">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在加载...</div>
                </div>
                <iframe class="preview-iframe" sandbox="allow-scripts allow-same-origin allow-forms"></iframe>
            </div>
            <div class="preview-resize-handle"></div>
        `;

        this.iframe = this.element.querySelector('.preview-iframe');
        this.loadingElement = this.element.querySelector('.preview-loading');
        this.headerElement = this.element.querySelector('.preview-header');
    }

    applyStyles() {
        Object.assign(this.element.style, {
            width: '800px',
            height: '600px',
            opacity: '0',
            transform: 'scale(0.9)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        });
    }

    calculatePosition() {
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const left = (viewportWidth - 800) / 2;
        const top = (viewportHeight - 600) / 2;
        
        this.element.style.left = `${Math.max(10, left)}px`;
        this.element.style.top = `${Math.max(10, top)}px`;
    }

    async loadContent() {
        return new Promise((resolve) => {
            this.iframe.onload = () => {
                this.loadingElement.style.display = 'none';
                resolve();
            };
            this.iframe.src = this.url;
        });
    }

    bindEvents() {
        // 关闭按钮
        this.element.querySelector('.close-btn').addEventListener('click', () => {
            this.close();
        });

        // 简单拖拽
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        this.headerElement.addEventListener('mousedown', (e) => {
            if (e.target.closest('.preview-controls')) return;
            
            isDragging = true;
            const rect = this.element.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const left = e.clientX - dragOffset.x;
            const top = e.clientY - dragOffset.y;
            
            this.element.style.left = `${left}px`;
            this.element.style.top = `${top}px`;
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });
    }

    showWithAnimation() {
        requestAnimationFrame(() => {
            this.element.style.opacity = '1';
            this.element.style.transform = 'scale(1)';
        });
    }

    close() {
        this.element.style.opacity = '0';
        this.element.style.transform = 'scale(0.9)';
        
        setTimeout(() => {
            this.destroy();
        }, 300);
    }

    destroy() {
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
    }

    getDisplayUrl() {
        try {
            const url = new URL(this.url);
            return url.hostname + url.pathname;
        } catch {
            return this.url;
        }
    }
}
```

### 3. 主管理器 (`modules/link-preview/link-preview-manager.js`)

```javascript
class LinkPreviewManager {
    constructor(options = {}) {
        this.previewWindows = new Map();
        this.eventManager = new LinkPreviewEventManager(this);
        this.maxWindows = 5;
        this.init();
    }

    init() {
        this.eventManager.bindGlobalEvents();
        console.log('✅ 链接预览管理器初始化完成');
    }

    async createPreview(url) {
        try {
            // 限制窗口数量
            if (this.previewWindows.size >= this.maxWindows) {
                this.closeOldestWindow();
            }

            // 创建预览窗口
            const previewWindow = new PreviewWindow(url);
            await previewWindow.render();
            
            // 注册窗口
            this.previewWindows.set(previewWindow.id, previewWindow);
            
            return previewWindow;
        } catch (error) {
            console.error('创建预览窗口失败:', error);
        }
    }

    destroyPreview(windowId) {
        const window = this.previewWindows.get(windowId);
        if (window) {
            window.destroy();
            this.previewWindows.delete(windowId);
        }
    }

    destroyAllPreviews() {
        this.previewWindows.forEach((window, id) => {
            this.destroyPreview(id);
        });
    }

    closeOldestWindow() {
        const oldestWindow = Array.from(this.previewWindows.values())[0];
        if (oldestWindow) {
            this.destroyPreview(oldestWindow.id);
        }
    }
}

// 暴露到全局
window.LinkPreviewManager = LinkPreviewManager;
```

## 第四步：集成到主项目

### 1. 在 HTML 中引入文件

在主 HTML 文件的 `</body>` 标签前添加：

```html
<!-- 链接预览功能 -->
<link rel="stylesheet" href="modules/link-preview/link-preview.css">
<script src="modules/link-preview/event-manager.js"></script>
<script src="modules/link-preview/preview-window.js"></script>
<script src="modules/link-preview/link-preview-manager.js"></script>
```

### 2. 初始化功能

在主 `script.js` 文件末尾添加：

```javascript
// 初始化链接预览功能
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.LinkPreviewManager) {
            window.app = window.app || {};
            window.app.linkPreviewManager = new LinkPreviewManager();
            console.log('✅ 链接预览功能已启用');
        }
    }, 1000);
});
```

## 第五步：测试功能

1. **打开项目页面**
2. **按住 Alt 键**
3. **点击任意链接**
4. **应该看到预览窗口弹出**
5. **按 ESC 键关闭窗口**

## 常见问题解决

### 问题1：预览窗口不显示
- 检查控制台是否有错误
- 确认 CSS 文件正确加载
- 检查 z-index 是否被其他元素覆盖

### 问题2：iframe 内容不加载
- 检查目标网站是否允许 iframe 嵌入
- 查看控制台的 CORS 错误信息
- 尝试访问允许嵌入的网站

### 问题3：拖拽功能不工作
- 确认鼠标事件正确绑定
- 检查 CSS 的 `cursor: move` 是否生效
- 验证事件冒泡是否被阻止

## 下一步开发

完成基础功能后，可以继续开发：

1. **添加设置页面集成**
2. **实现窗口调整大小功能**
3. **添加多窗口管理**
4. **实现配置保存和加载**

参考 `link-preview-implementation-guide.md` 获取完整的开发计划。
