/* 链接预览窗口基础样式 */
.link-preview-window {
    position: fixed;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    user-select: none;
    z-index: 10000;
    min-width: 300px;
    min-height: 200px;
    backdrop-filter: blur(10px);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .link-preview-window {
        background: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }
    
    .preview-header {
        background: #3a3a3a !important;
        border-bottom-color: #404040 !important;
    }
    
    .preview-url {
        color: #cccccc !important;
    }
    
    .preview-loading {
        background: #2d2d2d !important;
    }
    
    .loading-text {
        color: #cccccc !important;
    }
}

/* 预览窗口头部 */
.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    cursor: move;
    min-height: 40px;
}

.preview-title {
    flex: 1;
    min-width: 0;
}

.preview-url {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 400px;
}

.preview-controls {
    display: flex;
    gap: 4px;
}

.preview-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: background-color 0.2s;
    color: inherit;
}

.preview-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.close-btn:hover {
    background: #ff5f56;
    color: white;
}

.reading-mode-btn:hover {
    background: #007bff;
    color: white;
}

.minimize-btn:hover {
    background: #ffc107;
    color: white;
}

/* 预览内容区域 */
.preview-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.preview-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

/* 加载状态 */
.preview-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    z-index: 10;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 12px;
    font-size: 14px;
    color: #666;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 */
.preview-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px;
    text-align: center;
    background: inherit;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.error-message {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
}

.retry-btn {
    padding: 8px 16px;
    background: #1976d2;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background: #1565c0;
}

/* 调整大小手柄 */
.preview-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 16px;
    height: 16px;
    cursor: nw-resize;
    background: linear-gradient(-45deg, transparent 30%, #e0e0e0 30%, #e0e0e0 70%, transparent 70%);
    opacity: 0.6;
    transition: opacity 0.2s;
}

.preview-resize-handle:hover {
    opacity: 1;
}

/* 全屏模式 */
.link-preview-window.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 99999 !important;
    border-radius: 0 !important;
}

/* 最小化状态 */
.link-preview-window.minimized {
    height: 40px !important;
    overflow: hidden;
}

.link-preview-window.minimized .preview-content,
.link-preview-window.minimized .preview-resize-handle {
    display: none;
}

/* 动画效果 - 已在主样式中定义 transition */

.link-preview-window.entering {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

.link-preview-window.entered {
    opacity: 1;
    transform: scale(1) translateY(0);
}

.link-preview-window.exiting {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

/* 响应式适配 */
@media (max-width: 768px) {
    .link-preview-window {
        width: calc(100vw - 20px) !important;
        height: calc(100vh - 20px) !important;
        top: 10px !important;
        left: 10px !important;
        border-radius: 8px;
    }
    
    .preview-url {
        max-width: 200px;
    }
}

/* 焦点状态 */
.link-preview-window.focused {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #1976d2;
}

/* 拖拽状态 */
.link-preview-window.dragging {
    cursor: grabbing;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
    transform: rotate(1deg);
}

.link-preview-window.dragging .preview-header {
    cursor: grabbing;
}

/* 调整大小状态 */
.link-preview-window.resizing {
    transition: none;
}

/* 多窗口层叠效果 */
.link-preview-window:nth-of-type(2) {
    transform: translateX(20px) translateY(20px);
}

.link-preview-window:nth-of-type(3) {
    transform: translateX(40px) translateY(40px);
}

/* 工具提示样式 */
.preview-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* 状态指示器 */
.preview-status {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4caf50;
    z-index: 11;
}

.preview-status.loading {
    background: #ff9800;
    animation: pulse 1.5s infinite;
}

.preview-status.error {
    background: #f44336;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 右键菜单样式 */
.link-preview-context-menu {
    background: var(--preview-bg, #ffffff);
    border: 1px solid var(--preview-border, #e0e0e0);
    border-radius: 6px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    padding: 4px 0;
    min-width: 160px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    backdrop-filter: blur(10px);
}

.context-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: var(--preview-text, #333);
}

.context-menu-item:hover {
    background: var(--preview-btn-hover, rgba(0, 0, 0, 0.05));
}

.menu-icon {
    margin-right: 8px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.menu-text {
    flex: 1;
}

.context-menu-separator {
    height: 1px;
    background: var(--preview-border, #e0e0e0);
    margin: 4px 0;
}

/* 拖拽区域样式 */
.link-preview-drop-zone {
    background: rgba(0, 123, 255, 0.1);
    border: 2px dashed #007bff;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.drop-zone-content {
    text-align: center;
    color: #007bff;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.drop-zone-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.drop-zone-text {
    font-size: 14px;
    font-weight: 500;
}

/* 提示消息样式 */
.link-preview-toast {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

/* 深色模式适配 - 右键菜单 */
@media (prefers-color-scheme: dark) {
    .link-preview-context-menu {
        background: var(--preview-bg, #2d2d2d);
        border-color: var(--preview-border, #404040);
    }

    .context-menu-item {
        color: var(--preview-text, #ffffff);
    }

    .context-menu-item:hover {
        background: var(--preview-btn-hover, rgba(255, 255, 255, 0.1));
    }

    .context-menu-separator {
        background: var(--preview-border, #404040);
    }

    .drop-zone-content {
        color: #4dabf7;
    }
}

/* 阅读模式控制面板样式 */
.reading-controls {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(10px);
}

.reading-controls-content {
    padding: 16px;
}

.control-group {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 8px;
}

.control-group label {
    min-width: 60px;
    font-size: 14px;
    color: #333;
}

.control-group select,
.control-group input[type="range"] {
    flex: 1;
    margin-right: 8px;
}

.control-group select {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.control-group input[type="range"] {
    height: 4px;
    background: #ddd;
    border-radius: 2px;
    outline: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
}

.control-group .config-value {
    min-width: 50px;
    font-size: 12px;
    color: #666;
    text-align: right;
}

/* 文本操作快速菜单样式 */
.text-quick-actions {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.quick-action {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-right: 1px solid #e0e0e0;
    min-width: 80px;
}

.quick-action:last-child {
    border-right: none;
}

.quick-action:hover {
    background: rgba(0, 123, 255, 0.1);
}

.action-icon {
    margin-right: 6px;
    font-size: 14px;
}

.action-text {
    font-size: 12px;
    color: #333;
    white-space: nowrap;
}

/* 文本操作右键菜单样式 */
.text-context-menu {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    padding: 4px 0;
    backdrop-filter: blur(10px);
}

.text-context-menu .context-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #333;
}

.text-context-menu .context-menu-item:hover {
    background: rgba(0, 123, 255, 0.1);
}

.text-context-menu .menu-icon {
    margin-right: 8px;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.text-context-menu .menu-text {
    flex: 1;
    font-size: 14px;
}

/* 文本拖拽区域样式 */
.text-drop-zone {
    background: rgba(0, 123, 255, 0.1);
    border: 2px dashed #007bff;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.text-drop-zone .drop-zone-content {
    text-align: center;
    color: #007bff;
}

.text-drop-zone .drop-zone-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.text-drop-zone .drop-zone-text {
    font-size: 12px;
    font-weight: 500;
}

/* 高级配置弹窗样式 */
.advanced-config-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.config-dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(2px);
}

.config-dialog-content {
    position: relative;
    width: 800px;
    max-width: calc(100vw - 40px);
    max-height: calc(100vh - 40px);
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.config-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.config-dialog-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.config-dialog-close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    border-radius: 6px;
}

.config-dialog-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

.config-dialog-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.config-tabs {
    width: 200px;
    background: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    padding: 16px 0;
}

.config-tab {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 20px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
    font-size: 14px;
}

.config-tab:hover {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.config-tab.active {
    background: #007bff;
    color: white;
}

.tab-icon {
    margin-right: 8px;
    font-size: 16px;
}

.tab-text {
    font-weight: 500;
}

.config-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
}

.config-panel {
    display: none;
}

.config-panel.active {
    display: block;
}

.config-section {
    margin-bottom: 32px;
}

.config-section h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 8px;
}

.config-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 12px;
}

.config-label {
    min-width: 120px;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-checkbox {
    margin: 0;
}

.config-select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.config-range {
    flex: 1;
    height: 6px;
    background: #ddd;
    border-radius: 3px;
    outline: none;
    margin: 0 12px;
}

.config-range::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.config-value {
    min-width: 60px;
    font-size: 13px;
    color: #666;
    text-align: right;
    font-family: monospace;
}

.config-desc {
    flex: 1;
    font-size: 12px;
    color: #888;
    margin-left: auto;
}

.config-dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.config-btn {
    padding: 10px 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.config-btn-secondary {
    background: white;
    color: #666;
}

.config-btn-secondary:hover {
    background: #f8f9fa;
    border-color: #bbb;
}

.config-btn-primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.config-btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

/* 高级功能设置页面样式 */
.advanced-config-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.advanced-config-btn:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.advanced-config-btn .btn-icon {
    font-size: 16px;
}

.advanced-config-btn .btn-text {
    font-weight: 600;
}

.feature-status {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.status-label {
    color: #666;
    font-weight: 500;
}

.status-value {
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-success {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.status-warning {
    color: #ffc107;
    background: rgba(255, 193, 7, 0.1);
}

.status-error {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.quick-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: white;
    color: #666;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
}

.quick-action-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.quick-action-btn .btn-icon {
    font-size: 14px;
}

/* 深色模式适配 - 高级功能 */
@media (prefers-color-scheme: dark) {
    .advanced-config-dialog .config-dialog-content {
        background: #2d2d2d;
        color: #ffffff;
    }

    .advanced-config-dialog .config-dialog-header {
        background: #3d3d3d;
        border-color: #404040;
    }

    .advanced-config-dialog .config-tabs {
        background: #3d3d3d;
        border-color: #404040;
    }

    .advanced-config-dialog .config-tab {
        color: #cccccc;
    }

    .advanced-config-dialog .config-tab:hover {
        background: rgba(0, 123, 255, 0.2);
    }

    .advanced-config-dialog .config-tab.active {
        background: #007bff;
        color: white;
    }

    .advanced-config-dialog .config-select {
        background: #404040;
        border-color: #555555;
        color: #ffffff;
    }

    .advanced-config-dialog .config-dialog-footer {
        background: #3d3d3d;
        border-color: #404040;
    }

    .feature-status {
        background: #3d3d3d;
        border-color: #555555;
    }

    .quick-action-btn {
        background: #404040;
        border-color: #555555;
        color: #cccccc;
    }

    .quick-action-btn:hover {
        background: #4d4d4d;
        border-color: #007bff;
        color: #4dabf7;
    }
}
