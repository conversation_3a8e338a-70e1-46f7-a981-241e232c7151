# Moment Search V3 - 部署运维指南

> 完整的构建、测试、发布和运维流程指南

## 🚀 部署概览

Moment Search V3 作为Chrome扩展，具有独特的部署特点：
- **无需传统构建**: 直接使用源代码运行
- **多渠道发布**: Chrome Web Store、Edge Add-ons、自托管
- **版本管理**: 基于manifest.json的版本控制
- **自动更新**: 浏览器自动处理更新机制

## 📦 构建流程

### 预构建检查

#### 1. 代码质量检查

```bash
# 创建构建脚本 build.sh
#!/bin/bash

echo "🔍 开始构建前检查..."

# 检查必需文件
required_files=("manifest.json" "index.html" "script.js" "style.css" "background.js")
for file in "${required_files[@]}"; do
    if [ ! -f "moment-search-v3/$file" ]; then
        echo "❌ 缺少必需文件: $file"
        exit 1
    fi
    echo "✅ $file 存在"
done

# 验证manifest.json格式
if ! jq empty moment-search-v3/manifest.json 2>/dev/null; then
    echo "❌ manifest.json 格式错误"
    exit 1
fi
echo "✅ manifest.json 格式正确"

# 检查版本号
version=$(jq -r '.version' moment-search-v3/manifest.json)
echo "📦 当前版本: $version"
```

#### 2. 功能测试

```javascript
// test/functional-test.js
class FunctionalTest {
    static async runAllTests() {
        console.log('🧪 开始功能测试...');
        
        const tests = [
            this.testManifestValid,
            this.testCoreFilesExist,
            this.testBasicFunctionality,
            this.testPermissions,
            this.testCSP
        ];
        
        let passed = 0;
        let failed = 0;
        
        for (const test of tests) {
            try {
                await test();
                passed++;
            } catch (error) {
                console.error(`❌ 测试失败:`, error.message);
                failed++;
            }
        }
        
        console.log(`📊 测试结果: ${passed} 通过, ${failed} 失败`);
        return failed === 0;
    }
    
    static async testManifestValid() {
        const response = await fetch('manifest.json');
        const manifest = await response.json();
        
        // 检查必需字段
        const required = ['manifest_version', 'name', 'version', 'description'];
        for (const field of required) {
            if (!manifest[field]) {
                throw new Error(`Manifest缺少字段: ${field}`);
            }
        }
        
        console.log('✅ Manifest验证通过');
    }
    
    static async testBasicFunctionality() {
        // 测试核心功能是否正常
        if (typeof window.app === 'undefined') {
            throw new Error('主应用未初始化');
        }
        
        if (!window.app.searchManager) {
            throw new Error('搜索管理器未初始化');
        }
        
        console.log('✅ 基础功能测试通过');
    }
}
```

#### 3. 性能测试

```javascript
// test/performance-test.js
class PerformanceTest {
    static async runPerformanceTests() {
        console.log('⚡ 开始性能测试...');
        
        // 测试加载时间
        const loadStart = performance.now();
        await this.waitForAppReady();
        const loadTime = performance.now() - loadStart;
        
        console.log(`📊 应用加载时间: ${loadTime.toFixed(2)}ms`);
        
        if (loadTime > 300) {
            console.warn('⚠️ 加载时间超过300ms目标');
        } else {
            console.log('✅ 加载性能达标');
        }
        
        // 测试内存使用
        if (performance.memory) {
            const memoryMB = performance.memory.usedJSHeapSize / 1024 / 1024;
            console.log(`💾 内存使用: ${memoryMB.toFixed(2)}MB`);
            
            if (memoryMB > 30) {
                console.warn('⚠️ 内存使用超过30MB目标');
            } else {
                console.log('✅ 内存使用达标');
            }
        }
    }
    
    static waitForAppReady() {
        return new Promise((resolve) => {
            if (window.app && window.app.searchManager) {
                resolve();
            } else {
                setTimeout(() => this.waitForAppReady().then(resolve), 10);
            }
        });
    }
}
```

### 构建脚本

```bash
#!/bin/bash
# build.sh - 完整构建脚本

set -e  # 遇到错误立即退出

echo "🚀 Moment Search V3 构建开始..."

# 1. 清理构建目录
echo "🧹 清理构建目录..."
rm -rf dist/
mkdir -p dist/

# 2. 复制源文件
echo "📁 复制源文件..."
cp -r moment-search-v3/* dist/

# 3. 验证文件完整性
echo "🔍 验证文件完整性..."
cd dist/

# 检查文件大小
total_size=$(du -sh . | cut -f1)
echo "📦 扩展包大小: $total_size"

# 4. 运行测试
echo "🧪 运行测试..."
if command -v node &> /dev/null; then
    node ../test/build-test.js
fi

# 5. 创建发布包
echo "📦 创建发布包..."
cd ..
zip -r "moment-search-v3-$(jq -r '.version' dist/manifest.json).zip" dist/

echo "✅ 构建完成!"
echo "📦 发布包: moment-search-v3-$(jq -r '.version' dist/manifest.json).zip"
```

## 🔄 版本管理

### 版本号规范

遵循语义化版本控制 (Semantic Versioning):

```json
{
  "version": "3.1.2"
}
```

- **主版本号 (3)**: 不兼容的API修改
- **次版本号 (1)**: 向下兼容的功能性新增
- **修订号 (2)**: 向下兼容的问题修正

### 版本更新脚本

```bash
#!/bin/bash
# update-version.sh

current_version=$(jq -r '.version' moment-search-v3/manifest.json)
echo "当前版本: $current_version"

echo "选择更新类型:"
echo "1) 修订版本 (bug修复)"
echo "2) 次版本 (新功能)"
echo "3) 主版本 (重大更改)"

read -p "请选择 (1-3): " choice

case $choice in
    1)
        new_version=$(echo $current_version | awk -F. '{$NF = $NF + 1;} 1' | sed 's/ /./g')
        ;;
    2)
        new_version=$(echo $current_version | awk -F. '{$(NF-1) = $(NF-1) + 1; $NF = 0;} 1' | sed 's/ /./g')
        ;;
    3)
        new_version=$(echo $current_version | awk -F. '{$1 = $1 + 1; $2 = 0; $3 = 0;} 1' | sed 's/ /./g')
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo "新版本: $new_version"
read -p "确认更新? (y/N): " confirm

if [[ $confirm == [yY] ]]; then
    # 更新manifest.json
    jq ".version = \"$new_version\"" moment-search-v3/manifest.json > temp.json
    mv temp.json moment-search-v3/manifest.json
    
    # 更新README.md中的版本信息
    sed -i "s/V[0-9]\+\.[0-9]\+\.[0-9]\+/V$new_version/g" moment-search-v3/README.md
    
    echo "✅ 版本已更新到 $new_version"
    
    # 创建Git标签
    git add .
    git commit -m "chore: bump version to $new_version"
    git tag "v$new_version"
    
    echo "🏷️ Git标签已创建: v$new_version"
else
    echo "❌ 版本更新已取消"
fi
```

## 🌐 发布渠道

### 1. Chrome Web Store

#### 发布准备

```bash
# 1. 创建商店发布包
create-store-package.sh

#!/bin/bash
echo "📦 创建Chrome Web Store发布包..."

# 复制文件到发布目录
mkdir -p release/chrome-store/
cp -r dist/* release/chrome-store/

# 移除开发文件
rm -f release/chrome-store/*.md
rm -rf release/chrome-store/test/

# 创建zip包
cd release/chrome-store/
zip -r "../moment-search-chrome-$(jq -r '.version' manifest.json).zip" .
cd ../..

echo "✅ Chrome Web Store包已创建"
```

#### 发布清单

- [ ] 扩展图标 (16x16, 48x48, 128x128)
- [ ] 应用截图 (1280x800 或 640x400)
- [ ] 详细描述和功能说明
- [ ] 隐私政策链接
- [ ] 支持联系方式

### 2. Microsoft Edge Add-ons

```bash
# 创建Edge发布包
create-edge-package.sh

#!/bin/bash
echo "📦 创建Edge Add-ons发布包..."

mkdir -p release/edge-store/
cp -r dist/* release/edge-store/

# Edge特定优化
# 更新manifest中的描述
jq '.description = .description + " - 兼容Microsoft Edge"' release/edge-store/manifest.json > temp.json
mv temp.json release/edge-store/manifest.json

# 创建zip包
cd release/edge-store/
zip -r "../moment-search-edge-$(jq -r '.version' manifest.json).zip" .
cd ../..

echo "✅ Edge Add-ons包已创建"
```

### 3. 自托管发布

```bash
# 创建自托管包
create-self-hosted.sh

#!/bin/bash
echo "🏠 创建自托管发布包..."

mkdir -p release/self-hosted/
cp -r dist/* release/self-hosted/

# 添加安装说明
cp INSTALL.md release/self-hosted/
cp README.md release/self-hosted/

# 创建完整包
cd release/self-hosted/
zip -r "../moment-search-self-hosted-$(jq -r '.version' manifest.json).zip" .
cd ../..

# 生成更新清单
cat > release/updates.xml << EOF
<?xml version='1.0' encoding='UTF-8'?>
<gupdate xmlns='http://www.google.com/update2/response' protocol='2.0'>
  <app appid='$(jq -r '.key' dist/manifest.json)'>
    <updatecheck codebase='https://your-domain.com/moment-search-latest.crx' version='$(jq -r '.version' dist/manifest.json)' />
  </app>
</gupdate>
EOF

echo "✅ 自托管包已创建"
```

## 🔍 质量保证

### 自动化测试流程

```javascript
// test/qa-automation.js
class QAAutomation {
    static async runFullQASuite() {
        console.log('🔍 开始完整QA测试...');
        
        const testSuites = [
            { name: '功能测试', test: FunctionalTest.runAllTests },
            { name: '性能测试', test: PerformanceTest.runPerformanceTests },
            { name: '兼容性测试', test: CompatibilityTest.runTests },
            { name: '安全测试', test: SecurityTest.runTests }
        ];
        
        const results = [];
        
        for (const suite of testSuites) {
            console.log(`\n🧪 运行${suite.name}...`);
            try {
                const result = await suite.test();
                results.push({ name: suite.name, passed: result });
                console.log(`✅ ${suite.name}完成`);
            } catch (error) {
                results.push({ name: suite.name, passed: false, error });
                console.error(`❌ ${suite.name}失败:`, error);
            }
        }
        
        // 生成测试报告
        this.generateTestReport(results);
        
        return results.every(r => r.passed);
    }
    
    static generateTestReport(results) {
        const report = {
            timestamp: new Date().toISOString(),
            version: document.querySelector('meta[name="version"]')?.content || 'unknown',
            results: results,
            summary: {
                total: results.length,
                passed: results.filter(r => r.passed).length,
                failed: results.filter(r => !r.passed).length
            }
        };
        
        // 保存报告
        const reportJson = JSON.stringify(report, null, 2);
        console.log('📊 测试报告:', reportJson);
        
        // 可以发送到监控系统
        // this.sendToMonitoring(report);
    }
}
```

### 发布前检查清单

```markdown
## 发布前检查清单

### 代码质量
- [ ] 所有功能测试通过
- [ ] 性能测试达标 (加载<300ms, 内存<30MB)
- [ ] 无JavaScript错误
- [ ] 无CSS样式问题
- [ ] 代码已格式化和优化

### 功能验证
- [ ] 搜索功能正常 (所有平台)
- [ ] 快捷方式管理正常 (增删改查)
- [ ] 设置面板功能完整
- [ ] 数据导入导出正常
- [ ] 主题切换正常
- [ ] 拖拽排序正常

### 兼容性测试
- [ ] Chrome 88+ 测试通过
- [ ] Edge 88+ 测试通过
- [ ] 不同屏幕分辨率适配正常
- [ ] 不同操作系统测试通过

### 安全检查
- [ ] CSP策略正确配置
- [ ] 权限申请最小化
- [ ] 无XSS漏洞
- [ ] 用户数据安全

### 文档和资源
- [ ] README.md 更新
- [ ] CHANGELOG.md 更新
- [ ] 版本号正确
- [ ] 图标和截图准备完毕
```

## 📊 监控和维护

### 错误监控

```javascript
// monitoring/error-tracking.js
class ErrorTracking {
    static init() {
        // 全局错误捕获
        window.addEventListener('error', this.handleError);
        window.addEventListener('unhandledrejection', this.handlePromiseError);
        
        // 定期发送错误报告
        setInterval(() => {
            this.sendErrorReport();
        }, 300000); // 5分钟
    }
    
    static handleError(event) {
        const error = {
            type: 'javascript-error',
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error?.stack,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            version: chrome.runtime.getManifest().version
        };
        
        this.logError(error);
    }
    
    static logError(error) {
        // 本地存储错误日志
        const errors = JSON.parse(localStorage.getItem('error-logs') || '[]');
        errors.push(error);
        
        // 保持最近100条错误
        if (errors.length > 100) {
            errors.splice(0, errors.length - 100);
        }
        
        localStorage.setItem('error-logs', JSON.stringify(errors));
    }
}

// 初始化错误监控
ErrorTracking.init();
```

### 使用统计

```javascript
// monitoring/usage-analytics.js
class UsageAnalytics {
    static init() {
        this.startSession();
        this.trackEvents();
    }
    
    static startSession() {
        const session = {
            id: this.generateSessionId(),
            startTime: Date.now(),
            version: chrome.runtime.getManifest().version,
            userAgent: navigator.userAgent
        };
        
        localStorage.setItem('current-session', JSON.stringify(session));
    }
    
    static trackEvent(eventName, data = {}) {
        const event = {
            name: eventName,
            data: data,
            timestamp: Date.now(),
            sessionId: JSON.parse(localStorage.getItem('current-session'))?.id
        };
        
        // 存储事件
        const events = JSON.parse(localStorage.getItem('usage-events') || '[]');
        events.push(event);
        
        // 保持最近1000个事件
        if (events.length > 1000) {
            events.splice(0, events.length - 1000);
        }
        
        localStorage.setItem('usage-events', JSON.stringify(events));
    }
}
```

### 自动更新机制

Chrome扩展的更新由浏览器自动处理，但我们需要处理数据迁移：

```javascript
// background.js 中的更新处理
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'update') {
        const previousVersion = details.previousVersion;
        const currentVersion = chrome.runtime.getManifest().version;
        
        console.log(`从版本 ${previousVersion} 更新到 ${currentVersion}`);
        
        // 执行数据迁移
        handleDataMigration(previousVersion, currentVersion);
    }
});

function handleDataMigration(fromVersion, toVersion) {
    // 版本比较和迁移逻辑
    if (compareVersions(fromVersion, '3.0.0') < 0) {
        // 从2.x版本迁移到3.x
        migrateFromV2();
    }
    
    if (compareVersions(fromVersion, '3.1.0') < 0) {
        // 从3.0.x迁移到3.1.x
        migrateToV31();
    }
}
```

---

**文档版本**: V1.0  
**创建时间**: 2025-01-22  
**维护团队**: Moment Search Development Team
