/**
 * 链接预览事件管理器
 * 负责处理全局事件监听和键盘状态管理
 */
class LinkPreviewEventManager {
    constructor(manager) {
        this.manager = manager;
        this.keyboardState = {
            alt: false,
            ctrl: false,
            shift: false,
            meta: false
        };
        this.boundHandlers = new Map();
        this.isEnabled = true;
    }

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        if (!this.isEnabled) return;

        // 键盘状态跟踪
        this.bindKeyboardEvents();
        
        // 链接点击事件
        this.bindLinkClickEvents();
        
        // 全局快捷键
        this.bindGlobalShortcuts();

        console.log('✅ 链接预览事件管理器已绑定全局事件');
    }

    /**
     * 绑定键盘事件，跟踪修饰键状态
     */
    bindKeyboardEvents() {
        const keydownHandler = (e) => {
            this.updateKeyboardState(e);
        };

        const keyupHandler = (e) => {
            this.updateKeyboardState(e);
        };

        // 窗口失焦时重置键盘状态
        const blurHandler = () => {
            this.resetKeyboardState();
        };

        document.addEventListener('keydown', keydownHandler, true);
        document.addEventListener('keyup', keyupHandler, true);
        window.addEventListener('blur', blurHandler);
        
        this.boundHandlers.set('keydown', keydownHandler);
        this.boundHandlers.set('keyup', keyupHandler);
        this.boundHandlers.set('blur', blurHandler);
    }

    /**
     * 更新键盘状态
     */
    updateKeyboardState(event) {
        this.keyboardState.alt = event.altKey;
        this.keyboardState.ctrl = event.ctrlKey;
        this.keyboardState.shift = event.shiftKey;
        this.keyboardState.meta = event.metaKey;
    }

    /**
     * 重置键盘状态
     */
    resetKeyboardState() {
        this.keyboardState = {
            alt: false,
            ctrl: false,
            shift: false,
            meta: false
        };
    }

    /**
     * 绑定链接点击事件
     */
    bindLinkClickEvents() {
        const clickHandler = (e) => {
            if (!this.isEnabled) return;

            // 检查是否按下了触发修饰键
            if (this.isModifierPressed(e)) {
                const link = e.target.closest('a[href]');
                if (link && LinkUtils.isValidLink(link)) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 创建预览窗口
                    this.manager.createPreview(link.href, {
                        triggerElement: link,
                        triggerEvent: e
                    });
                }
            }
        };

        // 使用捕获阶段确保能够拦截链接点击
        document.addEventListener('click', clickHandler, true);
        this.boundHandlers.set('click', clickHandler);
    }

    /**
     * 绑定全局快捷键
     */
    bindGlobalShortcuts() {
        const shortcutHandler = (e) => {
            if (!this.isEnabled) return;

            switch (e.key) {
                case 'Escape':
                    // ESC 关闭所有预览窗口
                    if (this.manager.previewWindows.size > 0) {
                        e.preventDefault();
                        this.manager.destroyAllPreviews();
                    }
                    break;
                    
                case 'Tab':
                    // Tab 键切换预览窗口焦点
                    if (this.manager.previewWindows.size > 1) {
                        e.preventDefault();
                        this.manager.focusNextWindow();
                    }
                    break;
                    
                case 'w':
                case 'W':
                    // Ctrl+W 关闭当前焦点窗口
                    if ((e.ctrlKey || e.metaKey) && this.manager.previewWindows.size > 0) {
                        e.preventDefault();
                        this.manager.closeFocusedWindow();
                    }
                    break;
            }
        };

        document.addEventListener('keydown', shortcutHandler);
        this.boundHandlers.set('shortcuts', shortcutHandler);
    }

    /**
     * 检查是否按下了触发修饰键
     */
    isModifierPressed(event) {
        const config = this.manager.config;
        const modifier = config?.shortcuts?.modifier || 'alt';
        
        switch (modifier.toLowerCase()) {
            case 'alt':
                return event.altKey && !event.ctrlKey && !event.shiftKey;
            case 'ctrl':
                return (event.ctrlKey || event.metaKey) && !event.altKey && !event.shiftKey;
            case 'shift':
                return event.shiftKey && !event.altKey && !event.ctrlKey;
            case 'ctrl+alt':
                return (event.ctrlKey || event.metaKey) && event.altKey;
            case 'ctrl+shift':
                return (event.ctrlKey || event.metaKey) && event.shiftKey;
            case 'alt+shift':
                return event.altKey && event.shiftKey;
            default:
                return event.altKey; // 默认使用 Alt 键
        }
    }



    /**
     * 启用事件管理器
     */
    enable() {
        this.isEnabled = true;
        console.log('✅ 链接预览事件管理器已启用');
    }

    /**
     * 禁用事件管理器
     */
    disable() {
        this.isEnabled = false;
        console.log('⏸️ 链接预览事件管理器已禁用');
    }

    /**
     * 解绑所有事件
     */
    unbindAllEvents() {
        this.boundHandlers.forEach((handler, eventType) => {
            switch (eventType) {
                case 'keydown':
                case 'keyup':
                case 'click':
                case 'shortcuts':
                    document.removeEventListener(eventType, handler, true);
                    break;
                case 'blur':
                    window.removeEventListener(eventType, handler);
                    break;
            }
        });
        
        this.boundHandlers.clear();
        console.log('🧹 链接预览事件管理器已清理所有事件监听器');
    }

    /**
     * 获取当前键盘状态
     */
    getKeyboardState() {
        return { ...this.keyboardState };
    }

    /**
     * 检查特定修饰键是否被按下
     */
    isKeyPressed(key) {
        return this.keyboardState[key.toLowerCase()] || false;
    }

    /**
     * 更新配置
     */
    updateConfig(config) {
        // 配置更新时可能需要重新绑定事件
        console.log('🔄 链接预览事件管理器配置已更新');
    }

    /**
     * 销毁事件管理器
     */
    destroy() {
        this.unbindAllEvents();
        this.resetKeyboardState();
        this.manager = null;
        console.log('💥 链接预览事件管理器已销毁');
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.LinkPreviewEventManager = LinkPreviewEventManager;
}
