/**
 * 桌面级拖拽排序管理器
 * 实现标准的桌面应用拖拽交互体验
 */

class DesktopDragManager {
  constructor(shortcutManager) {
    this.shortcutManager = shortcutManager;

    // 拖拽状态
    this.isDragging = false;
    this.dragElement = null;
    this.dragGhost = null;

    // 鼠标和位置信息
    this.mouseOffset = { x: 0, y: 0 };
    this.startPosition = { x: 0, y: 0 };
    this.currentPosition = { x: 0, y: 0 };

    // 网格布局信息
    this.gridContainer = null;
    this.gridItems = [];
    this.gridColumns = 0;
    this.itemWidth = 0;
    this.itemHeight = 0;

    // 插入位置
    this.insertIndex = -1;
    this.originalIndex = -1;

    // 文件夹创建相关
    this.hoverTarget = null;
    this.hoverTimer = null;
    this.hoverDelay = 500; // 悬停500ms后准备创建文件夹
    this.folderManager = null;

    // 事件处理器引用
    this.boundHandlers = {
      mouseMove: null,
      mouseUp: null
    };

    // 性能优化标志
    this.layoutUpdatePending = false;

    // 常量定义
    this.PROXIMITY_THRESHOLD_RATIO = 1.5; // 响应阈值：图标宽度的1.5倍
    this.TOLERANCE_DISTANCE = 15; // 原位置容错距离（像素）
  }

  init() {
    this.gridContainer = document.getElementById('shortcutsGrid');
    if (!this.gridContainer) {
      console.warn('未找到快捷方式容器');
      return;
    }

    // 初始化文件夹管理器
    if (typeof FolderManager !== 'undefined') {
      this.folderManager = new FolderManager(this.shortcutManager);
    }

    this.calculateGridLayout();
    this.bindEvents();
    console.log('桌面级拖拽管理器初始化完成');
  }

  calculateGridLayout() {
    // 计算网格布局参数
    const containerRect = this.gridContainer.getBoundingClientRect();
    const containerStyle = getComputedStyle(this.gridContainer);
    
    // 获取CSS Grid的列数
    const gridTemplateColumns = containerStyle.gridTemplateColumns;
    this.gridColumns = gridTemplateColumns.split(' ').length;
    
    // 计算单个图标的尺寸
    const firstItem = this.gridContainer.querySelector('.shortcut-item:not(.shortcut-add-button)');
    if (firstItem) {
      const itemRect = firstItem.getBoundingClientRect();
      this.itemWidth = itemRect.width;
      this.itemHeight = itemRect.height;
    }
    
    // 网格布局参数计算完成
  }

  bindEvents() {
    // 为每个图标绑定鼠标事件
    this.gridContainer.addEventListener('mousedown', (e) => this.handleMouseDown(e));
    
    // 绑定文档级别的事件
    this.boundHandlers.mouseMove = (e) => this.handleMouseMove(e);
    this.boundHandlers.mouseUp = (e) => this.handleMouseUp(e);
    
    document.addEventListener('mousemove', this.boundHandlers.mouseMove);
    document.addEventListener('mouseup', this.boundHandlers.mouseUp);
    
    // 防止默认的拖拽行为
    this.gridContainer.addEventListener('dragstart', (e) => e.preventDefault());
  }

  handleMouseDown(e) {
    // 只处理快捷方式图标
    const shortcutItem = e.target.closest('.shortcut-item');
    if (!shortcutItem || shortcutItem.classList.contains('shortcut-add-button')) return;

    // 只响应左键
    if (e.button !== 0) return;

    // 如果已经在拖拽中，忽略新的mousedown事件
    if (this.isDragging) {
      return;
    }

    // 阻止默认行为
    e.preventDefault();

    // 立即清理所有可能的残影
    this.removeAllDragGhosts();

    // 开始拖拽（延迟隐藏原图标，避免闪烁）
    this.startDrag(shortcutItem, e);
  }

  startDrag(element, event) {
    this.isDragging = true;
    this.dragElement = element;

    // 一次性获取图标容器和位置信息，避免重复查询
    const iconContainer = element.querySelector('.shortcut-icon');
    if (!iconContainer) {
      console.error('未找到图标容器');
      return;
    }

    const iconRect = iconContainer.getBoundingClientRect();
    this.mouseOffset = {
      x: event.clientX - iconRect.left,
      y: event.clientY - iconRect.top
    };

    // 记录起始位置
    this.startPosition = { x: event.clientX, y: event.clientY };
    this.currentPosition = { x: event.clientX, y: event.clientY };

    // 获取原始索引
    this.gridItems = [...this.gridContainer.querySelectorAll('.shortcut-item:not(.shortcut-add-button)')];
    this.originalIndex = this.gridItems.indexOf(element);
    this.insertIndex = this.originalIndex;

    // 设置拖拽状态样式
    document.body.style.cursor = 'grabbing';
    document.body.classList.add('dragging');

    // 创建拖拽幽灵，传递已获取的容器和位置信息
    this.createDragGhost(iconContainer, iconRect);

    // 在拖拽幽灵创建完成后完全隐藏原图标，避免幽灵影子
    requestAnimationFrame(() => {
      if (this.isDragging && this.dragElement) {
        this.dragElement.style.opacity = '0';
        this.dragElement.style.visibility = 'hidden';
      }
    });

    // 拖拽开始
  }

  createDragGhost(iconContainer, iconRect) {
    // 先清理任何可能存在的旧拖拽幽灵
    this.removeAllDragGhosts();

    // 克隆图标容器（容器和位置信息已从startDrag传入，避免重复查询）
    this.dragGhost = iconContainer.cloneNode(true);
    this.dragGhost.classList.add('drag-ghost');

    // 计算初始位置
    const initialX = this.currentPosition.x - this.mouseOffset.x;
    const initialY = this.currentPosition.y - this.mouseOffset.y;

    // 设置拖拽幽灵的基本样式，使用transform实现硬件加速
    this.dragGhost.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: ${iconRect.width}px;
      height: ${iconRect.height}px;
      z-index: 10000;
      pointer-events: none;
      opacity: 0.9;
      transform: translate3d(${initialX}px, ${initialY}px, 0);
      will-change: transform;
    `;

    // 添加到DOM
    document.body.appendChild(this.dragGhost);

    // 拖拽幽灵创建完成
  }

  removeAllDragGhosts() {
    // 移除所有可能存在的拖拽幽灵，防止残影
    const existingGhosts = document.querySelectorAll('.drag-ghost');
    existingGhosts.forEach(ghost => ghost.remove());
    this.dragGhost = null;
  }

  handleMouseMove(e) {
    if (!this.isDragging || !this.dragGhost) return;

    // 更新当前位置
    this.currentPosition = { x: e.clientX, y: e.clientY };

    // 立即更新拖拽幽灵位置（保持流畅跟随）
    this.updateGhostPosition();

    // 使用requestAnimationFrame优化布局更新，避免过度计算
    if (!this.layoutUpdatePending) {
      this.layoutUpdatePending = true;
      requestAnimationFrame(() => {
        if (this.isDragging) {
          // 检测悬停目标（用于文件夹创建）
          this.detectHoverTarget(e);

          // 计算插入位置
          this.calculateInsertPosition(e);

          // 更新布局
          this.updateLayout();
        }
        this.layoutUpdatePending = false;
      });
    }
  }

  updateGhostPosition() {
    if (!this.dragGhost) return;

    // 使用transform进行硬件加速的位置更新
    const x = this.currentPosition.x - this.mouseOffset.x;
    const y = this.currentPosition.y - this.mouseOffset.y;

    // 使用translate3d实现硬件加速的流畅移动
    this.dragGhost.style.transform = `translate3d(${x}px, ${y}px, 0)`;
  }

  calculateInsertPosition(event) {
    // 简化的插入位置计算逻辑

    // 获取容器位置信息
    this.containerRect = this.gridContainer.getBoundingClientRect();

    // 检查是否在容器范围内（放宽边界检查）
    const relativeX = event.clientX - this.containerRect.left;
    const relativeY = event.clientY - this.containerRect.top;

    // 放宽边界检查：允许在容器左侧一定范围内拖拽
    const leftMargin = 100; // 增大左侧边界容错范围
    if (relativeX < -leftMargin || relativeY < 0 ||
        relativeX > this.containerRect.width + leftMargin ||
        relativeY > this.containerRect.height) {
      this.insertIndex = this.originalIndex;
      return;
    }

    // 简化的原位置容错检查（减小到5px）
    if (!this.originalCenter) {
      const originalItemRect = this.dragElement.getBoundingClientRect();
      this.originalCenter = {
        x: originalItemRect.left + originalItemRect.width / 2,
        y: originalItemRect.top + originalItemRect.height / 2
      };
    }

    const dxOriginal = event.clientX - this.originalCenter.x;
    const dyOriginal = event.clientY - this.originalCenter.y;
    const distanceToOriginal = Math.sqrt(dxOriginal * dxOriginal + dyOriginal * dyOriginal);

    // 减小容错范围到3px，提高精度
    if (distanceToOriginal <= 3) {
      this.insertIndex = this.originalIndex;
      return;
    }

    // 修正的位置计算：考虑拖拽元素移除后的索引调整
    let newIndex = this.originalIndex; // 默认保持原位置

    // 使用绝对坐标进行计算，避免容器边界问题
    const mouseX = event.clientX;

    // 遍历所有图标，找到最合适的插入位置
    for (let i = 0; i < this.gridItems.length; i++) {
      if (this.gridItems[i] === this.dragElement) continue;

      const itemRect = this.gridItems[i].getBoundingClientRect();
      const itemCenter = itemRect.left + itemRect.width / 2;

      // 如果鼠标在图标左半部分，插入到该图标前面
      if (mouseX < itemCenter) {
        newIndex = i;
        break;
      }
      // 如果是最后一个图标且鼠标在其右侧，插入到末尾
      else if (i === this.gridItems.length - 1) {
        newIndex = this.gridItems.length;
      }
    }

    // 保存视觉插入位置（用于让位逻辑）
    this.visualInsertIndex = newIndex;

    // 关键修复：调整索引以考虑拖拽元素的移除（用于最终数组操作）
    // 当向后拖拽时（newIndex > originalIndex），需要减1
    // 因为原元素被移除后，后面的元素索引都会前移
    if (newIndex > this.originalIndex) {
      newIndex--;
    }

    // 边界检查并更新插入索引
    const finalIndex = Math.max(0, Math.min(newIndex, this.gridItems.length - 1));
    if (finalIndex !== this.insertIndex) {
      this.insertIndex = finalIndex;
    }
  }

  updateLayout() {
    const transition = 'transform 0.15s cubic-bezier(0.4, 0, 0.2, 1)';

    // 如果插入到原位置，清理所有变换
    if (this.insertIndex === this.originalIndex) {
      this.gridItems.forEach(item => {
        if (item !== this.dragElement) {
          item.style.transform = '';
          item.style.transition = transition;
        }
      });
      return;
    }

    // 计算移动距离
    const itemGap = parseInt(getComputedStyle(this.gridContainer).gap) || 24;
    const moveDistance = this.itemWidth + itemGap;

    // 修复的让位逻辑：使用视觉插入位置进行判断
    this.gridItems.forEach((item, index) => {
      if (item === this.dragElement) return;

      let offset = 0;

      // 使用视觉插入位置进行让位判断
      const visualIndex = this.visualInsertIndex ?? this.insertIndex;

      if (visualIndex < this.originalIndex) {
        // 向前移动：原位置前的图标需要向右让位
        if (index >= visualIndex && index < this.originalIndex) {
          offset = moveDistance;
        }
      } else if (visualIndex > this.originalIndex) {
        // 向后移动：原位置后的图标需要向左让位
        if (index > this.originalIndex && index < visualIndex) {
          offset = -moveDistance;
        }
      }

      // 应用变换
      const targetTransform = offset !== 0 ? `translateX(${offset}px)` : '';
      if (item.style.transform !== targetTransform) {
        item.style.transform = targetTransform;
        item.style.transition = transition;
      }
    });
  }



  handleMouseUp(e) {
    if (!this.isDragging) return;

    // 检查是否有悬停目标（用于创建文件夹）
    if (this.hoverTarget && this.folderManager) {
      const sourceId = this.dragElement.dataset.id;
      const targetId = this.hoverTarget.dataset.id;

      // 获取源和目标的类型
      const sourceType = this.dragElement.dataset.type;
      const targetType = this.hoverTarget.dataset.type;

      console.log(`检测到拖拽操作: ${sourceId}(${sourceType}) -> ${targetId}(${targetType})`);

      // 验证拖拽操作的有效性
      if (sourceType === 'folder' && targetType === 'folder') {
        console.warn('不允许将文件夹拖拽到另一个文件夹上');
        this.cleanup();
        return;
      }

      if (sourceType === 'folder' && targetType === 'shortcut') {
        console.warn('不允许将文件夹拖拽到图标上');
        this.cleanup();
        return;
      }

      // 只允许图标拖拽到图标上创建文件夹，或图标拖拽到文件夹上添加到文件夹
      if (sourceType === 'shortcut' && targetType === 'shortcut') {
        console.log(`创建文件夹: ${sourceId} -> ${targetId}`);

        // 创建文件夹
        const folder = this.folderManager.createFolder(sourceId, targetId);

        if (folder) {
          console.log('文件夹创建成功:', folder.name);

          // 清理拖拽状态
          this.cleanup();

          return;
        }
      } else if (sourceType === 'shortcut' && targetType === 'folder') {
        console.log(`添加图标到文件夹: ${sourceId} -> ${targetId}`);

        // 添加图标到文件夹
        const success = this.folderManager.addToFolder(targetId, sourceId);

        if (success) {
          console.log('图标已添加到文件夹');
          this.cleanup();
          return;
        }
      }
    }

    const isBackToOriginal = this.insertIndex === this.originalIndex;
    console.log(`完成拖拽: 从索引 ${this.originalIndex} 移动到 ${this.insertIndex}${isBackToOriginal ? ' (回到原位)' : ''}`);

    // 执行实际的重排序（即使是回到原位也要执行，确保状态一致性）
    if (this.insertIndex !== this.originalIndex) {
      this.performReorder();
    } else {
      // 回到原位的情况
      console.log('图标已放回原位，无需重排序');
    }

    // 清理拖拽状态
    this.cleanup();
  }



  performReorder() {
    // 调用ShortcutManager执行重排序
    const draggedId = this.dragElement.dataset.id;
    const success = this.shortcutManager.reorderToIndex(draggedId, this.insertIndex);
    
    if (success) {
      console.log('重排序成功');
    } else {
      console.error('重排序失败');
    }
  }

  cleanup() {
    // 立即恢复原始元素的可见性和样式，禁用动画
    if (this.dragElement) {
      // 先禁用过渡动画
      this.dragElement.style.transition = 'none';
      // 立即恢复样式
      this.dragElement.style.opacity = '';
      this.dragElement.style.visibility = '';
      this.dragElement.style.pointerEvents = '';
      // 强制重绘
      this.dragElement.offsetHeight;
      // 移除过渡禁用
      this.dragElement.style.transition = '';
    }

    // 彻底移除所有拖拽幽灵，防止残影
    this.removeAllDragGhosts();

    // 立即清理所有图标的变换，禁用动画
    if (this.gridItems) {
      this.gridItems.forEach(item => {
        // 先禁用过渡动画，避免清理时的动画效果
        item.style.transition = 'none';
        // 立即清理变换
        item.style.transform = '';
        // 强制重绘，确保变化立即生效
        item.offsetHeight;
        // 移除过渡禁用
        item.style.transition = '';
      });
    }

    // 清理文件夹相关状态
    this.clearHoverTarget();

    // 恢复全局状态
    document.body.style.cursor = '';
    document.body.classList.remove('dragging');

    // 重置状态和清理缓存
    this.isDragging = false;
    this.dragElement = null;
    this.insertIndex = -1;
    this.originalIndex = -1;
    this.visualInsertIndex = null; // 清理视觉插入位置

    // 清理缓存的位置信息
    this.containerRect = null;
    this.originalCenter = null;

    // 重置性能优化标志
    this.layoutUpdatePending = false;
  }

  destroy() {
    // 清理事件监听器
    if (this.boundHandlers.mouseMove) {
      document.removeEventListener('mousemove', this.boundHandlers.mouseMove);
    }
    if (this.boundHandlers.mouseUp) {
      document.removeEventListener('mouseup', this.boundHandlers.mouseUp);
    }

    this.cleanup();
    console.log('桌面级拖拽管理器已销毁');
  }

  /**
   * 检测悬停目标（用于文件夹创建）
   * @param {MouseEvent} event 鼠标事件
   */
  detectHoverTarget(event) {
    if (!this.folderManager) return;

    // 临时隐藏拖拽幽灵，避免遮挡目标检测
    const originalStyles = this.dragGhost ? {
      display: this.dragGhost.style.display,
      pointerEvents: this.dragGhost.style.pointerEvents
    } : null;

    if (this.dragGhost) {
      this.dragGhost.style.display = 'none';
      this.dragGhost.style.pointerEvents = 'none';
    }

    // 获取鼠标下的元素
    const elementUnderMouse = document.elementFromPoint(event.clientX, event.clientY);
    const targetItem = elementUnderMouse?.closest('.shortcut-item:not(.shortcut-add-button)');

    // 恢复拖拽幽灵显示
    if (this.dragGhost && originalStyles) {
      this.dragGhost.style.display = originalStyles.display;
      this.dragGhost.style.pointerEvents = originalStyles.pointerEvents;
    }

    // 如果没有目标或目标是自己，清除悬停状态
    if (!targetItem || targetItem === this.dragElement) {
      this.clearHoverTarget();
      return;
    }

    const targetId = targetItem.dataset.id;
    const sourceId = this.dragElement.dataset.id;

    // 检查是否可以创建文件夹
    if (!this.folderManager.canCreateFolder(sourceId, targetId)) {
      this.clearHoverTarget();
      return;
    }

    // 如果是新的悬停目标
    if (this.hoverTarget !== targetItem) {
      this.clearHoverTarget();
      this.hoverTarget = targetItem;

      // 只对图标区域添加简单的高亮效果
      const iconElement = targetItem.querySelector('.shortcut-icon');
      if (iconElement) {
        iconElement.classList.add('folder-merge-highlight');
      }
    }
  }

  /**
   * 清除悬停目标状态
   */
  clearHoverTarget() {
    if (this.hoverTarget) {
      // 清除图标高亮效果
      const iconElement = this.hoverTarget.querySelector('.shortcut-icon');
      if (iconElement) {
        iconElement.classList.remove('folder-merge-highlight');
      }

      this.hoverTarget = null;
    }

    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer);
      this.hoverTimer = null;
    }
  }



  /**
   * 在拖拽放置时创建文件夹
   * @param {string} sourceId 源图标ID
   * @param {string} targetId 目标图标ID
   */
  createFolderOnDrop(sourceId, targetId) {
    if (!this.folderManager) return;

    // 创建文件夹
    const folder = this.folderManager.createFolder(sourceId, targetId);

    if (folder) {
      console.log('文件夹创建成功:', folder.name);

      // 清理拖拽状态
      this.cleanup();
    }
  }


}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DesktopDragManager;
}
