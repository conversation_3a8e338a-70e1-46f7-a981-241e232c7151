# Moment Search V3 - 架构设计文档

> 详细的系统架构设计和模块组织说明

## 🏗️ 整体架构

### 架构模式
Moment Search V3 采用**模块化单页应用**架构，基于以下设计原则：

- **单一职责**: 每个模块负责特定功能领域
- **松耦合**: 模块间通过事件和接口通信
- **高内聚**: 相关功能集中在同一模块内
- **可扩展**: 支持插件系统和功能扩展

### 系统层次结构

```
┌─────────────────────────────────────────┐
│              用户界面层 (UI Layer)           │
├─────────────────────────────────────────┤
│            业务逻辑层 (Business Layer)      │
├─────────────────────────────────────────┤
│            数据访问层 (Data Layer)          │
├─────────────────────────────────────────┤
│            浏览器API层 (Browser API)       │
└─────────────────────────────────────────┘
```

## 🧩 核心模块架构

### 主应用类 (NewTabApp)

```javascript
class NewTabApp {
    constructor() {
        // 核心管理器实例
        this.timeManager = null;
        this.searchManager = null;
        this.searchModeManager = null;
        this.shortcutManager = null;
        this.settingsManager = null;
        this.backgroundManager = null;
        this.pageManager = null;
    }
}
```

**职责**:
- 应用程序生命周期管理
- 模块初始化和依赖注入
- 全局事件协调
- 错误处理和恢复

### 1. 搜索管理器 (SearchManager)

```javascript
class SearchManager {
    constructor(platforms = PLATFORMS) {
        this.platforms = platforms;
        this.aliasMap = ALIAS_MAP;
        this.currentPlatform = 'all';
        this.platformManager = new PlatformManager();
        this.searchMode = 'normal'; // 'normal' | 'quick'
    }
}
```

**核心功能**:
- 多平台搜索执行
- 智能别名匹配
- 搜索历史管理
- 平台切换和管理

**关键方法**:
- `executeSearch(query, platform)` - 执行搜索
- `matchAlias(query)` - 别名匹配
- `addToHistory(query, platform)` - 添加历史记录
- `switchPlatform(platformId)` - 切换搜索平台

### 2. 快捷方式管理器 (ShortcutManager)

```javascript
class ShortcutManager {
    constructor() {
        this.shortcuts = [];
        this.maxShortcuts = 60;
        this.container = document.getElementById('shortcutsGrid');
        this.faviconManager = new FaviconManager();
        this.iconCacheManager = new IconCacheManager();
        this.dragDropManager = null;
    }
}
```

**核心功能**:
- 快捷方式CRUD操作
- 拖拽排序功能
- 图标获取和缓存
- 批量导入导出

**关键方法**:
- `addShortcut(name, url, icon)` - 添加快捷方式
- `removeShortcut(id)` - 删除快捷方式
- `updateShortcut(id, data)` - 更新快捷方式
- `reorderShortcuts(fromIndex, toIndex)` - 重新排序

### 3. 设置管理器 (SettingsManager)

```javascript
class SettingsManager {
    constructor() {
        this.settings = this.getDefaultSettings();
        this.panel = document.getElementById('settingsPanel');
        this.content = document.getElementById('settingsContent');
        this.currentSection = 'appearance';
    }
}
```

**核心功能**:
- 用户设置管理
- 设置面板UI控制
- 主题和样式应用
- 配置持久化

**设置结构**:
```javascript
{
    theme: {
        mode: 'auto',        // 'light' | 'dark' | 'auto'
        system: true,        // 跟随系统主题
        color: '#1681ff'     // 主题色
    },
    shortcuts: {
        showNames: true,     // 显示快捷方式名称
        iconSize: 60,        // 图标大小
        iconSpacing: 16,     // 图标间距
        containerWidth: 80   // 容器宽度百分比
    },
    search: {
        defaultPlatform: 'google',  // 默认搜索平台
        enableHistory: true,        // 启用搜索历史
        historyLimit: 50           // 历史记录限制
    }
}
```

### 4. 背景管理器 (BackgroundManager)

```javascript
class BackgroundManager {
    constructor() {
        this.currentWallpaper = null;
        this.wallpaperCache = new Map();
        this.backgroundLayer = document.getElementById('backgroundLayer');
    }
}
```

**核心功能**:
- 壁纸加载和显示
- 背景图片缓存
- 主题色提取
- 动态背景效果

### 5. 页面管理器 (PageManager)

```javascript
class PageManager {
    constructor() {
        this.pages = [];
        this.currentPageIndex = 0;
        this.maxPagesPerMode = 8;
    }
}
```

**核心功能**:
- 多页面快捷方式组织
- 页面切换和导航
- 搜索模式管理
- 页面数据持久化

## 🔄 数据流架构

### 数据流向图

```
用户操作 → 事件处理 → 业务逻辑 → 数据更新 → UI渲染 → 本地存储
    ↑                                                      ↓
    └──────────────── 状态同步 ←─────────────────────────────┘
```

### 事件系统

```javascript
class GlobalEventManager {
    static events = new Map();
    
    static on(event, callback) {
        if (!this.events.has(event)) {
            this.events.set(event, []);
        }
        this.events.get(event).push(callback);
    }
    
    static emit(event, data) {
        if (this.events.has(event)) {
            this.events.get(event).forEach(callback => callback(data));
        }
    }
}
```

**核心事件**:
- `shortcut:added` - 快捷方式添加
- `shortcut:removed` - 快捷方式删除
- `shortcut:reordered` - 快捷方式重排序
- `search:executed` - 搜索执行
- `theme:changed` - 主题变更
- `settings:updated` - 设置更新

## 💾 数据存储架构

### 存储层次结构

```
Chrome Storage API (云同步)
        ↓
localStorage (本地缓存)
        ↓
内存缓存 (运行时)
```

### 数据模型

```javascript
// 快捷方式数据模型
const ShortcutModel = {
    id: 'string',           // 唯一标识符
    name: 'string',         // 显示名称
    url: 'string',          // 目标URL
    icon: 'string',         // 图标URL或emoji
    order: 'number',        // 排序序号
    category: 'string',     // 分类标签
    createdAt: 'timestamp', // 创建时间
    updatedAt: 'timestamp'  // 更新时间
};

// 页面数据模型
const PageModel = {
    id: 'string',           // 页面ID
    name: 'string',         // 页面名称
    icon: 'string',         // 页面图标
    mode: 'string',         // 搜索模式 'normal' | 'quick'
    shortcuts: 'array',     // 快捷方式列表
    order: 'number'         // 页面排序
};
```

### 数据管理器

```javascript
class DataStorageManager {
    constructor() {
        this.storageKey = 'moment-search-data';
        this.backupKey = 'moment-search-backup';
        this.maxBackups = 5;
        this.version = '3.0.0';
    }
    
    // 数据导出
    exportData() {
        return {
            baseConfig: this.getBaseConfig(),
            navConfig: this.getNavConfig(),
            metadata: {
                version: this.version,
                exportTime: new Date().toISOString()
            }
        };
    }
    
    // 数据导入
    async importData(data) {
        // 验证数据格式
        if (!this.validateImportData(data)) {
            throw new Error('数据格式无效');
        }
        
        // 创建备份
        await this.createBackup();
        
        // 导入数据
        await this.importBaseConfig(data.baseConfig);
        await this.importNavConfig(data.navConfig);
    }
}
```

## 🎨 UI组件架构

### 组件层次结构

```
NewTabApp
├── TimeDisplay          # 时间显示组件
├── SearchContainer      # 搜索容器
│   ├── PlatformSelector # 平台选择器
│   ├── SearchInput      # 搜索输入框
│   └── SearchHistory    # 搜索历史
├── ShortcutsGrid       # 快捷方式网格
│   ├── ShortcutItem    # 快捷方式项
│   └── AddButton       # 添加按钮
├── PageIndicator       # 页面指示器
└── SettingsPanel       # 设置面板
    ├── SettingsNav     # 设置导航
    └── SettingsContent # 设置内容
```

### 样式系统

```css
:root {
    /* 颜色系统 */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #4285f4;
    
    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 动画时长 */
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
}
```

## 🔌 扩展API集成

### Chrome Extension API使用

```javascript
// manifest.json 配置
{
    "manifest_version": 3,
    "permissions": ["storage", "tabs", "favicon"],
    "chrome_url_overrides": {
        "newtab": "index.html"
    },
    "background": {
        "service_worker": "background.js"
    }
}
```

### 后台脚本架构

```javascript
// background.js
class BackgroundService {
    constructor() {
        this.init();
    }
    
    init() {
        // 扩展安装/更新处理
        chrome.runtime.onInstalled.addListener(this.handleInstall);
        
        // 消息处理
        chrome.runtime.onMessage.addListener(this.handleMessage);
        
        // 性能监控
        this.setupPerformanceMonitoring();
    }
    
    handleMessage(message, sender, sendResponse) {
        const handlers = {
            'executeSearch': this.executeSearch,
            'getStorageData': this.getStorageData,
            'setStorageData': this.setStorageData
        };
        
        const handler = handlers[message.type];
        if (handler) {
            handler(message.data).then(sendResponse);
            return true; // 保持消息通道开放
        }
    }
}
```

## 🔧 性能优化架构

### 加载优化策略

1. **延迟初始化**: 非关键模块延迟100ms初始化
2. **异步加载**: 图标和壁纸异步加载
3. **缓存机制**: 多层缓存减少重复请求
4. **DOM优化**: 最小化DOM操作，使用DocumentFragment

### 内存管理

```javascript
class MemoryManager {
    static cleanup() {
        // 清理过期缓存
        this.clearExpiredCache();
        
        // 移除未使用的事件监听器
        this.removeUnusedListeners();
        
        // 垃圾回收提示
        if (window.gc) {
            window.gc();
        }
    }
}
```

## 🛡️ 安全架构

### 内容安全策略 (CSP)

```json
{
    "content_security_policy": {
        "extension_pages": "script-src 'self'; object-src 'self'; img-src 'self' blob: data: https://www.google.com; style-src 'self' 'unsafe-inline' blob:;"
    }
}
```

### 权限最小化原则

- **storage**: 仅用于保存用户设置
- **tabs**: 仅用于搜索功能
- **favicon**: 仅用于获取网站图标

## 📈 可扩展性设计

### 插件系统架构 (规划中)

```javascript
class PluginManager {
    constructor() {
        this.plugins = new Map();
        this.hooks = new Map();
    }
    
    registerPlugin(plugin) {
        // 插件注册逻辑
    }
    
    executeHook(hookName, data) {
        // 钩子执行逻辑
    }
}
```

### 模块扩展点

- **搜索平台**: 支持自定义搜索引擎
- **快捷方式**: 支持自定义快捷方式类型
- **主题系统**: 支持自定义主题和样式
- **数据源**: 支持多种数据导入格式

---

**文档版本**: V1.0  
**创建时间**: 2025-01-22  
**维护团队**: Moment Search Development Team
