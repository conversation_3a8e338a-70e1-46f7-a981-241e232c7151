/* Moment Search V3 - 样式文件 */

/* 应用加载状态控制 - 简化版本 */
.app-loading {
    visibility: hidden;
}

/* 页面加载期间禁用所有transition和animation，防止视觉跳跃 */
.app-loading *,
.app-loading *::before,
.app-loading *::after {
    transition: none !important;
    animation: none !important;
    transform: none !important;
}

.app-ready {
    visibility: visible;
    /* 移除动画，实现静态切换 */
}

/* 确保背景层在准备状态下正常显示 */
.app-ready .background-layer {
    opacity: 1 !important;
    /* 移除过渡效果，实现静态切换 */
}

/* CSS变量定义 */
:root {
    /* 颜色系统 */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #4285f4;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #ffffff;
    
    /* 背景色 */
    --bg-primary: rgba(255, 255, 255, 0.95);
    --bg-secondary: rgba(255, 255, 255, 0.8);
    --bg-overlay: rgba(0, 0, 0, 0.1);
    
    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 圆角系统 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 50%;
    
    /* 阴影系统 */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 24px rgba(0,0,0,0.2);
    
    /* 动画时长 */
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;



    /* 设置面板专用变量 */
    --settings-primary: #1976d2;
    --settings-primary-hover: #1565c0;
    --settings-primary-light: #e3f2fd;
    --settings-danger: #d32f2f;
    --settings-danger-hover: #c62828;
    --settings-nav-bg: #f8f9fa;
    --settings-nav-hover: #e9ecef;
    --settings-border: #e9ecef;
    --settings-border-light: #f1f3f4;
    --settings-text: #212529;
    --settings-text-secondary: #6c757d;
    --settings-nav-width: 35%; /* 在更小的面板中，左侧导航占35% */
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    height: 100vh;
    overflow: hidden;
    position: relative;
    /* 默认使用白色背景，而不是彩色渐变 */
    background: #ffffff;
}

/* 背景层 */
.background-layer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* 默认使用白色背景，背景管理器会根据用户设置动态应用背景 */
    background: #ffffff;
    background-size: cover;
    background-position: center;
    filter: blur(1px);
    z-index: -1;
    overflow: hidden;
    /* 移除过渡动画，实现静态切换 */
}

/* 背景图片元素 */
.background-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform-origin: center center;
    object-fit: cover;
    max-width: none;
    max-height: none;
}

/* 侧边栏容器 - 默认左侧位置 */
.sidebar {
    position: fixed;
    left: var(--spacing-lg);
    bottom: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    z-index: 100;
    transition: all var(--duration-normal) ease;
}

/* 右侧侧边栏位置 */
body.sidebar-right .sidebar {
    left: auto;
    right: var(--spacing-lg);
}

body.sidebar-right .page-sidebar {
    left: auto;
    right: var(--spacing-lg);
}

/* 侧边栏自动隐藏功能 */
body.sidebar-auto-hide .sidebar {
    opacity: 0;
    visibility: hidden;
    transform: scale(0.8);
}

body.sidebar-auto-hide .shortcut-help-container {
    opacity: 0;
    visibility: hidden;
    transform: scale(0.8);
}

body.sidebar-auto-hide .page-sidebar {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-50%) scale(0.8);
}

/* 悬停时显示侧边栏元素 - 统一控制 */
body.sidebar-auto-hide.sidebar-hover .sidebar {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

body.sidebar-auto-hide.sidebar-hover .shortcut-help-container {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

body.sidebar-auto-hide.sidebar-hover .page-sidebar {
    opacity: 1;
    visibility: visible;
    transform: translateY(-50%) scale(1);
}

.tool-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
    transition: all var(--duration-normal) ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tool-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

/* 快捷键帮助容器 - 默认左上角位置 */
.shortcut-help-container {
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    z-index: 100;
    transition: all var(--duration-normal) ease;
}

/* 右侧快捷键帮助位置 */
body.sidebar-right .shortcut-help-container {
    left: auto;
    right: var(--spacing-lg);
}



/* 快捷键帮助提示框 */
.shortcut-help-tooltip {
    position: absolute;
    top: 0; /* 与图标水平对齐 */
    left: 60px; /* 向右偏移，避开左侧导航区域 */
    min-width: 280px; /* 减小最小宽度，适应简化后的内容 */
    max-width: 350px; /* 减小最大宽度 */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px); /* 改为水平方向的动画效果 */
    transition: all var(--duration-normal) ease;
    z-index: 1000;
}

/* 右侧快捷键帮助提示框位置调整 */
body.sidebar-right .shortcut-help-tooltip {
    left: auto;
    right: 60px; /* 向左偏移，避开右侧边界 */
    transform: translateX(10px); /* 右侧时向右偏移 */
}

body.sidebar-right .shortcut-help-container:hover .shortcut-help-tooltip,
body.sidebar-right .shortcut-help-tooltip.show {
    transform: translateX(0);
}

.shortcut-help-container:hover .shortcut-help-tooltip,
.shortcut-help-tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(0); /* 水平方向的显示动画 */
}

/* 快捷键帮助头部 */
.shortcut-help-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.shortcut-help-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.shortcut-help-header h3::before {
    content: "⌨️";
    font-size: 18px;
}

/* 快捷键帮助内容 */
.shortcut-help-content {
    padding: var(--spacing-md) var(--spacing-lg);
    max-height: 250px; /* 减小最大高度，适应简化后的内容 */
    overflow-y: auto;
}

/* 快捷键分组 */
.shortcut-group {
    margin-bottom: var(--spacing-md);
}

.shortcut-group:last-child {
    margin-bottom: 0;
}

.shortcut-group-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 快捷键项目 */
.shortcut-item-help {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    font-size: 13px;
}

.shortcut-key {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-family: 'Consolas', 'Monaco', monospace;
    font-weight: 500;
    color: var(--text-dark);
    white-space: nowrap;
}

.shortcut-description {
    color: var(--text-secondary);
    flex: 1;
    margin-left: var(--spacing-sm);
}

.shortcut-scope {
    font-size: 11px;
    color: var(--text-muted);
    font-style: italic;
    margin-left: var(--spacing-xs);
}

/* 时间显示 */
.time-display {
    position: absolute;
    top: 40px; /* 修改为计算后的正确位置，避免刷新时的位置跳跃 */
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--text-light);
    text-shadow: 0 2px 8px rgba(0,0,0,0.3);
    z-index: 10;
    /* 确保时间区域在字体大小变化时保持位置稳定 */
    min-height: 80px;
    min-width: 300px; /* 固定最小宽度防止抖动 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    /* 防止内容变化导致的布局抖动 */
    will-change: transform;
    backface-visibility: hidden;
}

.time {
    font-size: 4rem;
    margin-bottom: var(--spacing-xs);
    letter-spacing: -2px;
    /* 固定宽度防止数字变化导致的抖动 */
    min-width: 280px;
    display: inline-block;
    /* 字体系列和粗细由JavaScript动态设置，不在CSS中硬编码 */
}

.date {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 400;
    /* 固定宽度防止日期变化导致的抖动 */
    min-width: 200px;
    display: inline-block;
}

/* 搜索容器 - 位置固定，不应被JavaScript修改 */
.search-container {
    position: absolute;
    top: 220px !important; /* 使用!important确保位置不被JavaScript修改 */
    left: 50%;
    transform: translateX(-50%);
    width: 600px; /* 增加宽度 */
    max-width: 90vw;
    z-index: 20;
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: 14px 24px 14px 16px; /* 减小左侧内边距，让图标更贴近边缘 */
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(10px);
    transition: all var(--duration-normal) ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-box:focus-within {
    box-shadow: var(--shadow-lg);
    transform: none; /* 移除垂直移动，避免跳动 */
    background: var(--bg-primary); /* 使用主题变量而不是固定的白色 */
}

/* 深色主题搜索框焦点状态 */
.dark-theme .search-box:focus-within {
    background: rgba(0, 0, 0, 0.9); /* 深色主题的焦点背景 */
}

.platform-selector {
    display: flex;
    align-items: center;
    margin-right: 12px; /* 适当的右侧间隙 */
    margin-left: 0; /* 完全贴靠左侧 */
    padding: 0; /* 移除内边距，让图标完全贴边 */
    background: transparent; /* 背景透明，与输入栏背景一致 */
    border-radius: var(--radius-lg);
    backdrop-filter: none; /* 移除模糊效果 */
    border: none; /* 移除边框 */
    cursor: pointer;
    transition: all var(--duration-fast) ease;
    min-width: 24px; /* 调整最小宽度 */
    justify-content: center;
}

.platform-selector:hover {
    background: rgba(255, 255, 255, 0.1); /* 轻微的悬停背景 */
    transform: none; /* 移除缩放效果，避免跳动 */
    border-radius: var(--radius-md); /* 悬停时显示圆角 */
}

.platform-icon-display {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px; /* 进一步增大图标容器尺寸到32px */
    height: 32px;
}

.platform-favicon {
    width: 30px; /* 增大图标尺寸到30px */
    height: 30px;
    border-radius: 50%; /* 改为标准圆形 */
    object-fit: cover;
}

.platform-emoji {
    font-size: 30px; /* 增大emoji尺寸到30px */
    line-height: 1;
}



.platform-icon {
    font-size: 14px;
    line-height: 1;
}

.platform-name {
    font-size: 14px; /* 增加字体大小 */
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
}

#searchInput {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    background: transparent;
    color: var(--text-primary);
    padding: var(--spacing-sm) 0;
}

#searchInput::placeholder {
    color: #999;
}

/* 深色主题搜索输入框占位符 */
.dark-theme #searchInput::placeholder {
    color: #aaa;
}

/* 平台选择下拉框 */
.platform-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 8px;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    max-height: 400px;
    overflow: hidden;
}

/* 深色主题平台下拉框 */
.dark-theme .platform-dropdown {
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
}

.platform-dropdown-content {
    padding: 16px;
}

.platform-search {
    margin-bottom: 12px;
}

.platform-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 14px;
}

/* 深色主题平台搜索输入框 */
.dark-theme .platform-search input {
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.dark-theme .platform-search input:focus {
    border-color: #4285f4;
    background: rgba(255, 255, 255, 0.2);
}

.platform-search input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
}

/* 多选功能头部 */
.platform-multi-select-header {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    align-items: center;
}

.multi-select-toggle {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: rgba(74, 144, 226, 0.1);
    border: 1px solid rgba(74, 144, 226, 0.3);
    border-radius: 6px;
    color: var(--text-primary);
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.multi-select-toggle:hover {
    background: rgba(74, 144, 226, 0.2);
    border-color: rgba(74, 144, 226, 0.5);
}

.multi-select-icon {
    font-size: 14px;
}



/* 平台项复选框 */
.platform-item-checkbox {
    position: absolute;
    top: -5px;
    left: -5px;
    z-index: 10;
}

.platform-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

/* 多选模式下的平台项样式 */
.platform-item.multi-select-mode {
    transition: all 0.2s ease;
}

.platform-item.multi-select-mode.selected {
    border: 2px solid #22c55e !important;
    background: rgba(34, 197, 94, 0.1) !important;
    transform: scale(1.02);
}

.platform-item.multi-select-mode:hover {
    border: 2px solid rgba(34, 197, 94, 0.5);
    background: rgba(34, 197, 94, 0.05);
}

/* 多选模式下的搜索图标样式 */
#platformEmoji.multi-select-mode {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
    animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
    0% {
        box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
    }
    50% {
        box-shadow: 0 4px 8px rgba(34, 197, 94, 0.5);
    }
    100% {
        box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
    }
}



/* 用户新增平台标识 */
.platform-source-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    background: rgba(59, 130, 246, 0.8);
    color: white;
    font-size: 9px;
    padding: 1px 3px;
    border-radius: 2px;
    z-index: 10;
    pointer-events: none;
}

.platform-list {
    max-height: 300px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr)); /* 大幅减小最小宽度 */
    gap: 4px; /* 减小间距 */
}

/* 平台项容器 - 纯图标效果，无任何容器视觉装饰 */
.platform-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2px !important;
    cursor: pointer;
    margin: 2px !important;
    /* 移除所有可能的视觉装饰 */
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    outline: none !important;
    transition: none !important;
}

/* 确保悬停时也没有任何容器视觉效果 */
.platform-item:hover {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}

.platform-item.selected {
    background: rgba(74, 144, 226, 0.2);
    border-color: rgba(74, 144, 226, 0.4);
}

/* 平台图标容器 - 纯净无装饰 */
.platform-icon-container {
    width: 24px;
    height: 24px;
    margin-bottom: 2px;
    position: relative; /* 为删除按钮定位 */
}

/* 平台图标 - 纯图标效果 */
.platform-icon-container img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
    display: block;
    transition: transform 0.2s ease;
}

/* 纯图标悬停效果 - 只对图标本身应用缩放 */
.platform-item:hover .platform-icon-container img {
    transform: scale(1.05); /* 与主页图标一致的缩放效果 */
}

.platform-icon-container span {
    font-size: 20px;
    line-height: 24px;
    display: block;
    width: 24px;
    height: 24px;
    text-align: center;
    transition: transform 0.2s ease;
    /* 完全移除所有装饰样式 */
}

/* 平台图标悬停效果 - 只对图标本身应用简洁的缩放效果 */
.platform-item:hover .platform-icon-container img,
.platform-item:hover .platform-icon-container span {
    transform: scale(1.05);
}

/* 兼容性样式：将旧类名重定向到新样式 */
.platform-item-icon {
    width: 24px;
    height: 24px;
    margin-bottom: 2px;
    position: relative;
}

/* 平台项名称 - 保持静态，无悬停变化 */
.platform-item-name {
    font-size: 10px;
    color: var(--text-primary);
    text-align: center;
    line-height: 1.1;
    margin-top: 1px;
    transition: none !important; /* 禁用过渡效果 */
}

/* 确保悬停时文字不变色 */
.platform-item:hover .platform-item-name {
    color: var(--text-primary) !important;
}

/* 删除按钮样式 */
.platform-item-delete {
    position: absolute;
    top: -3px;
    right: -3px;
    width: 14px;
    height: 14px;
    background: #ff4444;
    border: 1px solid white;
    border-radius: 50%;
    display: none; /* 默认隐藏 */
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 9px;
    color: white;
    font-weight: bold;
    z-index: 10;
    transition: all var(--duration-fast) ease;
}

.platform-item-delete:hover {
    background: #cc0000;
    transform: scale(1.1);
}

/* 悬停时显示删除按钮 */
.platform-item:hover .platform-item-delete {
    display: flex;
}

/* 删除动画效果 */
.platform-item.deleting {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

/* 添加按钮样式 - 完全移除外框效果，与普通图标保持一致 */
.platform-add-item {
    /* 完全移除所有装饰效果 */
    border: none !important;
    background: transparent !important;
    transition: none !important;
    margin: 2px !important;
    padding: 2px !important;
    box-shadow: none !important;
    outline: none !important;
}

.platform-add-item:hover {
    /* hover时保持完全透明，无任何视觉变化 */
    border: none !important;
    background: transparent !important;
    transform: none !important;
    box-shadow: none !important;
    outline: none !important;
}

.platform-add-item .platform-item-icon {
    /* 移除所有装饰效果 */
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    outline: none !important;
    transition: none !important;
    /* 图标样式 */
    font-size: 24px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    /* 浅色主题图标颜色 */
    color: rgba(0, 0, 0, 0.6) !important;
}

.platform-add-item .platform-item-name {
    /* 文字样式 */
    font-weight: 500;
    text-align: center;
    transition: none !important;
    /* 浅色主题文字颜色 */
    color: rgba(0, 0, 0, 0.6) !important;
}

/* 深色主题添加按钮样式 */
.dark-theme .platform-add-item .platform-item-icon {
    color: rgba(255, 255, 255, 0.8) !important;
}

.dark-theme .platform-add-item .platform-item-name {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* 添加平台模态弹窗样式 */
.add-platform-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-platform-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.add-platform-content {
    position: relative;
    background: white;
    color: #333;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

/* 深色主题添加平台模态框 */
.dark-theme .add-platform-content {
    background: #2a2a2a;
    color: #ffffff;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.add-platform-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #eee;
}

.add-platform-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 深色主题添加平台头部 */
.dark-theme .add-platform-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.dark-theme .add-platform-header h3 {
    color: #ffffff;
}

.add-platform-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.add-platform-close:hover {
    background: #f5f5f5;
    color: #333;
}

/* 深色主题关闭按钮 */
.dark-theme .add-platform-close {
    color: #aaa;
}

.dark-theme .add-platform-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.add-platform-body {
    padding: 24px;
    max-height: 50vh; /* 减小最大高度，为少量平台留出更多空间 */
    overflow-y: auto;
    /* 当内容较少时，自动调整高度 */
    min-height: auto;
}

.add-platform-description {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 14px;
    text-align: center;
}

/* 深色主题描述文字 */
.dark-theme .add-platform-description {
    color: #aaa;
}

.add-platform-grid {
    display: grid;
    grid-template-columns: repeat(9, 60px); /* 固定每行最多9个图标，每个60px宽 */
    gap: 4px; /* 与弹窗列表一致的间距 */
    justify-content: center; /* 居中显示 */
    max-width: 100%; /* 确保不超出容器宽度 */
}

.add-platform-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2px !important; /* 与弹窗列表一致的最小padding */
    border: 1px solid #e0e0e0; /* 减小边框宽度 */
    border-radius: 4px; /* 减小圆角 */
    cursor: pointer;
    transition: all 0.2s ease;
    background: none !important; /* 移除背景，让图标纯净显示 */
    margin: 2px !important; /* 与弹窗列表一致的最小margin */
    max-width: 60px; /* 强制限制最大宽度 */
    min-width: 50px; /* 设置最小宽度 */
}

/* 深色主题平台选项 */
.dark-theme .add-platform-option {
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.add-platform-option:hover {
    border-color: #4285f4;
    background: none !important; /* 悬停时也不显示背景 */
}

.add-platform-option.selected {
    border-color: #4285f4;
    background: none !important; /* 选中时也不显示背景 */
}

/* 状态可视化样式 */
.add-platform-option.used {
    border-color: #34a853; /* 绿色外框表示已使用 */
    background: none !important; /* 移除背景色，只保留边框指示状态 */
}

.add-platform-option.used:hover {
    border-color: #2d8f47;
    background: none !important; /* 悬停时也不显示背景 */
}

.add-platform-option.unused {
    border-color: #ea4335; /* 红色外框表示未使用 */
    background: none !important; /* 移除背景色，只保留边框指示状态 */
}

.add-platform-option.unused:hover {
    border-color: #d33b2c;
    background: none !important; /* 悬停时也不显示背景 */
}

/* 全新的平台选项图标容器样式 - 完全重构，移除所有装饰 */
.add-platform-icon-container {
    width: 24px; /* 与弹窗列表一致的图标尺寸 */
    height: 24px;
    margin-bottom: 2px; /* 最小margin */
    /* 完全移除所有装饰样式 */
}

.add-platform-icon-container img {
    width: 24px; /* 与弹窗列表一致的图标尺寸 */
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
    display: block; /* 确保图片作为块级元素显示 */
    /* 完全移除所有装饰样式 */
}

.add-platform-icon-container span {
    font-size: 20px; /* 与弹窗列表一致的emoji尺寸 */
    line-height: 24px;
    display: block;
    width: 24px;
    height: 24px;
    text-align: center;
    /* 完全移除所有装饰样式 */
}

/* 悬停时图标效果 - 保持简洁，只有缩放效果 */
.add-platform-option:hover .add-platform-icon-container {
    transform: scale(1.05);
}

/* 兼容性样式：将旧类名重定向到新样式 */
.add-platform-option-icon {
    width: 24px;
    height: 24px;
    margin-bottom: 2px;
    transition: all 0.2s ease;
}

.add-platform-option:hover .add-platform-option-icon {
    transform: scale(1.05);
}

.add-platform-option-name {
    font-size: 9px; /* 进一步缩小字体以适应窄宽度 */
    color: #333;
    text-align: center;
    font-weight: 500;
    margin-top: 1px; /* 与弹窗列表一致的最小间距 */
    line-height: 1.1; /* 与弹窗列表一致的行高 */
    word-break: break-all; /* 允许单词内换行 */
    overflow-wrap: break-word; /* 处理长单词换行 */
    max-width: 100%; /* 确保不超出容器宽度 */
}

/* 深色主题平台选项名称 */
.dark-theme .add-platform-option-name {
    color: #ffffff;
}

.add-platform-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #eee;
    background: #fafafa;
}

/* 深色主题添加平台底部 */
.dark-theme .add-platform-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: #1a1a1a;
}

.add-platform-btn {
    padding: 10px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-platform-btn.cancel {
    background: #f5f5f5;
    color: #666;
}

.add-platform-btn.cancel:hover {
    background: #e0e0e0;
    color: #333;
}

/* 深色主题按钮 */
.dark-theme .add-platform-btn.cancel {
    background: rgba(255, 255, 255, 0.1);
    color: #aaa;
}

.dark-theme .add-platform-btn.cancel:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

.add-platform-btn.confirm {
    background: #4285f4;
    color: white;
}

.add-platform-btn.confirm:hover {
    background: #3367d6;
}

/* 双模式页面侧边栏样式将在后面添加 */

/* 移除搜索模式切换器相关样式 */

/* 移除搜索模式面板相关样式 */

/* 移除平台选择器和别名提示相关样式 */



/* 旧的模式切换提示样式已移除，使用enhanced版本 */

/* 搜索模式面板切换动画 */
.search-mode-panel {
    opacity: 0;
    transform: translateY(10px);
    transition: all var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.search-mode-panel.active {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}



/* 移除搜索侧边栏滚动条样式 */

/* 自定义快捷键输入组件 */
.custom-shortcut-input {
    width: 100%;
}

.shortcut-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    transition: all var(--duration-normal) ease;
    /* 浅色主题样式 */
    background: rgba(0,0,0,0.05);
    border: 1px solid rgba(0,0,0,0.1);
}

.shortcut-display:hover {
    /* 浅色主题悬停样式 */
    background: rgba(0,0,0,0.08);
    border-color: rgba(0,0,0,0.15);
}

.shortcut-keys {
    flex: 1;
    font-family: 'Consolas', 'Monaco', monospace;
    font-weight: 600;
    font-size: 13px;
    /* 浅色主题文字颜色 */
    color: #333;
}

.shortcut-edit-btn {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: all var(--duration-fast) ease;
    /* 浅色主题按钮样式 */
    background: rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.15);
    color: #333;
}

.shortcut-edit-btn:hover {
    background: rgba(0,0,0,0.12);
    border-color: rgba(0,0,0,0.2);
    transform: translateY(-1px);
}

/* 深色主题适配 */
.dark-theme .shortcut-display {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
}

.dark-theme .shortcut-display:hover {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.3);
}

.dark-theme .shortcut-keys {
    color: rgba(255,255,255,0.9);
}

.dark-theme .shortcut-edit-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
}

.dark-theme .shortcut-edit-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.4);
}

.shortcut-input-container {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    /* 浅色主题样式 */
    background: rgba(0,0,0,0.03);
    border: 1px solid rgba(0,0,0,0.08);
}

.shortcut-input {
    width: 100%;
    padding: 10px 12px;
    border-radius: var(--radius-sm);
    font-family: 'Consolas', 'Monaco', monospace;
    font-weight: 600;
    font-size: 14px;
    text-align: center;
    transition: all var(--duration-normal) ease;
    /* 浅色主题样式 */
    background: rgba(0,0,0,0.05);
    border: 2px solid rgba(0,0,0,0.1);
    color: #333;
}

.shortcut-input:focus {
    outline: none;
    /* 浅色主题焦点样式 */
    border-color: rgba(0,0,0,0.3);
    background: rgba(0,0,0,0.08);
    box-shadow: 0 0 0 3px rgba(0,0,0,0.05);
}

.shortcut-input.recording {
    border-color: #4CAF50;
    background: rgba(76,175,80,0.1);
    animation: pulse 1.5s infinite;
}

/* 深色主题适配 */
.dark-theme .shortcut-input-container {
    background: rgba(255,255,255,0.08);
    border: 1px solid rgba(255,255,255,0.15);
}

.dark-theme .shortcut-input {
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(255,255,255,0.2);
    color: white;
}

.dark-theme .shortcut-input:focus {
    border-color: rgba(255,255,255,0.5);
    background: rgba(255,255,255,0.15);
    box-shadow: 0 0 0 3px rgba(255,255,255,0.1);
}

.dark-theme .shortcut-input.recording {
    background: rgba(76,175,80,0.1);
}

@keyframes pulse {
    0%, 100% { box-shadow: 0 0 0 3px rgba(76,175,80,0.2); }
    50% { box-shadow: 0 0 0 6px rgba(76,175,80,0.1); }
}

.shortcut-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.shortcut-save-btn, .shortcut-cancel-btn {
    padding: 6px 12px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    cursor: pointer;
    transition: all var(--duration-fast) ease;
    /* 浅色主题基础样式 */
    border: 1px solid rgba(0,0,0,0.2);
}

.shortcut-save-btn {
    /* 浅色主题保存按钮 */
    background: rgba(76,175,80,0.15);
    color: #2E7D32;
    border-color: rgba(76,175,80,0.4);
}

.shortcut-save-btn:hover:not(:disabled) {
    background: rgba(76,175,80,0.25);
    border-color: rgba(76,175,80,0.6);
    transform: translateY(-1px);
}

.shortcut-save-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.shortcut-cancel-btn {
    /* 浅色主题取消按钮 */
    background: rgba(244,67,54,0.15);
    color: #C62828;
    border-color: rgba(244,67,54,0.4);
}

.shortcut-cancel-btn:hover {
    background: rgba(244,67,54,0.25);
    border-color: rgba(244,67,54,0.6);
    transform: translateY(-1px);
}

/* 深色主题适配 */
.dark-theme .shortcut-save-btn,
.dark-theme .shortcut-cancel-btn {
    border: 1px solid rgba(255,255,255,0.3);
}

.dark-theme .shortcut-save-btn {
    background: rgba(76,175,80,0.2);
    color: #4CAF50;
    border-color: rgba(76,175,80,0.5);
}

.dark-theme .shortcut-save-btn:hover:not(:disabled) {
    background: rgba(76,175,80,0.3);
    border-color: rgba(76,175,80,0.7);
}

.dark-theme .shortcut-cancel-btn {
    background: rgba(244,67,54,0.2);
    color: #f44336;
    border-color: rgba(244,67,54,0.5);
}

.dark-theme .shortcut-cancel-btn:hover {
    background: rgba(244,67,54,0.3);
    border-color: rgba(244,67,54,0.7);
}

.shortcut-help {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    /* 浅色主题样式 */
    background: rgba(0,0,0,0.03);
    border-left: 3px solid rgba(0,0,0,0.2);
}

.shortcut-help p {
    margin: 2px 0;
    font-size: 11px;
    /* 浅色主题文字颜色 */
    color: rgba(0,0,0,0.6);
}

.shortcut-error {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 12px;
    /* 浅色主题错误样式 */
    background: rgba(244,67,54,0.08);
    border: 1px solid rgba(244,67,54,0.2);
    color: #C62828;
}

/* 深色主题适配 */
.dark-theme .shortcut-help {
    background: rgba(255,255,255,0.05);
    border-left: 3px solid rgba(255,255,255,0.3);
}

.dark-theme .shortcut-help p {
    color: rgba(255,255,255,0.7);
}

.dark-theme .shortcut-error {
    background: rgba(244,67,54,0.1);
    border: 1px solid rgba(244,67,54,0.3);
    color: #f44336;
}

/* 主题切换动画 - 移除以实现静态切换 */
body, .background-layer, .page-sidebar {
    /* 移除过渡动画，实现静态切换 */
}

/* 双模式页面管理侧边栏 */
.page-sidebar {
    position: fixed;
    left: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0;
    z-index: 100;
    max-height: 80vh;
    overflow: hidden;
}

/* 页面区域 */
.page-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

/* 常规搜索区域 */
#normalPageSection {
    margin-bottom: var(--spacing-sm);
}

/* 快捷搜索区域 */
#quickPageSection {
    margin-top: var(--spacing-sm);
}

/* 页面区域头部 */
.page-section-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

/* 页面区域底部 */
.page-section-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: var(--spacing-sm);
}

/* 分隔线 */
.page-divider {
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    margin: var(--spacing-sm) 0;
    border-radius: 1px;
}

.page-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.page-item {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all var(--duration-normal) ease;
    font-size: 16px;
    position: relative;
}

.page-item:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.page-item.active {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-md);
}

.page-item.active::after {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 2px;
}

.page-action-btn {
    width: 28px;
    height: 28px;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all var(--duration-normal) ease;
    font-size: 14px;
    color: var(--text-light);
    font-weight: bold;
}

.page-action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.1);
}

/* 主内容区域 */
.main-content {
    position: relative;
    width: 100%;
    height: 100%;
    /* 移除过渡动画，实现静态切换 */
}

/* 快捷方式网格 */
.shortcuts-grid {
    position: absolute;
    top: 320px;
    left: 50%;
    transform: translateX(-50%);
    display: grid;
    grid-template-columns: repeat(var(--grid-columns, 10), var(--icon-size, 60px));
    gap: var(--icon-spacing, 24px);
    justify-content: center;
    z-index: 10;
    /* 移除过渡动画，实现静态切换 */
    width: var(--container-width, 80%);
    max-width: 1200px;
}

/* 页面指示器 */
.page-indicator {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-light);
    text-shadow: 0 1px 3px rgba(0,0,0,0.5);
    z-index: 10;
}

.current-page {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

.page-counter {
    font-size: 12px;
    opacity: 0.7;
}

/* 快捷方式基础样式 */
.shortcut-item,
.shortcut-add-button {
    text-align: center;
    cursor: pointer;
    position: relative;
    transition: all var(--duration-normal) ease;
    transform: translateZ(0);
    will-change: transform;
}

.shortcut-icon {
    width: var(--icon-size, 60px);
    height: var(--icon-size, 60px);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    font-size: calc(var(--icon-size, 60px) * 0.4);
    transition: all var(--duration-normal) ease;
    box-shadow: none; /* 移除阴影效果，实现纯图标显示 */
    backdrop-filter: none; /* 移除模糊效果，实现纯图标显示 */
    border: none;
    margin-bottom: var(--spacing-sm);
    overflow: hidden;
    position: relative;
}

/* 纯图标悬停效果 - 只保留简洁的缩放效果，无装饰 */
.shortcut-icon:hover {
    transform: scale(1.05); /* 保持简洁的缩放效果 */
    box-shadow: none; /* 移除悬停时的阴影效果 */
    background: transparent; /* 保持背景透明，无装饰 */
}

/* 真实网站图标样式 - 优化为圆形完全填充 */
.shortcut-favicon {
    width: 100%;
    height: 100%;
    border-radius: var(--radius-full);
    object-fit: cover;
    transition: all var(--duration-fast) ease;
    position: absolute;
    top: 0;
    left: 0;
    background: white;
    z-index: 1;
}

/* emoji图标样式 */
.shortcut-emoji {
    font-size: calc(var(--icon-size, 60px) * 0.4);
    line-height: 1;
    transition: all var(--duration-fast) ease;
    position: relative;
    z-index: 2;
    background: var(--bg-primary);
    width: 100%;
    height: 100%;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 添加图标按钮特定样式 */

/* 悬停效果已合并到基础交互样式中 */

.shortcut-add-button:focus {
    outline: none;
}

.add-icon {
    width: var(--icon-size, 60px);
    height: var(--icon-size, 60px);
    border-radius: var(--radius-full);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--duration-normal) ease;
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-sm);
}

.add-icon-plus {
    font-size: calc(var(--icon-size, 60px) * 0.5);
    font-weight: 300;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1;
}

.shortcut-add-button:hover .add-icon,
.shortcut-add-button.hover .add-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
    background: rgba(255, 255, 255, 0.98);
}

.shortcut-add-button:hover .add-icon-plus,
.shortcut-add-button.hover .add-icon-plus {
    color: rgba(255, 255, 255, 0.9);
}

/* 快捷方式选中状态 */
.shortcut-item.selected {
    transform: scale(1.02);
}

.shortcut-item.selected .shortcut-icon {
    transform: scale(1.05);
}

/* 快捷方式聚焦状态 */
.shortcut-item:focus {
    outline: none;
}

.shortcut-name {
    margin-top: 8px;
    font-size: calc(var(--icon-size, 60px) * 0.2);
    color: var(--text-color, #ffffff);
    text-shadow: 0 1px 3px rgba(0,0,0,0.5);
    opacity: 0.9;
    max-width: var(--icon-size, 60px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: all var(--duration-normal) ease;
}

/* 隐藏快捷方式名称 */
.shortcuts-grid.hide-names .shortcut-name {
    opacity: 0;
    height: 0;
    margin-top: 0;
    overflow: hidden;
}

/* 基础交互样式 - 移除容器悬停效果，只保留添加按钮的效果 */
.shortcut-add-button:hover,
.shortcut-add-button.hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
}

.shortcut-item:active,
.shortcut-add-button:active {
    transform: translateY(-1px);
}

/* 拖拽时的容器样式 - 移除transition，避免页面加载时的布局跳跃 */

@keyframes insertIndicatorPulse {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
}

/* 拖拽幽灵样式 - 确保纯图标显示 */
.drag-ghost {
    /* 完全重置所有可能的装饰样式 */
    background: none !important;
    box-shadow: none !important;
    border: none !important;
    backdrop-filter: none !important;
    border-radius: 50% !important;
    /* 确保图标内容正确显示 */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 拖拽幽灵内的图标样式 */
.drag-ghost img,
.drag-ghost span {
    /* 确保图标元素保持原始样式 */
    border-radius: 50% !important;
    box-shadow: none !important;
    border: none !important;
}

/* 拖拽幽灵内的favicon需要保留白色背景 */
.drag-ghost .shortcut-favicon {
    background: white !important;
}

/* 拖拽幽灵内的favicon图片 */
.drag-ghost .shortcut-favicon {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

/* 拖拽幽灵内的emoji */
.drag-ghost .shortcut-emoji {
    font-size: inherit !important;
    line-height: 1 !important;
}

/* 文件夹合并高亮效果 - 只对图标区域 */
.shortcut-icon.folder-merge-highlight {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transition: all 0.2s ease;
}

/* 文件夹样式 */
.folder-item {
    position: relative;
}

.folder-icon {
    position: relative;
}

.folder-emoji {
    font-size: 2.5rem;
}

.folder-preview-icon {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    display: block;
}

.folder-content {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 200px;
    max-width: 300px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 8px;
    margin-top: 5px;
}

.folder-child-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.folder-child-item:hover {
    background: var(--bg-secondary);
}

.child-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
}

.child-icon img {
    width: 24px;
    height: 24px;
    border-radius: 4px;
}

.child-emoji {
    font-size: 1.5rem;
}

.child-name {
    font-size: 11px;
    text-align: center;
    color: var(--text-secondary);
    line-height: 1.2;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}



/* 桌面级拖拽样式 */
.shortcut-item {
    cursor: grab;
    transition: transform 0.2s ease, opacity 0.2s ease;
}

/* 移除容器悬停效果 - 悬停效果将由 .shortcut-icon:hover 处理 */

.shortcut-item:active {
    cursor: grabbing;
}

/* 拖拽时的全局样式 */
body.dragging {
    user-select: none;
    -webkit-user-select: none;
}

body.dragging * {
    cursor: grabbing !important;
}

/* 网格项目的平滑移动动画 */
.shortcut-item {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}



/* 旧的图标大小变体已移除，现在使用动态CSS变量 */

/* 设置面板 - 左侧边栏样式（优化动画性能） */
.settings-panel {
    position: fixed;
    left: -30%;
    top: 0;
    width: 30%; /* 减少宽度到30% */
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    color: #333;
    backdrop-filter: blur(10px);
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.08);
    border-right: 1px solid rgba(0, 0, 0, 0.06);
    transition: left 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    overflow: hidden;
    will-change: transform;
    min-width: 400px; /* 减少最小宽度适应更紧凑的布局 */
    display: flex;
    flex-direction: column;
}

/* 深色主题设置面板 */
.dark-theme .settings-panel {
    background: rgba(26, 26, 26, 0.98);
    color: #ffffff;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);
}

.settings-panel.show {
    left: 0;
}

/* 右侧侧边栏时的设置面板样式 */
body.sidebar-right .settings-panel {
    left: auto;
    right: -30%;
    border-right: none;
    border-left: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: -2px 0 15px rgba(0, 0, 0, 0.08);
    transition: right 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色主题右侧设置面板 */
.dark-theme.sidebar-right .settings-panel {
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: -2px 0 15px rgba(0, 0, 0, 0.3);
}

/* 右侧设置面板显示状态 - 统一控制 */
body.sidebar-right .settings-panel.show {
    right: 0;
}

/* 设置面板背景遮罩 */
.settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    opacity: 0;
    visibility: hidden;
    transition: all var(--duration-normal) ease;
    z-index: 999;
}

.settings-overlay.show {
    opacity: 1;
    visibility: visible;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--settings-border);
    background: var(--settings-nav-bg);
}

.settings-header h3 {
    color: #212529;
    font-weight: 600;
    font-size: 18px;
    margin: 0;
}

/* 深色主题设置头部 */
.dark-theme .settings-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: #1a1a1a;
}

.dark-theme .settings-header h3 {
    color: #ffffff;
}

.close-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #e9ecef;
    color: #495057;
    border-color: #ced4da;
}

/* 深色主题关闭按钮 */
.dark-theme .close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

.dark-theme .close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.settings-content {
    display: flex;
    height: calc(100vh - 80px);
    overflow: hidden;
}

/* 左侧导航栏 - 现代化简洁设计 */
.settings-nav {
    width: var(--settings-nav-width);
    background: var(--settings-nav-bg);
    border-right: 1px solid var(--settings-border);
    padding: var(--spacing-xl) 0;
    overflow-y: auto;
}

.settings-nav-item {
    display: block;
    padding: 14px 24px;
    margin: 0;
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #495057;
    font-size: 14px;
    font-weight: 500;
    text-align: left;
    width: 100%;
    border-left: 3px solid transparent;
    position: relative;
}

/* 深色主题设置导航 */
.dark-theme .settings-nav {
    background: #1a1a1a;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .settings-nav-item {
    color: #aaa;
}

.settings-nav-item:hover {
    background: var(--settings-nav-hover);
    color: var(--settings-text);
}

.settings-nav-item.active {
    background: var(--settings-primary-light);
    color: var(--settings-primary);
    border-left-color: var(--settings-primary);
    font-weight: 600;
}

/* 深色主题设置导航项 */
.dark-theme .settings-nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.dark-theme .settings-nav-item.active {
    background: rgba(66, 133, 244, 0.2);
    color: #4285f4;
    border-left-color: #4285f4;
}

.settings-nav-item.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--settings-primary);
}

/* 右侧内容区域 - 优化布局 */
.settings-main {
    flex: 1; /* 使用flex: 1自动占据剩余空间，避免固定宽度冲突 */
    padding: 16px; /* 进一步减少padding适应更小的空间 */
    overflow-y: auto;
    background: #ffffff;
    color: #333;
    min-height: 0; /* 确保flex子项可以收缩 */
}

/* 深色主题设置主要内容 */
.dark-theme .settings-main {
    background: #2a2a2a;
    color: #ffffff;
}

.settings-section {
    display: none;
    animation: fadeIn 0.2s ease-in-out;
    max-width: 100%;
    flex: 1; /* 占用剩余空间 */
    overflow: hidden; /* 防止内容溢出 */
    height: 100%; /* 确保高度传递 */
}

.settings-section.active {
    display: flex;
    flex-direction: column;
    height: 100%; /* 确保高度传递 */
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.setting-group {
    background: #ffffff;
    border-radius: 8px; /* 减少圆角，更简洁 */
    padding: 0;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03); /* 减少阴影 */
    overflow: hidden;
    margin-bottom: 16px; /* 减少间距 */
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-group h4 {
    display: block;
    margin: 0 0 12px 0;
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 600;
    color: var(--settings-text-secondary);
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

/* 深色主题设置组 */
.dark-theme .setting-group {
    background: #3a3a3a;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.dark-theme .setting-group h4 {
    background: #2a2a2a;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #aaa;
}

/* 隐藏非时间设置页面的标题 */
.settings-section:not([data-section="time"]) .setting-group h4 {
    display: none;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px; /* 进一步减少padding适应更紧凑的空间 */
    border-bottom: 1px solid var(--settings-border-light);
    transition: background-color 0.15s ease;
    min-height: 44px; /* 减少最小高度 */
    gap: 12px; /* 减少间距适应更小的空间 */
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item:hover {
    background-color: var(--settings-nav-bg);
}

/* 深色主题设置项 */
.dark-theme .setting-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .setting-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.setting-label {
    color: var(--settings-text);
    font-weight: 500;
    font-size: 13px; /* 进一步减小字体适应更小空间 */
    line-height: 1.3;
    flex: 1;
    margin-right: 0; /* 移除右边距，使用gap代替 */
    max-width: 55%; /* 进一步限制标签宽度，为控件留出更多空间 */
}

/* 深色主题文字颜色统一 */
.dark-theme .setting-label,
.dark-theme .form-row label,
.dark-theme .font-name,
.dark-theme .weight-name {
    color: #ffffff;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 6px; /* 进一步减少控件内部间距 */
    flex-shrink: 0;
    min-width: 100px; /* 减少控件最小宽度适应更小空间 */
}

/* 现代化开关样式 */
.switch {
    position: relative;
    width: 48px;
    height: 28px;
    background: #dee2e6;
    border-radius: 14px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    outline: none;
}

.switch.active {
    background: var(--settings-primary);
}

.switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.switch.active::after {
    transform: translateX(20px);
}

.switch:hover {
    box-shadow: 0 0 0 8px rgba(25, 118, 210, 0.1);
}

/* 现代化选择框样式 */
.setting-select {
    padding: 6px 10px; /* 进一步减少padding */
    border: 1px solid #ced4da;
    border-radius: 4px; /* 进一步减少圆角 */
    background: white;
    color: var(--settings-text);
    font-size: 13px; /* 减小字体 */
    min-width: 80px; /* 减少最小宽度 */
    max-width: 140px; /* 减少最大宽度适应更小空间 */
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
    height: 32px; /* 进一步减少高度 */
}

/* 深色主题设置选择框 */
.dark-theme .setting-select {
    background: #3a3a3a;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.setting-select:hover {
    border-color: var(--settings-primary);
}

.setting-select:focus {
    outline: none;
    border-color: var(--settings-primary);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

/* 深色主题设置选择框悬停和焦点状态 */
.dark-theme .setting-select:hover {
    border-color: #4285f4;
    background: #4a4a4a;
}

.dark-theme .setting-select:focus {
    border-color: #4285f4;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
    background: #4a4a4a;
}

/* 现代化按钮样式 */
.setting-btn {
    background: var(--settings-primary);
    color: white;
    border: none;
    padding: 8px 12px; /* 减少padding适应更小空间 */
    border-radius: 4px; /* 减少圆角 */
    cursor: pointer;
    font-size: 13px; /* 减小字体 */
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px; /* 减少间距 */
    font-family: inherit;
    min-height: 32px; /* 减少最小高度 */
}

.setting-btn:hover {
    background: var(--settings-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.setting-btn.danger {
    background: var(--settings-danger);
}

.setting-btn.danger:hover {
    background: var(--settings-danger-hover);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.setting-btn.secondary {
    background: #f5f5f5;
    color: #424242;
    border: 1px solid #e0e0e0;
}

.setting-btn.secondary:hover {
    background: #eeeeee;
    border-color: #d0d0d0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 深色主题设置按钮 */
.dark-theme .setting-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dark-theme .setting-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 设置项中的内联按钮 */
.setting-inline-btn {
    width: auto !important;
    margin: 0 !important;
    padding: 8px 16px !important;
    font-size: 13px !important;
    min-height: 36px !important;
}

.button-group {
    display: flex;
    gap: 8px; /* 减少按钮间距 */
    padding: 16px; /* 减少padding */
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    margin: 0;
}

.button-group .setting-btn {
    flex: 1;
    min-width: 0; /* 允许按钮收缩 */
    font-size: 12px; /* 进一步减小按钮字体 */
}

/* 深色主题按钮组 */
.dark-theme .button-group {
    background: #1a1a1a;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 滑动条样式 */
.slider-container {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
}

.setting-slider {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.setting-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--settings-primary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.setting-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
}

.setting-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--settings-primary);
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

.slider-value {
    font-size: 12px;
    color: var(--settings-text-secondary);
    min-width: 40px;
    text-align: right;
}

/* 颜色选择器样式 */
.color-picker-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 200px; /* 增加宽度以容纳一行显示 */
}

.preset-colors {
    display: flex;
    gap: 6px;
    flex-wrap: nowrap; /* 不换行，强制一行显示 */
    align-items: center;
}

.color-preset {
    width: 20px;
    height: 20px;
    border-radius: 50%; /* 圆形 */
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    flex-shrink: 0; /* 防止压缩 */
}

/* 自定义颜色选择器样式 */
.custom-color-picker {
    position: relative;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    overflow: hidden;
    flex-shrink: 0;
}

.custom-color-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0; /* 隐藏原生input */
}

.custom-color-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* 备用线性渐变，以防conic-gradient不支持 */
    background: linear-gradient(45deg,
        #ff0000, #ff7f00, #ffff00, #00ff00,
        #00ffff, #0000ff, #8000ff, #ff0080, #ff0000);
    /* 主要的圆锥渐变 */
    background: conic-gradient(
        from 0deg,
        #ff0000 0deg,
        #ff7f00 45deg,
        #ffff00 90deg,
        #00ff00 135deg,
        #00ffff 180deg,
        #0000ff 225deg,
        #8000ff 270deg,
        #ff0080 315deg,
        #ff0000 360deg
    );
    border: 2px solid transparent;
    border-radius: 50%;
    transition: all 0.2s ease;
    pointer-events: none; /* 让点击穿透到input */
    box-sizing: border-box;
}

.custom-color-picker:hover .custom-color-icon {
    transform: scale(1.1);
    border-color: var(--primary-color);
}

.color-preset:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.color-preset.active {
    border-color: var(--settings-primary);
    transform: scale(1.1);
}

/* 旧的custom-color-input样式已被新的设计替代 */

/* 关于页面样式 */
.about-content {
    padding: 40px 24px;
    text-align: center;
    color: #6c757d;
    line-height: 1.6;
}

.about-title {
    font-size: 18px;
    font-weight: 600;
    color: #212529;
    margin-bottom: 12px;
}

.about-description {
    font-size: 14px;
    margin-bottom: 20px;
    color: #6c757d;
}

.about-link {
    display: inline-block;
    color: var(--settings-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
    border: 1px solid var(--settings-primary);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.about-link:hover {
    background: var(--settings-primary);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .time {
        font-size: 3rem;
    }

    .shortcuts-grid {
        grid-template-columns: repeat(5, 60px);
        gap: var(--spacing-md);
    }

    .search-container {
        width: 90%;
    }

    .settings-panel {
        width: 100%;
        left: -100%;
        min-width: unset; /* 移除最小宽度限制 */
    }

    .settings-panel.show {
        left: 0;
    }

    /* 右侧侧边栏小屏幕调整 */
    body.sidebar-right .settings-panel {
        right: -100%;
    }

    /* 小屏幕下调整布局 */
    .settings-content {
        flex-direction: column; /* 小屏幕下改为垂直布局 */
    }

    .settings-nav {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--settings-border);
        padding: var(--spacing-md) 0;
    }

    .settings-main {
        width: 100%;
        padding: 16px; /* 小屏幕下减少padding */
    }

    .setting-item {
        flex-direction: column; /* 小屏幕下垂直排列 */
        align-items: flex-start;
        gap: 8px;
        padding: 16px 20px;
    }

    .setting-label {
        max-width: 100%;
        margin-bottom: 8px;
    }

    .setting-control {
        width: 100%;
        justify-content: flex-end;
    }

    .sidebar {
        left: var(--spacing-md);
        bottom: var(--spacing-md);
    }

    /* 右侧侧边栏小屏幕调整 */
    body.sidebar-right .sidebar {
        left: auto;
        right: var(--spacing-md);
    }

    /* 小屏幕下快捷键帮助调整 */
    .shortcut-help-container {
        top: var(--spacing-md);
        left: var(--spacing-md);
    }

    /* 右侧快捷键帮助小屏幕调整 */
    body.sidebar-right .shortcut-help-container {
        left: auto;
        right: var(--spacing-md);
    }

    /* 小屏幕下页面侧边栏调整 */
    .page-sidebar {
        left: var(--spacing-md);
    }

    /* 右侧页面侧边栏小屏幕调整 */
    body.sidebar-right .page-sidebar {
        left: auto;
        right: var(--spacing-md);
    }

    .shortcut-help-tooltip {
        min-width: 240px; /* 小屏幕下进一步减小宽度 */
        max-width: 280px;
        left: 50px; /* 小屏幕下也保持向右偏移，但减少偏移量 */
    }

    /* 右侧快捷键提示框小屏幕调整 */
    body.sidebar-right .shortcut-help-tooltip {
        left: auto;
        right: 50px; /* 小屏幕下向左偏移，但减少偏移量 */
    }

    .shortcut-help-content {
        max-height: 200px; /* 小屏幕下进一步减少高度，适应简化内容 */
    }

    /* 小屏幕下弹窗居中显示 */
    .modal-overlay {
        justify-content: center;
        padding-left: 0;
    }

    .modal-content {
        margin-left: 0;
        width: 320px; /* 小屏幕下适当减小宽度 */
    }

    .icon-grid {
        grid-template-columns: repeat(5, 1fr); /* 小屏幕下改为5列 */
        gap: 6px; /* 小屏幕下减小间距 */
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .shortcuts-grid {
        grid-template-columns: repeat(8, 60px);
    }

    .modal-content {
        width: 350px; /* 中等屏幕下的宽度 */
    }

    /* 中等屏幕下的设置面板优化 */
    .settings-panel {
        width: 40%; /* 中等屏幕下稍微增加宽度 */
        min-width: 350px; /* 减少最小宽度 */
    }

    /* 右侧侧边栏中等屏幕调整 */
    body.sidebar-right .settings-panel {
        right: -40%;
    }

    .settings-main {
        padding: 14px; /* 减少padding */
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .modal-content {
        width: 300px;
    }

    .icon-grid {
        grid-template-columns: repeat(4, 1fr); /* 超小屏幕下改为4列 */
        gap: 4px;
        padding: var(--spacing-sm);
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 20px;
}

.modal-content {
    background: white;
    color: #333;
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    width: 420px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色主题模态框样式 */
.dark-theme .modal-content {
    background: #2a2a2a;
    color: #ffffff;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
}

.modal-overlay .modal-content {
    transform: scale(1);
    opacity: 1;
}

/* 弹窗显示动画 */
@keyframes modalSlideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.modal-overlay[style*="flex"] .modal-content {
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 深色主题模态框头部 */
.dark-theme .modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header h3 {
    color: var(--text-primary);
    font-weight: 500;
}

.modal-body {
    padding: var(--spacing-lg);
}

/* 错误提示样式 */
.error-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: var(--border-radius);
    color: #dc2626;
    font-size: 14px;
    line-height: 1.4;
}

.error-message .error-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.error-message .error-text {
    flex: 1;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    font-size: 14px;
    color: #333;
    background: white;
    transition: border-color var(--duration-fast) ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

/* 深色主题表单输入框 */
.dark-theme .form-group input {
    background: #3a3a3a;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.dark-theme .form-group input:focus {
    border-color: #4285f4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.3);
}

/* URL输入组样式 */
.url-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.url-input-group input {
    flex: 1;
}

.btn-fetch-icon {
    padding: 12px 16px;
    background: rgba(74, 144, 226, 0.8);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 14px;
    cursor: pointer;
    transition: all var(--duration-fast) ease;
    white-space: nowrap;
}

.btn-fetch-icon:hover {
    background: rgba(74, 144, 226, 1);
    transform: translateY(-1px);
}

.btn-fetch-icon:disabled {
    background: rgba(255, 255, 255, 0.2);
    cursor: not-allowed;
    transform: none;
}

/* 图标输入组样式 */
.icon-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.icon-preview {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
}

.preview-favicon {
    width: 100%;
    height: 100%;
    border-radius: var(--radius-full);
    object-fit: cover;
}

.preview-emoji {
    font-size: 24px;
    line-height: 1;
}

/* 图标显示组样式 */
.icon-display-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--radius-md);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.icon-status {
    flex: 1;
    font-size: 14px;
    color: #666;
    font-style: italic;
}

.icon-input-group input {
    flex: 1;
}

.btn-reset-icon {
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    font-size: 14px;
    cursor: pointer;
    transition: all var(--duration-fast) ease;
    white-space: nowrap;
}

.btn-reset-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}



@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}



.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 深色主题模态框底部 */
.dark-theme .modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cancel, .btn-confirm {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 14px;
    transition: all var(--duration-fast) ease;
}

.btn-cancel {
    background: #f5f5f5;
    color: var(--text-secondary);
}

.btn-cancel:hover {
    background: #e0e0e0;
}

.btn-confirm {
    background: var(--accent-color);
    color: white;
}

.btn-confirm:hover {
    background: #3367d6;
}

/* 图标选择器样式 */
.icon-grid-container {
    width: 100%;
    margin-bottom: var(--spacing-md);
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr); /* 6列布局 */
    gap: 8px;
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: var(--radius-md);
    border: 1px solid rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden; /* 明确禁止横向滚动 */
    box-sizing: border-box; /* 确保padding不会导致溢出 */
}

.icon-option {
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1; /* 保持正方形比例 */
    min-width: 0; /* 允许收缩 */
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 16px;
    transition: all var(--duration-fast) ease;
    background: white;
    border: 2px solid transparent;
    box-sizing: border-box; /* 确保边框不会导致溢出 */
}

.icon-option:hover {
    background: rgba(66, 133, 244, 0.1);
    transform: scale(1.1);
}

.icon-option.selected {
    background: rgba(66, 133, 244, 0.15);
    border-color: var(--accent-color);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
}

/* 字体选择器样式 */
.font-selector-container {
    width: 100%;
}

.font-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 2列布局，4个字体选项 */
    gap: 8px;
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: var(--radius-md);
    border: 1px solid rgba(0, 0, 0, 0.1);
    max-height: 120px; /* 减少高度，因为移除了预览 */
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
}

/* 深色主题字体网格 */
.dark-theme .font-grid,
.dark-theme .font-weight-grid {
    background: #2a2a2a;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.font-option {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--duration-fast) ease;
    background: white;
    border: 2px solid transparent;
    box-sizing: border-box;
    min-height: 40px; /* 减少高度 */
}

/* 深色主题字体和字体粗细选项 */
.dark-theme .font-option,
.dark-theme .font-weight-option {
    background: #3a3a3a;
}

.dark-theme .font-option:hover,
.dark-theme .font-weight-option:hover {
    background: rgba(66, 133, 244, 0.2);
}

.dark-theme .font-option.selected,
.dark-theme .font-weight-option.selected {
    background: rgba(66, 133, 244, 0.3);
    border-color: #4285f4;
}

.font-option:hover {
    background: rgba(66, 133, 244, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.font-option.selected {
    background: rgba(66, 133, 244, 0.15);
    border-color: var(--accent-color);
    box-shadow: 0 4px 16px rgba(66, 133, 244, 0.3);
}

.font-name {
    font-size: 13px;
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    text-align: center;
    line-height: 1.2;
    font-weight: 500;
}



/* 字体粗细选择器样式 */
.font-weight-selector-container {
    width: 100%;
}

.font-weight-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 2列布局，2个粗细选项 */
    gap: 8px;
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: var(--radius-md);
    border: 1px solid rgba(0, 0, 0, 0.1);
    max-height: 80px;
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
}



.font-weight-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--duration-fast) ease;
    background: white;
    border: 2px solid transparent;
    box-sizing: border-box;
    min-height: 50px;
    /* 强制使用系统字体以确保字体粗细差异明显 */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
}



.font-weight-option:hover {
    background: rgba(66, 133, 244, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.font-weight-option.selected {
    background: rgba(66, 133, 244, 0.15);
    border-color: var(--accent-color);
    box-shadow: 0 4px 16px rgba(66, 133, 244, 0.3);
}

.weight-name {
    font-size: 15px;
    color: var(--text-primary);
    font-family: inherit; /* 继承父元素的字体 */
    text-align: center;
    line-height: 1.2;
    font-weight: inherit; /* 继承父元素的字体粗细，这样才能显示差异 */
    margin-bottom: 2px;
}





/* 滚动条样式 */
.icon-grid::-webkit-scrollbar {
    width: 6px;
}

.icon-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.icon-grid::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.icon-grid::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 右键菜单样式 */
.context-menu {
    position: fixed;
    background: white;
    color: #333;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-sm);
    z-index: 3000;
    min-width: 120px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.menu-item {
    padding: var(--spacing-md);
    cursor: pointer;
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-size: 14px;
    transition: background var(--duration-fast) ease;
}

.menu-item:hover {
    background: rgba(0, 0, 0, 0.05);
}

/* 深色主题右键菜单 */
.dark-theme .context-menu {
    background: #2a2a2a;
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
}

.dark-theme .menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* 主题切换 - 使用更高特异性确保变量正确覆盖 */
body.dark-theme {
    --text-primary: #ffffff !important;
    --text-secondary: #cccccc !important;
    --text-light: #ffffff !important;
    --bg-primary: rgba(0, 0, 0, 0.8) !important;
    --bg-secondary: rgba(0, 0, 0, 0.6) !important;
    --bg-overlay: rgba(255, 255, 255, 0.1) !important;
}

body.light-theme {
    --text-primary: #333333 !important;
    --text-secondary: #666666 !important;
    --text-light: #ffffff !important;
    --bg-primary: rgba(255, 255, 255, 0.95) !important;
    --bg-secondary: rgba(255, 255, 255, 0.8) !important;
    --bg-overlay: rgba(0, 0, 0, 0.1) !important;
}





/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn var(--duration-normal) ease;
}

.slide-in-right {
    animation: slideInRight var(--duration-normal) ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* Phase 2: 高级动画效果 */

/* 加载动画 */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.loading-shimmer {
    position: relative;
    overflow: hidden;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* 设置面板增强动画已合并到主定义中 */



/* 主题切换动画 */
.theme-transition * {
    transition: background-color 0.3s ease,
                color 0.3s ease,
                border-color 0.3s ease,
                box-shadow 0.3s ease;
}

/* 弹性动画 */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

.bounce {
    animation: bounce 1s ease;
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 旋转动画 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.spin {
    animation: spin 1s linear infinite;
}

/* 渐入渐出动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 40px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease;
}

/* 缩放动画 */
@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }
    50% {
        opacity: 1;
    }
}

.zoom-in {
    animation: zoomIn 0.6s ease;
}

/* ===== 平台管理样式 ===== */

/* 平台标签页 */
.platform-tabs {
    display: flex;
    border-bottom: 1px solid var(--settings-border);
    margin-bottom: var(--spacing-lg);
}

.platform-tab {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all var(--duration-fast);
    font-size: 14px;
    font-weight: 500;
}

.platform-tab:hover {
    color: var(--settings-primary);
    background: var(--settings-primary-light);
}

.platform-tab.active {
    color: var(--settings-primary);
    border-bottom-color: var(--settings-primary);
    background: var(--settings-primary-light);
}

/* 深色主题平台标签页 */
.dark-theme .platform-tabs {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.dark-theme .platform-tab {
    color: #aaa;
}

.dark-theme .platform-tab:hover {
    color: #4285f4;
    background: rgba(66, 133, 244, 0.1);
}

.dark-theme .platform-tab.active {
    color: #4285f4;
    background: rgba(66, 133, 244, 0.2);
}

/* 标签页内容 */
.platform-tab-content {
    display: none;
    flex: 1; /* 利用父容器的flex布局，自动占用剩余空间 */
    overflow: hidden; /* 防止内容溢出 */
    height: 100%; /* 确保高度传递 */
}

.platform-tab-content.active {
    display: flex;
    flex-direction: column;
    height: 100%; /* 确保高度传递 */
}

/* 确保平台设置区域充分利用高度 - 只在激活时应用 */
.settings-section[data-section="platforms"].active {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* URL解析表单 */
.url-parser-form {
    background: #f8f9fa;
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
}

/* 深色主题URL解析表单 */
.dark-theme .url-parser-form {
    background: #2a2a2a;
}

.form-row {
    margin-bottom: var(--spacing-md);
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-row label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}



.form-help {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 12px;
    color: var(--text-secondary);
}

/* 解析结果 */
.parse-result {
    background: #fff;
    border: 1px solid var(--settings-border);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.result-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.result-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: var(--spacing-sm);
}

.result-status.success {
    background: #4caf50;
}

.result-status.error {
    background: #f44336;
}

.result-title {
    font-weight: 600;
    color: var(--text-primary);
}

.result-details {
    background: #f8f9fa;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-lg);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
}

.platform-config {
    border-top: 1px solid var(--settings-border);
    padding-top: var(--spacing-lg);
}

/* 平台统计 */
.platform-stats {
    background: var(--settings-primary-light);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    font-size: 14px;
    color: var(--settings-primary);
}



/* 平台列表容器布局优化 */
.platform-tab-content[data-tab-content="platform-list"] .setting-group {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    flex: 1; /* 让设置组充分利用可用空间 */
}

.platform-tab-content[data-tab-content="platform-list"] .platform-list {
    flex: 1;
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    /* 覆盖搜索下拉框的grid布局，使用块级布局 */
    display: block !important;
    grid-template-columns: none !important;
    gap: 0 !important;
    box-sizing: border-box;
    /* 覆盖通用的max-height限制，让列表充分利用可用空间 */
    max-height: none !important;
}

/* 设置页面中的平台项样式 - 使用更具体的选择器避免过度使用!important */
.platform-tab-content[data-tab-content="platform-list"] .platform-list .platform-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: var(--spacing-md);
    border: 1px solid var(--settings-border);
    border-radius: var(--radius-md);
    margin: 0 0 var(--spacing-sm) 0;
    background: #fff;
    transition: all var(--duration-fast);
    width: 100%;
    max-width: 100%;
    height: auto;
    min-height: 60px;
    box-sizing: border-box;
}

/* 深色主题设置页面平台项 */
.dark-theme .platform-tab-content[data-tab-content="platform-list"] .platform-list .platform-item {
    background: #3a3a3a;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark-theme .platform-tab-content[data-tab-content="platform-list"] .platform-list .platform-item:hover {
    background: #4a4a4a;
    border-color: #4285f4;
}

.dark-theme .platform-tab-content[data-tab-content="platform-list"] .platform-list .platform-item.builtin {
    background: #2a2a2a;
}

.dark-theme .platform-tab-content[data-tab-content="platform-list"] .platform-list .platform-item.builtin:hover {
    background: #3a3a3a;
}

.platform-tab-content[data-tab-content="platform-list"] .platform-list .platform-item:hover {
    box-shadow: var(--shadow-sm);
    border-color: var(--settings-primary);
}

.platform-tab-content[data-tab-content="platform-list"] .platform-list .platform-item.builtin {
    background: #f8f9fa;
}

.platform-tab-content[data-tab-content="platform-list"] .platform-list .platform-item.builtin:hover {
    background: #f8f9fa;
}

/* 设置页面平台项的子元素样式 - 移除图标显示 */

.platform-tab-content[data-tab-content="platform-list"] .platform-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
    overflow: hidden;
}

.platform-tab-content[data-tab-content="platform-list"] .settings-platform-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.platform-tab-content[data-tab-content="platform-list"] .platform-details {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
    word-break: break-all;
    overflow-wrap: break-word;
}

.platform-tab-content[data-tab-content="platform-list"] .platform-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-shrink: 0;
}

.platform-tab-content[data-tab-content="platform-list"] .platform-action-btn {
    padding: 4px 8px;
    border: 1px solid var(--settings-border);
    border-radius: var(--radius-sm);
    background: #fff;
    color: var(--text-primary);
    font-size: 12px;
    cursor: pointer;
    transition: all var(--duration-fast);
}

/* 深色主题平台操作按钮 */
.dark-theme .platform-tab-content[data-tab-content="platform-list"] .platform-action-btn {
    background: #4a4a4a;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.platform-tab-content[data-tab-content="platform-list"] .platform-action-btn:hover {
    background: var(--settings-primary);
    color: white;
    border-color: var(--settings-primary);
}

.platform-tab-content[data-tab-content="platform-list"] .platform-action-btn.delete {
    border-color: #dc3545;
    color: #dc3545;
}

.platform-tab-content[data-tab-content="platform-list"] .platform-action-btn.delete:hover {
    background: #dc3545;
    color: white;
}

/* 平台列表状态样式 */
.platform-list-empty {
    text-align: center;
    padding: 40px;
    color: #666;
}

.platform-list-error {
    text-align: center;
    padding: 40px;
    color: #f44336;
}

.platform-builtin-label {
    color: #666;
    font-size: 12px;
}


}

.platform-action-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 12px;
    transition: all var(--duration-fast);
}

.platform-action-btn.edit {
    background: var(--settings-primary-light);
    color: var(--settings-primary);
}

.platform-action-btn.delete {
    background: #ffebee;
    color: var(--settings-danger);
}

.platform-action-btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

/* 文件夹拖拽相关样式 */
.folder-modal.drag-out-indicator {
    border: 3px solid rgba(255, 59, 48, 0.8) !important;
    box-shadow: 0 0 20px rgba(255, 59, 48, 0.4) !important;
}

.folder-modal.drag-out-indicator::after {
    content: '拖拽到此区域外移除图标';
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 59, 48, 0.9);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    white-space: nowrap;
    z-index: 10001;
}

/* 主页面接收文件夹拖拽的样式 */
.shortcuts-grid.drag-over-from-folder {
    background: rgba(24, 144, 255, 0.1);
    border: 2px dashed rgba(24, 144, 255, 0.5);
    border-radius: 12px;
    transition: all 0.2s ease;
    position: relative;
}

.shortcuts-grid.drag-over-from-folder::before {
    content: '放置图标到此处';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(24, 144, 255, 0.9);
    color: white;
    padding: 12px 24px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 500;
    z-index: 1000;
    pointer-events: none;
}

/* ========================================
   壁纸编辑器样式
   ======================================== */

/* 壁纸编辑模态框 */
.wallpaper-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wallpaper-editor-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.wallpaper-editor-content {
    position: relative;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 1200px;
    height: 80%;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 编辑器头部 */
.wallpaper-editor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.wallpaper-editor-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
}

.wallpaper-editor-close {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    color: #6c757d;
    transition: all 0.2s ease;
}

.wallpaper-editor-close:hover {
    background: #e9ecef;
    color: #495057;
    border-color: #ced4da;
}

/* 编辑器主体 */
.wallpaper-editor-body {
    flex: 1;
    overflow: hidden;
}

.wallpaper-editor-main {
    display: flex;
    height: 100%;
}

/* 左侧预览区域 */
.wallpaper-preview-container {
    flex: 1;
    position: relative;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wallpaper-preview-viewport {
    position: relative;
    width: 80%;
    height: 80%;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.wallpaper-preview-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    cursor: move;
}

.wallpaper-preview-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform-origin: center center;
    cursor: move;
    max-width: none;
    max-height: none;
    object-fit: cover;
}

/* 交互提示 */
.wallpaper-interaction-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 16px 20px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 10;
}

.wallpaper-preview-viewport:hover .wallpaper-interaction-hint {
    opacity: 1;
    visibility: visible;
}

.hint-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.hint-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.hint-text {
    font-size: 13px;
    line-height: 1.4;
}

.hint-text div {
    margin-bottom: 2px;
}

.hint-text div:last-child {
    margin-bottom: 0;
}

.wallpaper-preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.preview-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
    background-image:
        linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* 右侧控制面板 */
.wallpaper-controls-panel {
    width: 320px;
    background: #ffffff;
    border-left: 1px solid #e9ecef;
    overflow-y: auto;
    padding: 20px;
}

.control-group {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f1f3f4;
}

.control-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.control-group h4 {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
}

.control-item label {
    min-width: 80px;
    font-size: 13px;
    color: #6c757d;
    font-weight: 500;
}

.control-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.control-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #1976d2;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(25, 118, 210, 0.3);
}

.control-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.4);
}

.control-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #1976d2;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(25, 118, 210, 0.3);
}

.control-value {
    min-width: 50px;
    text-align: right;
    font-size: 12px;
    color: #495057;
    font-weight: 600;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}



/* 快速操作按钮 */
.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.quick-action-btn {
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #ffffff;
    color: #495057;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-action-btn:hover {
    background: #f8f9fa;
    border-color: #ced4da;
    color: #212529;
}

/* 编辑器底部 */
.wallpaper-editor-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.wallpaper-editor-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.wallpaper-editor-btn.cancel {
    background: #f5f5f5;
    color: #6c757d;
    border: 1px solid #e0e0e0;
}

.wallpaper-editor-btn.cancel:hover {
    background: #eeeeee;
    border-color: #d0d0d0;
    color: #495057;
}

.wallpaper-editor-btn.apply {
    background: #1976d2;
    color: white;
}

.wallpaper-editor-btn.apply:hover {
    background: #1565c0;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

/* 壁纸控制按钮组 */
.wallpaper-controls {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.wallpaper-controls .setting-btn {
    flex: 1;
    min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .wallpaper-editor-content {
        width: 95%;
        height: 90%;
    }

    .wallpaper-editor-main {
        flex-direction: column;
    }

    .wallpaper-preview-container {
        height: 60%;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }

    .wallpaper-controls-panel {
        width: 100%;
        height: 40%;
        border-left: none;
        padding: 16px;
    }

    .control-group {
        margin-bottom: 16px;
        padding-bottom: 12px;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .wallpaper-controls {
        flex-direction: column;
    }

    .wallpaper-controls .setting-btn {
        min-width: auto;
    }
}

