# Moment Search V3 安装指南

## 🚀 快速安装

### 方法一：Chrome扩展商店（推荐）
> 注：目前还未上架，请使用方法二

### 方法二：开发者模式安装

#### 1. 下载扩展文件
- 下载或克隆项目到本地
- 确保 `moment-search-v3` 文件夹包含以下文件：
  ```
  moment-search-v3/
  ├── manifest.json
  ├── index.html
  ├── style.css
  ├── script.js
  ├── background.js
  └── assets/
  ```

#### 2. 在Chrome中加载扩展
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择 `moment-search-v3` 文件夹
6. 点击"选择文件夹"

#### 3. 验证安装
- 打开新标签页，应该看到Moment Search界面
- 如果看到错误，请检查控制台（F12）获取详细信息

## 🔧 安装问题排查

### 常见问题

#### 问题1：扩展无法加载
**症状**：点击"加载已解压的扩展程序"后出现错误

**解决方案**：
1. 检查 `manifest.json` 文件格式是否正确
2. 确保所有必需文件都存在
3. 检查文件路径是否正确

#### 问题2：新标签页没有变化
**症状**：安装后新标签页仍显示Chrome默认页面

**解决方案**：
1. 确认扩展已启用（在 `chrome://extensions/` 中检查）
2. 刷新扩展：点击扩展卡片上的刷新按钮
3. 重启Chrome浏览器

#### 问题3：功能不正常
**症状**：界面显示但搜索或快捷方式不工作

**解决方案**：
1. 打开开发者工具（F12）查看控制台错误
2. 检查是否有JavaScript错误
3. 确认所有文件都正确加载

#### 问题4：样式显示异常
**症状**：界面布局混乱或样式缺失

**解决方案**：
1. 检查 `style.css` 文件是否存在
2. 清除浏览器缓存
3. 重新加载扩展

## 📋 系统要求

### 浏览器支持
- **Chrome** 88+ （推荐）
- **Edge** 88+ （基于Chromium）
- **其他Chromium内核浏览器**

### 系统要求
- **Windows** 10+
- **macOS** 10.14+
- **Linux** Ubuntu 18.04+

### 权限说明
扩展需要以下权限：
- `storage` - 保存用户设置和快捷方式
- `tabs` - 管理标签页（用于搜索功能）

## 🔄 更新扩展

### 手动更新
1. 下载最新版本文件
2. 在 `chrome://extensions/` 中找到Moment Search
3. 点击"重新加载"按钮
4. 或者删除旧版本，重新加载新版本

### 数据备份
更新前建议备份数据：
1. 打开Moment Search设置
2. 点击"导出数据"
3. 保存备份文件
4. 更新后可通过"导入数据"恢复

## 🗑️ 卸载扩展

### 完全卸载
1. 在 `chrome://extensions/` 中找到Moment Search
2. 点击"移除"按钮
3. 确认删除

### 清理数据
卸载后如需清理残留数据：
1. 打开Chrome设置
2. 进入"隐私设置和安全性" → "清除浏览数据"
3. 选择"高级"选项卡
4. 勾选"扩展程序数据"
5. 点击"清除数据"

## 🔧 开发者安装

### 开发环境设置
```bash
# 克隆项目
git clone https://github.com/your-repo/moment-search-v3.git

# 进入项目目录
cd moment-search-v3

# 在Chrome中加载扩展进行开发测试
```

### 开发模式特性
- 实时代码修改
- 控制台调试信息
- 性能监控数据

### 调试技巧
1. 使用 `console.log()` 输出调试信息
2. 在 `chrome://extensions/` 中点击"检查视图"
3. 使用Chrome DevTools调试

## 📞 获取帮助

### 技术支持
- **GitHub Issues**: [提交问题](https://github.com/your-repo/moment-search-v3/issues)
- **文档**: [查看完整文档](./docs/)
- **FAQ**: [常见问题解答](./docs/FAQ.md)

### 社区支持
- **讨论区**: [GitHub Discussions](https://github.com/your-repo/moment-search-v3/discussions)
- **反馈建议**: [功能请求](https://github.com/your-repo/moment-search-v3/issues/new?template=feature_request.md)

---

**安装遇到问题？** 请查看 [故障排除指南](./docs/troubleshooting.md) 或提交Issue获取帮助。
