# Moment Search V3 - API参考文档

> 完整的API接口文档和使用示例

## 📡 核心API概览

Moment Search V3 提供了一套完整的API接口，支持所有核心功能的编程访问。所有API都通过全局 `window.app` 对象访问。

### API访问方式

```javascript
// 获取应用实例
const app = window.app;

// 访问各个管理器
const searchManager = app.searchManager;
const shortcutManager = app.shortcutManager;
const settingsManager = app.settingsManager;
```

## 🔍 SearchManager API

### 基础方法

#### `executeSearch(query, platform)`
执行搜索操作

```javascript
// 在指定平台搜索
await searchManager.executeSearch('JavaScript', 'google');

// 使用当前选中平台搜索
await searchManager.executeSearch('Vue.js');

// 全平台搜索
await searchManager.executeSearch('React', 'all');
```

**参数**:
- `query` (string): 搜索关键词
- `platform` (string, 可选): 搜索平台ID，默认使用当前平台

**返回值**: Promise<boolean> - 搜索是否成功执行

#### `matchAlias(query)`
智能别名匹配

```javascript
// 匹配别名搜索
const result = searchManager.matchAlias('g JavaScript');
// 返回: { platform: 'google', query: 'JavaScript' }

const result2 = searchManager.matchAlias('bd Vue.js');
// 返回: { platform: 'baidu', query: 'Vue.js' }
```

**参数**:
- `query` (string): 包含别名的搜索字符串

**返回值**: Object | null
- `platform` (string): 匹配的平台ID
- `query` (string): 提取的搜索关键词

#### `switchPlatform(platformId)`
切换搜索平台

```javascript
// 切换到Google
searchManager.switchPlatform('google');

// 切换到百度
searchManager.switchPlatform('baidu');

// 切换到全平台搜索
searchManager.switchPlatform('all');
```

### 搜索历史API

#### `addToHistory(query, platform)`
添加搜索历史记录

```javascript
searchManager.addToHistory('JavaScript教程', 'google');
```

#### `getSearchHistory(limit)`
获取搜索历史

```javascript
const history = searchManager.getSearchHistory(10);
// 返回最近10条搜索记录
```

#### `clearSearchHistory()`
清空搜索历史

```javascript
searchManager.clearSearchHistory();
```

## 🚀 ShortcutManager API

### 快捷方式CRUD操作

#### `addShortcut(shortcutData)`
添加新的快捷方式

```javascript
const newShortcut = await shortcutManager.addShortcut({
    name: 'GitHub',
    url: 'https://github.com',
    icon: '🐙', // 可以是emoji或URL
    category: 'development'
});
```

**参数**:
- `shortcutData` (Object): 快捷方式数据
  - `name` (string): 显示名称
  - `url` (string): 目标URL
  - `icon` (string, 可选): 图标emoji或URL
  - `category` (string, 可选): 分类标签

**返回值**: Promise<Object> - 创建的快捷方式对象

#### `removeShortcut(id)`
删除快捷方式

```javascript
await shortcutManager.removeShortcut('shortcut-id-123');
```

#### `updateShortcut(id, updateData)`
更新快捷方式

```javascript
await shortcutManager.updateShortcut('shortcut-id-123', {
    name: '新名称',
    url: 'https://new-url.com'
});
```

#### `getShortcuts()`
获取所有快捷方式

```javascript
const shortcuts = shortcutManager.getShortcuts();
```

### 排序和组织

#### `reorderShortcuts(fromIndex, toIndex)`
重新排序快捷方式

```javascript
// 将第0个快捷方式移动到第3个位置
shortcutManager.reorderShortcuts(0, 3);
```

#### `moveShortcutToPage(shortcutId, targetPageId)`
移动快捷方式到指定页面

```javascript
shortcutManager.moveShortcutToPage('shortcut-123', 'page-456');
```

### 批量操作

#### `importShortcuts(shortcutsData)`
批量导入快捷方式

```javascript
const importData = [
    { name: 'Google', url: 'https://google.com', icon: '🔍' },
    { name: 'GitHub', url: 'https://github.com', icon: '🐙' }
];

await shortcutManager.importShortcuts(importData);
```

#### `exportShortcuts()`
导出快捷方式数据

```javascript
const exportData = shortcutManager.exportShortcuts();
// 返回可用于导入的JSON数据
```

## ⚙️ SettingsManager API

### 设置读写

#### `getSetting(key, defaultValue)`
获取设置值

```javascript
// 获取主题模式
const themeMode = settingsManager.getSetting('theme.mode', 'auto');

// 获取快捷方式显示设置
const showNames = settingsManager.getSetting('shortcuts.showNames', true);
```

#### `setSetting(key, value)`
设置配置值

```javascript
// 设置主题模式
settingsManager.setSetting('theme.mode', 'dark');

// 设置图标大小
settingsManager.setSetting('shortcuts.iconSize', 80);
```

#### `updateSettings(section, settings)`
批量更新设置

```javascript
// 更新主题设置
settingsManager.updateSettings('theme', {
    mode: 'dark',
    color: '#ff6b6b',
    system: false
});
```

### 设置面板控制

#### `openSettingsPanel(section)`
打开设置面板

```javascript
// 打开外观设置
settingsManager.openSettingsPanel('appearance');

// 打开快捷方式设置
settingsManager.openSettingsPanel('shortcuts');
```

#### `closeSettingsPanel()`
关闭设置面板

```javascript
settingsManager.closeSettingsPanel();
```

### 主题和样式

#### `applyTheme(themeData)`
应用主题

```javascript
settingsManager.applyTheme({
    mode: 'dark',
    color: '#667eea',
    wallpaper: 'custom-bg.jpg'
});
```

## 🎨 BackgroundManager API

### 壁纸管理

#### `setWallpaper(imageData)`
设置壁纸

```javascript
// 设置本地图片
await backgroundManager.setWallpaper({
    type: 'file',
    data: fileBlob
});

// 设置网络图片
await backgroundManager.setWallpaper({
    type: 'url',
    data: 'https://example.com/wallpaper.jpg'
});
```

#### `removeWallpaper()`
移除当前壁纸

```javascript
backgroundManager.removeWallpaper();
```

#### `getWallpaperInfo()`
获取当前壁纸信息

```javascript
const info = backgroundManager.getWallpaperInfo();
// 返回: { type, url, size, timestamp }
```

## 📄 PageManager API

### 页面管理

#### `createPage(pageData)`
创建新页面

```javascript
const newPage = await pageManager.createPage({
    name: '工作页面',
    icon: '💼',
    mode: 'normal'
});
```

#### `deletePage(pageId)`
删除页面

```javascript
await pageManager.deletePage('page-id-123');
```

#### `switchToPage(pageId)`
切换到指定页面

```javascript
pageManager.switchToPage('page-id-123');
```

#### `getCurrentPage()`
获取当前页面信息

```javascript
const currentPage = pageManager.getCurrentPage();
```

## 🔄 事件系统API

### 事件监听

#### `GlobalEventManager.on(event, callback)`
监听事件

```javascript
// 监听快捷方式添加事件
GlobalEventManager.on('shortcut:added', (shortcut) => {
    console.log('新增快捷方式:', shortcut.name);
});

// 监听主题变更事件
GlobalEventManager.on('theme:changed', (theme) => {
    console.log('主题已变更:', theme.mode);
});
```

#### `GlobalEventManager.emit(event, data)`
触发事件

```javascript
// 触发自定义事件
GlobalEventManager.emit('custom:event', { data: 'example' });
```

### 核心事件列表

| 事件名称 | 触发时机 | 数据参数 |
|---------|---------|---------|
| `shortcut:added` | 添加快捷方式时 | 快捷方式对象 |
| `shortcut:removed` | 删除快捷方式时 | 快捷方式ID |
| `shortcut:updated` | 更新快捷方式时 | 更新后的快捷方式对象 |
| `shortcut:reordered` | 重排序时 | { fromIndex, toIndex } |
| `search:executed` | 执行搜索时 | { query, platform } |
| `theme:changed` | 主题变更时 | 主题配置对象 |
| `settings:updated` | 设置更新时 | { section, settings } |
| `page:switched` | 页面切换时 | 页面对象 |

## 💾 存储API

### 数据导入导出

#### `DataStorageManager.exportData()`
导出完整数据

```javascript
const dataManager = new DataStorageManager();
const exportData = dataManager.exportData();

// 下载为JSON文件
const blob = new Blob([JSON.stringify(exportData, null, 2)], 
    { type: 'application/json' });
const url = URL.createObjectURL(blob);
```

#### `DataStorageManager.importData(data)`
导入数据

```javascript
const dataManager = new DataStorageManager();
await dataManager.importData(importedData);
```

### 本地存储工具

#### `Storage.set(key, value)`
设置存储值

```javascript
Storage.set('user-preference', { theme: 'dark' });
```

#### `Storage.get(key, defaultValue)`
获取存储值

```javascript
const preference = Storage.get('user-preference', {});
```

## 🛠️ 工具函数API

### 实用工具

#### `Utils.generateId()`
生成唯一ID

```javascript
const uniqueId = Utils.generateId();
// 返回: "id_1642857600000_abc123"
```

#### `Utils.debounce(func, delay)`
防抖函数

```javascript
const debouncedSearch = Utils.debounce((query) => {
    searchManager.executeSearch(query);
}, 300);
```

#### `Utils.formatDate(date, format)`
格式化日期

```javascript
const formatted = Utils.formatDate(new Date(), 'YYYY-MM-DD HH:mm');
```

## 🔌 扩展API (规划中)

### 插件系统API

```javascript
// 注册插件
PluginManager.register({
    id: 'my-plugin',
    name: 'My Awesome Plugin',
    version: '1.0.0',
    init: function(api) {
        // 插件初始化逻辑
    }
});

// 调用插件API
const result = await PluginManager.call('my-plugin', 'method', params);
```

## 📝 使用示例

### 完整的快捷方式管理示例

```javascript
// 创建一个完整的快捷方式管理功能
class CustomShortcutManager {
    constructor() {
        this.app = window.app;
        this.init();
    }
    
    init() {
        // 监听快捷方式事件
        GlobalEventManager.on('shortcut:added', this.onShortcutAdded.bind(this));
    }
    
    async addWorkspaceShortcuts() {
        const workspaceShortcuts = [
            { name: 'Gmail', url: 'https://gmail.com', icon: '📧' },
            { name: 'Calendar', url: 'https://calendar.google.com', icon: '📅' },
            { name: 'Drive', url: 'https://drive.google.com', icon: '💾' }
        ];
        
        for (const shortcut of workspaceShortcuts) {
            await this.app.shortcutManager.addShortcut(shortcut);
        }
    }
    
    onShortcutAdded(shortcut) {
        console.log(`快捷方式 "${shortcut.name}" 已添加`);
    }
}

// 使用示例
const customManager = new CustomShortcutManager();
customManager.addWorkspaceShortcuts();
```

---

**文档版本**: V1.0  
**创建时间**: 2025-01-22  
**维护团队**: Moment Search Development Team
