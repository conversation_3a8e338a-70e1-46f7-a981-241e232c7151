/**
 * 快捷键帮助管理器
 * 负责显示和管理快捷键帮助信息
 */
class ShortcutHelpManager {
    constructor() {
        this.helpButton = null;
        this.helpTooltip = null;
        this.helpContent = null;
        this.shortcuts = [];
        
        this.init();
    }

    /**
     * 初始化快捷键帮助管理器
     */
    init() {
        this.helpButton = document.getElementById('shortcutHelpBtn');
        this.helpTooltip = document.getElementById('shortcutHelpTooltip');
        this.helpContent = document.getElementById('shortcutHelpContent');

        if (!this.helpButton || !this.helpTooltip || !this.helpContent) {
            console.warn('快捷键帮助元素未找到');
            return;
        }

        this.loadShortcuts();
        this.renderShortcuts();
        this.bindEvents();

        console.log('✅ 快捷键帮助管理器初始化完成');
    }

    /**
     * 获取当前设置
     */
    getCurrentSettings() {
        // 尝试从多个来源获取设置
        if (typeof Storage !== 'undefined' && Storage.get) {
            return Storage.get('settings_v3', {});
        } else if (window.app?.settingsManager?.settings) {
            return window.app.settingsManager.settings;
        } else {
            // 尝试从localStorage直接读取
            try {
                const stored = localStorage.getItem('settings_v3');
                if (stored) {
                    return JSON.parse(stored);
                }
            } catch (e) {
                console.warn('无法读取设置，使用默认快捷键配置');
            }
        }
        return {};
    }

    /**
     * 生成快捷键配置
     */
    generateShortcutsConfig(settings = {}) {
        return [
            {
                group: '全局快捷键',
                items: [
                    {
                        key: settings.shortcuts?.modeSwitch || 'Ctrl+Q',
                        description: '切换搜索模式',
                        scope: '全局可用'
                    },
                    {
                        key: 'Escape',
                        description: '关闭设置面板',
                        scope: '设置面板打开时'
                    }
                ]
            },
            {
                group: '搜索功能',
                items: [
                    {
                        key: settings.shortcuts?.platformSwitch || 'Tab',
                        description: '切换搜索平台',
                        scope: '搜索框聚焦时'
                    },
                    {
                        key: 'Enter',
                        description: '执行搜索',
                        scope: '搜索框聚焦时'
                    }
                ]
            }
        ];
    }

    /**
     * 加载快捷键配置
     */
    loadShortcuts() {
        const settings = this.getCurrentSettings();
        this.shortcuts = this.generateShortcutsConfig(settings);
    }

    /**
     * 渲染快捷键列表
     */
    renderShortcuts() {
        if (!this.helpContent) return;

        const html = this.shortcuts.map(group => `
            <div class="shortcut-group">
                <div class="shortcut-group-title">${group.group}</div>
                ${group.items.map(item => `
                    <div class="shortcut-item-help">
                        <span class="shortcut-key">${item.key}</span>
                        <span class="shortcut-description">${item.description}</span>
                        <span class="shortcut-scope">${item.scope}</span>
                    </div>
                `).join('')}
            </div>
        `).join('');

        this.helpContent.innerHTML = html;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        if (!this.helpButton || !this.helpTooltip) return;

        // 键盘支持
        this.helpButton.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleTooltip();
            } else if (e.key === 'Escape') {
                // ESC键隐藏提示框
                this.helpTooltip.classList.remove('show');
                this.helpButton.setAttribute('aria-expanded', 'false');
            }
        });

        // 设置可访问性属性
        this.helpButton.setAttribute('tabindex', '0');
        this.helpButton.setAttribute('aria-haspopup', 'true');
        this.helpButton.setAttribute('aria-expanded', 'false');
        this.helpButton.setAttribute('role', 'button');

        // 监听设置变化，实时更新快捷键显示
        document.addEventListener('shortcutUpdated', () => {
            this.loadShortcuts();
            this.renderShortcuts();
        });
    }

    /**
     * 切换提示框显示状态（键盘支持）
     */
    toggleTooltip() {
        if (!this.helpTooltip || !this.helpButton) return;

        // 检查当前是否有 'show' 类
        const isVisible = this.helpTooltip.classList.contains('show');

        if (isVisible) {
            // 隐藏提示框
            this.helpTooltip.classList.remove('show');
            this.helpButton.setAttribute('aria-expanded', 'false');
        } else {
            // 显示提示框
            this.helpTooltip.classList.add('show');
            this.helpButton.setAttribute('aria-expanded', 'true');
        }
    }

    /**
     * 加载默认快捷键配置
     */
    loadDefaultShortcuts() {
        // 使用空设置对象，将使用默认值
        this.shortcuts = this.generateShortcutsConfig({});
    }

    /**
     * 更新快捷键配置
     * 当设置页面修改快捷键时调用
     */
    updateShortcuts() {
        this.loadShortcuts();
        this.renderShortcuts();

        // 触发自定义事件
        document.dispatchEvent(new CustomEvent('shortcutUpdated'));
    }

    /**
     * 获取当前快捷键配置
     */
    getCurrentShortcuts() {
        return this.shortcuts;
    }
}

// 导出类
window.ShortcutHelpManager = ShortcutHelpManager;
