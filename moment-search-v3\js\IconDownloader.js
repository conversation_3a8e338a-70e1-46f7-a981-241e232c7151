/**
 * 图标下载器
 * 简化版本，兼容现有接口但实际使用Base64Icons
 */
class IconDownloader {
    constructor() {
        // 保持接口兼容性，但实际不使用
    }

    /**
     * 检查是否有真实图标缓存
     * @returns {boolean} 始终返回false，使用Base64Icons代替
     */
    hasRealIconCache() {
        return false;
    }

    /**
     * 获取缓存的图标base64数据
     * @param {string} websiteUrl 网站URL
     * @returns {string|null} 始终返回null，使用Base64Icons代替
     */
    getCachedIconBase64(websiteUrl) {
        return null;
    }

    /**
     * 清除真实图标缓存
     */
    clearRealIconCache() {
        console.log('✅ 图标缓存管理已简化，使用内置Base64Icons');
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 空统计信息
     */
    getCacheStats() {
        return {
            iconCount: 0,
            totalSizeBytes: 0,
            totalSizeKB: 0,
            domains: [],
            lastUpdate: '使用内置图标'
        };
    }
}

// 导出类
window.IconDownloader = IconDownloader;
