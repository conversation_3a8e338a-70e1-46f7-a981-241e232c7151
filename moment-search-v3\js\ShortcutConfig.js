/**
 * 快捷方式配置数据
 * 静态配置文件，包含预设快捷方式的基础数据
 * 图标将通过API动态获取，与用户添加的图标使用相同机制
 */
class ShortcutConfig {
    constructor() {
        this.config = this.getConfigData();
    }

    /**
     * 获取配置数据
     * @returns {Object} 配置对象
     */
    getConfigData() {
        return {
            version: '3.0.0',
            lastUpdated: '2025-01-31',
            shortcuts: [
                {
                    id: 1,
                    name: 'Bing',
                    url: 'https://www.bing.com',
                    src: '', // 空的src，将通过API获取真实图标
                    icon: '🌐',
                    order: 1,
                    category: 'search',
                    searchMode: 'direct',
                    pageMode: 'normal',
                    type: 'icon'
                },
                {
                    id: 2,
                    name: 'Chrome',
                    url: 'https://www.google.com/chrome',
                    src: '', // 空的src，将通过API获取真实图标
                    icon: '🌍',
                    order: 2,
                    category: 'browser',
                    searchMode: 'direct',
                    pageMode: 'normal',
                    type: 'icon'
                }
            ]
        };
    }

    /**
     * 获取所有快捷方式
     * @returns {Array} 快捷方式数组
     */
    getShortcuts() {
        return [...this.config.shortcuts];
    }

    /**
     * 根据ID获取快捷方式
     * @param {number} id 快捷方式ID
     * @returns {Object|null} 快捷方式对象
     */
    getShortcutById(id) {
        return this.config.shortcuts.find(shortcut => shortcut.id === id) || null;
    }

    /**
     * 根据分类获取快捷方式
     * @param {string} category 分类名称
     * @returns {Array} 快捷方式数组
     */
    getShortcutsByCategory(category) {
        return this.config.shortcuts.filter(shortcut => shortcut.category === category);
    }

    /**
     * 根据页面模式获取快捷方式
     * @param {string} pageMode 页面模式 (normal/quick)
     * @returns {Array} 快捷方式数组
     */
    getShortcutsByPageMode(pageMode) {
        return this.config.shortcuts.filter(shortcut => shortcut.pageMode === pageMode);
    }

    /**
     * 获取配置信息
     * @returns {Object} 配置信息
     */
    getConfigInfo() {
        return {
            version: this.config.version,
            lastUpdated: this.config.lastUpdated,
            shortcutCount: this.config.shortcuts.length,
            categories: [...new Set(this.config.shortcuts.map(s => s.category))]
        };
    }

    /**
     * 检查快捷方式是否有图标URL
     * @param {Object} shortcut 快捷方式对象
     * @returns {boolean} 是否有图标URL
     */
    hasIconUrl(shortcut) {
        return !!(shortcut.src && shortcut.src.trim() !== '');
    }
}

// 导出类
window.ShortcutConfig = ShortcutConfig;