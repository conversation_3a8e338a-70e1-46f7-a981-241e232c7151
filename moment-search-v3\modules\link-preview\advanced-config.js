/**
 * 高级功能配置弹窗
 * 提供独立的配置界面，支持阅读模式、文本操作等高级功能的配置
 */
class AdvancedConfigDialog {
    constructor(linkPreviewManager) {
        this.linkPreviewManager = linkPreviewManager;
        this.isVisible = false;
        this.currentTab = 'reading';
        this.keydownHandler = null;
        
        // 配置数据
        this.config = {
            reading: {
                enabled: true,
                defaultTheme: 'light',
                defaultFontSize: 16,
                defaultLineHeight: 1.6,
                defaultMaxWidth: 800,
                defaultFontFamily: 'system',
                autoEnable: false,
                savePreferences: true
            },
            textOperations: {
                enabled: true,
                defaultSearchEngine: 'google',
                defaultTranslationService: 'google',
                enableQuickActions: true,
                enableContextMenu: true,
                enableDragDrop: true,
                autoHideDelay: 5000
            },
            general: {
                enableAnimations: true,
                showTooltips: true,
                enableKeyboardShortcuts: true,
                enableAdvancedFeatures: true,
                debugMode: false
            }
        };
        
        this.init();
    }

    /**
     * 初始化配置弹窗
     */
    init() {
        this.loadConfig();
        console.log('✅ 高级功能配置弹窗初始化完成');
    }

    /**
     * 显示配置弹窗
     */
    show() {
        if (this.isVisible) return;

        this.createDialog();
        this.isVisible = true;

        // 简单的淡入效果
        requestAnimationFrame(() => {
            this.dialogElement.style.opacity = '1';
        });
    }

    /**
     * 隐藏配置弹窗
     */
    hide() {
        if (!this.isVisible) return;

        this.dialogElement.style.opacity = '0';

        setTimeout(() => {
            if (this.dialogElement && this.dialogElement.parentNode) {
                this.dialogElement.parentNode.removeChild(this.dialogElement);
            }

            // 清理事件监听器
            if (this.keydownHandler) {
                document.removeEventListener('keydown', this.keydownHandler);
                this.keydownHandler = null;
            }

            this.dialogElement = null;
            this.isVisible = false;
        }, 200);
    }

    /**
     * 创建配置弹窗
     */
    createDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'advanced-config-dialog';
        dialog.innerHTML = `
            <div class="config-dialog-overlay"></div>
            <div class="config-dialog-content">
                <div class="config-dialog-header">
                    <h2>高级功能配置</h2>
                    <button class="config-dialog-close">✕</button>
                </div>
                
                <div class="config-dialog-body">
                    <div class="config-tabs">
                        <button class="config-tab active" data-tab="reading">
                            <span class="tab-icon">📖</span>
                            <span class="tab-text">阅读模式</span>
                        </button>
                        <button class="config-tab" data-tab="textOperations">
                            <span class="tab-icon">🔍</span>
                            <span class="tab-text">文本操作</span>
                        </button>
                        <button class="config-tab" data-tab="general">
                            <span class="tab-icon">⚙️</span>
                            <span class="tab-text">通用设置</span>
                        </button>
                    </div>
                    
                    <div class="config-content">
                        <div class="config-panel active" data-panel="reading">
                            ${this.createReadingPanel()}
                        </div>
                        <div class="config-panel" data-panel="textOperations">
                            ${this.createTextOperationsPanel()}
                        </div>
                        <div class="config-panel" data-panel="general">
                            ${this.createGeneralPanel()}
                        </div>
                    </div>
                </div>
                
                <div class="config-dialog-footer">
                    <button class="config-btn config-btn-secondary" data-action="reset">重置默认</button>
                    <button class="config-btn config-btn-secondary" data-action="export">导出配置</button>
                    <button class="config-btn config-btn-secondary" data-action="import">导入配置</button>
                    <button class="config-btn config-btn-primary" data-action="save">保存设置</button>
                </div>
            </div>
        `;
        
        // CSS样式已在样式表中定义，这里只需要添加到DOM
        document.body.appendChild(dialog);
        this.dialogElement = dialog;
        
        this.bindDialogEvents();
        this.loadConfigValues();
    }

    /**
     * 创建阅读模式配置面板
     */
    createReadingPanel() {
        return `
            <div class="config-section">
                <h3>基本设置</h3>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="reading.enabled">
                        启用阅读模式
                    </label>
                    <span class="config-desc">为预览窗口提供优化的阅读体验</span>
                </div>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="reading.autoEnable">
                        自动启用阅读模式
                    </label>
                    <span class="config-desc">打开预览窗口时自动启用阅读模式</span>
                </div>
            </div>
            
            <div class="config-section">
                <h3>默认样式</h3>
                <div class="config-row">
                    <label class="config-label">默认主题:</label>
                    <select class="config-select" data-key="reading.defaultTheme">
                        <option value="light">明亮</option>
                        <option value="dark">深色</option>
                        <option value="sepia">护眼</option>
                        <option value="highContrast">高对比度</option>
                    </select>
                </div>
                <div class="config-row">
                    <label class="config-label">默认字体:</label>
                    <select class="config-select" data-key="reading.defaultFontFamily">
                        <option value="system">系统字体</option>
                        <option value="serif">衬线字体</option>
                        <option value="sansSerif">无衬线字体</option>
                        <option value="mono">等宽字体</option>
                    </select>
                </div>
                <div class="config-row">
                    <label class="config-label">默认字号:</label>
                    <input type="range" class="config-range" data-key="reading.defaultFontSize" min="12" max="24" step="1">
                    <span class="config-value" data-display="reading.defaultFontSize">16px</span>
                </div>
                <div class="config-row">
                    <label class="config-label">默认行距:</label>
                    <input type="range" class="config-range" data-key="reading.defaultLineHeight" min="1.2" max="2.0" step="0.1">
                    <span class="config-value" data-display="reading.defaultLineHeight">1.6</span>
                </div>
                <div class="config-row">
                    <label class="config-label">默认宽度:</label>
                    <input type="range" class="config-range" data-key="reading.defaultMaxWidth" min="600" max="1200" step="50">
                    <span class="config-value" data-display="reading.defaultMaxWidth">800px</span>
                </div>
            </div>
            
            <div class="config-section">
                <h3>其他选项</h3>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="reading.savePreferences">
                        保存用户偏好
                    </label>
                    <span class="config-desc">记住用户在阅读模式中的设置调整</span>
                </div>
            </div>
        `;
    }

    /**
     * 创建文本操作配置面板
     */
    createTextOperationsPanel() {
        return `
            <div class="config-section">
                <h3>基本设置</h3>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="textOperations.enabled">
                        启用文本操作功能
                    </label>
                    <span class="config-desc">提供文本搜索、翻译等功能</span>
                </div>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="textOperations.enableQuickActions">
                        启用快速操作菜单
                    </label>
                    <span class="config-desc">选择文本时显示快速操作按钮</span>
                </div>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="textOperations.enableContextMenu">
                        启用右键菜单
                    </label>
                    <span class="config-desc">在预览窗口中右键选中文本时显示操作菜单</span>
                </div>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="textOperations.enableDragDrop">
                        启用拖拽操作
                    </label>
                    <span class="config-desc">支持拖拽文本到指定区域进行操作</span>
                </div>
            </div>
            
            <div class="config-section">
                <h3>默认服务</h3>
                <div class="config-row">
                    <label class="config-label">默认搜索引擎:</label>
                    <select class="config-select" data-key="textOperations.defaultSearchEngine">
                        <option value="google">Google</option>
                        <option value="baidu">百度</option>
                        <option value="bing">Bing</option>
                        <option value="duckduckgo">DuckDuckGo</option>
                    </select>
                </div>
                <div class="config-row">
                    <label class="config-label">默认翻译服务:</label>
                    <select class="config-select" data-key="textOperations.defaultTranslationService">
                        <option value="google">Google 翻译</option>
                        <option value="baidu">百度翻译</option>
                        <option value="youdao">有道翻译</option>
                    </select>
                </div>
            </div>
            
            <div class="config-section">
                <h3>行为设置</h3>
                <div class="config-row">
                    <label class="config-label">自动隐藏延迟:</label>
                    <input type="range" class="config-range" data-key="textOperations.autoHideDelay" min="1000" max="10000" step="500">
                    <span class="config-value" data-display="textOperations.autoHideDelay">5000ms</span>
                </div>
            </div>
        `;
    }

    /**
     * 创建通用设置配置面板
     */
    createGeneralPanel() {
        return `
            <div class="config-section">
                <h3>界面设置</h3>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="general.enableAnimations">
                        启用动画效果
                    </label>
                    <span class="config-desc">为界面元素添加平滑的动画过渡</span>
                </div>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="general.showTooltips">
                        显示工具提示
                    </label>
                    <span class="config-desc">鼠标悬停时显示功能说明</span>
                </div>
            </div>
            
            <div class="config-section">
                <h3>功能设置</h3>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="general.enableKeyboardShortcuts">
                        启用键盘快捷键
                    </label>
                    <span class="config-desc">支持键盘快捷键操作</span>
                </div>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="general.enableAdvancedFeatures">
                        启用高级功能
                    </label>
                    <span class="config-desc">启用所有高级功能和实验性特性</span>
                </div>
            </div>
            
            <div class="config-section">
                <h3>开发者选项</h3>
                <div class="config-row">
                    <label class="config-label">
                        <input type="checkbox" class="config-checkbox" data-key="general.debugMode">
                        调试模式
                    </label>
                    <span class="config-desc">启用详细的控制台日志输出</span>
                </div>
            </div>
        `;
    }

    /**
     * 绑定弹窗事件
     */
    bindDialogEvents() {
        const dialog = this.dialogElement;

        // 关闭按钮
        dialog.querySelector('.config-dialog-close').addEventListener('click', () => {
            this.hide();
        });

        // 点击遮罩层关闭
        dialog.querySelector('.config-dialog-overlay').addEventListener('click', () => {
            this.hide();
        });

        // 标签页切换
        dialog.querySelectorAll('.config-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                this.switchTab(tab.dataset.tab);
            });
        });

        // 底部按钮
        dialog.querySelectorAll('.config-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.handleAction(btn.dataset.action);
            });
        });

        // 配置项变化监听
        this.bindConfigEvents();

        // ESC键关闭
        this.keydownHandler = (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        };
        document.addEventListener('keydown', this.keydownHandler);
    }

    /**
     * 绑定配置项事件
     */
    bindConfigEvents() {
        const dialog = this.dialogElement;

        // 复选框
        dialog.querySelectorAll('.config-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.updateConfigValue(e.target.dataset.key, e.target.checked);
            });
        });

        // 选择框
        dialog.querySelectorAll('.config-select').forEach(select => {
            select.addEventListener('change', (e) => {
                this.updateConfigValue(e.target.dataset.key, e.target.value);
            });
        });

        // 滑块
        dialog.querySelectorAll('.config-range').forEach(range => {
            range.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                this.updateConfigValue(e.target.dataset.key, value);
                this.updateDisplayValue(e.target.dataset.key, value);
            });
        });
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        if (this.currentTab === tabName) return;

        const dialog = this.dialogElement;

        // 更新标签页状态
        dialog.querySelectorAll('.config-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });

        // 更新面板状态
        dialog.querySelectorAll('.config-panel').forEach(panel => {
            panel.classList.toggle('active', panel.dataset.panel === tabName);
        });

        this.currentTab = tabName;
    }

    /**
     * 处理按钮操作
     */
    handleAction(action) {
        switch (action) {
            case 'save':
                this.saveConfig();
                break;
            case 'reset':
                this.resetConfig();
                break;
            case 'export':
                this.exportConfig();
                break;
            case 'import':
                this.importConfig();
                break;
        }
    }

    /**
     * 更新配置值
     */
    updateConfigValue(key, value) {
        const keys = key.split('.');
        let target = this.config;

        for (let i = 0; i < keys.length - 1; i++) {
            if (!target[keys[i]]) target[keys[i]] = {};
            target = target[keys[i]];
        }

        target[keys[keys.length - 1]] = value;
    }

    /**
     * 获取配置值
     */
    getConfigValue(key) {
        const keys = key.split('.');
        let target = this.config;

        for (const k of keys) {
            if (target[k] === undefined) return undefined;
            target = target[k];
        }

        return target;
    }

    /**
     * 更新显示值
     */
    updateDisplayValue(key, value) {
        const displayElement = this.dialogElement.querySelector(`[data-display="${key}"]`);
        if (displayElement) {
            let displayValue = value;

            // 根据配置项类型格式化显示值
            if (key.includes('FontSize') || key.includes('MaxWidth')) {
                displayValue = `${value}px`;
            } else if (key.includes('autoHideDelay')) {
                displayValue = `${value}ms`;
            }

            displayElement.textContent = displayValue;
        }
    }

    /**
     * 加载配置值到界面
     */
    loadConfigValues() {
        // 使用通用方法加载不同类型的配置项
        this.loadConfigByType('.config-checkbox', (element, value) => {
            element.checked = value;
        });

        this.loadConfigByType('.config-select', (element, value) => {
            element.value = value;
        });

        this.loadConfigByType('.config-range', (element, value) => {
            element.value = value;
            this.updateDisplayValue(element.dataset.key, value);
        });
    }

    /**
     * 通用的配置加载方法
     */
    loadConfigByType(selector, setter) {
        this.dialogElement.querySelectorAll(selector).forEach(element => {
            const value = this.getConfigValue(element.dataset.key);
            if (value !== undefined) {
                setter(element, value);
            }
        });
    }

    /**
     * 保存配置
     */
    saveConfig() {
        try {
            // 保存到本地存储
            localStorage.setItem('linkPreview_advancedConfig', JSON.stringify(this.config));

            // 应用配置到各个组件
            this.applyConfig();

            LinkUtils.showToast('配置已保存', 2000, 'success');
            this.hide();
        } catch (error) {
            console.error('保存配置失败:', error);
            LinkUtils.showToast('保存配置失败', 2000, 'error');
        }
    }

    /**
     * 重置配置
     */
    resetConfig() {
        if (confirm('确定要重置所有配置到默认值吗？')) {
            this.config = this.getDefaultConfig();
            this.loadConfigValues();
            LinkUtils.showToast('配置已重置', 2000, 'info');
        }
    }

    /**
     * 导出配置
     */
    exportConfig() {
        try {
            const configData = {
                version: '1.0.0',
                timestamp: Date.now(),
                config: this.config
            };

            const dataStr = JSON.stringify(configData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `link-preview-config-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            LinkUtils.showToast('配置已导出', 2000, 'success');
        } catch (error) {
            console.error('导出配置失败:', error);
            LinkUtils.showToast('导出配置失败', 2000, 'error');
        }
    }

    /**
     * 导入配置
     */
    importConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const configData = JSON.parse(e.target.result);

                    if (configData.config) {
                        this.config = { ...this.getDefaultConfig(), ...configData.config };
                        this.loadConfigValues();
                        LinkUtils.showToast('配置已导入', 2000, 'success');
                    } else {
                        throw new Error('无效的配置文件格式');
                    }
                } catch (error) {
                    console.error('导入配置失败:', error);
                    LinkUtils.showToast('导入配置失败：文件格式错误', 2000, 'error');
                }
            };

            reader.readAsText(file);
        });

        input.click();
    }

    /**
     * 应用配置到各个组件
     */
    applyConfig() {
        // 应用到链接预览管理器
        if (this.linkPreviewManager) {
            // 更新基本配置
            this.linkPreviewManager.updateConfig({
                advanced: this.config
            });

            // 应用到文本操作处理器
            if (this.linkPreviewManager.textOperationsHandler) {
                this.linkPreviewManager.textOperationsHandler.updateConfig(this.config.textOperations);
                this.linkPreviewManager.textOperationsHandler.setEnabled(this.config.textOperations.enabled);
            }
        }
    }

    /**
     * 加载配置
     */
    loadConfig() {
        try {
            const saved = localStorage.getItem('linkPreview_advancedConfig');
            if (saved) {
                const savedConfig = JSON.parse(saved);
                this.config = { ...this.getDefaultConfig(), ...savedConfig };
            }
        } catch (error) {
            console.error('加载高级配置失败:', error);
            this.config = this.getDefaultConfig();
        }
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            reading: {
                enabled: true,
                defaultTheme: 'light',
                defaultFontSize: 16,
                defaultLineHeight: 1.6,
                defaultMaxWidth: 800,
                defaultFontFamily: 'system',
                autoEnable: false,
                savePreferences: true
            },
            textOperations: {
                enabled: true,
                defaultSearchEngine: 'google',
                defaultTranslationService: 'google',
                enableQuickActions: true,
                enableContextMenu: true,
                enableDragDrop: true,
                autoHideDelay: 5000
            },
            general: {
                enableAnimations: true,
                showTooltips: true,
                enableKeyboardShortcuts: true,
                enableAdvancedFeatures: true,
                debugMode: false
            }
        };
    }

    /**
     * 获取当前配置
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * 销毁配置弹窗
     */
    destroy() {
        this.hide();
        console.log('💥 高级功能配置弹窗已销毁');
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.AdvancedConfigDialog = AdvancedConfigDialog;
}
