/* 链接预览窗口基础样式 */
.link-preview-window {
    position: fixed;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    user-select: none;
    z-index: 10000;
    min-width: 300px;
    min-height: 200px;
    backdrop-filter: blur(10px);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .link-preview-window {
        background: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }
    
    .preview-header {
        background: #3a3a3a !important;
        border-bottom-color: #404040 !important;
    }
    
    .preview-url {
        color: #cccccc !important;
    }
    
    .preview-loading {
        background: #2d2d2d !important;
    }
    
    .loading-text {
        color: #cccccc !important;
    }
}

/* 预览窗口头部 */
.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    cursor: move;
    min-height: 40px;
}

.preview-title {
    flex: 1;
    min-width: 0;
}

.preview-url {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 400px;
}

.preview-controls {
    display: flex;
    gap: 4px;
}

.preview-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: background-color 0.2s;
    color: inherit;
}

.preview-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.close-btn:hover {
    background: #ff5f56;
    color: white;
}

.reading-mode-btn:hover {
    background: #007bff;
    color: white;
}

.minimize-btn:hover {
    background: #ffc107;
    color: white;
}

/* 预览内容区域 */
.preview-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.preview-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

/* 加载状态 */
.preview-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    z-index: 10;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 12px;
    font-size: 14px;
    color: #666;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 */
.preview-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px;
    text-align: center;
    background: inherit;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.error-message {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
}

.retry-btn {
    padding: 8px 16px;
    background: #1976d2;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background: #1565c0;
}

/* 调整大小手柄 */
.preview-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 16px;
    height: 16px;
    cursor: nw-resize;
    background: linear-gradient(-45deg, transparent 30%, #e0e0e0 30%, #e0e0e0 70%, transparent 70%);
    opacity: 0.6;
    transition: opacity 0.2s;
}

.preview-resize-handle:hover {
    opacity: 1;
}

/* 全屏模式 */
.link-preview-window.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 99999 !important;
    border-radius: 0 !important;
}

/* 最小化状态 */
.link-preview-window.minimized {
    height: 40px !important;
    overflow: hidden;
}

.link-preview-window.minimized .preview-content {
    display: none;
}

.link-preview-window.minimized .preview-resize-handle {
    display: none;
}

/* 动画效果 */
.link-preview-window {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.link-preview-window.entering {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

.link-preview-window.entered {
    opacity: 1;
    transform: scale(1) translateY(0);
}

.link-preview-window.exiting {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

/* 响应式适配 */
@media (max-width: 768px) {
    .link-preview-window {
        width: calc(100vw - 20px) !important;
        height: calc(100vh - 20px) !important;
        top: 10px !important;
        left: 10px !important;
        border-radius: 8px;
    }
    
    .preview-url {
        max-width: 200px;
    }
}

/* 焦点状态 */
.link-preview-window.focused {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #1976d2;
}

/* 拖拽状态 */
.link-preview-window.dragging {
    cursor: grabbing;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
    transform: rotate(1deg);
}

.link-preview-window.dragging .preview-header {
    cursor: grabbing;
}

/* 调整大小状态 */
.link-preview-window.resizing {
    transition: none;
}

/* 多窗口层叠效果 */
.link-preview-window:nth-of-type(2) {
    transform: translateX(20px) translateY(20px);
}

.link-preview-window:nth-of-type(3) {
    transform: translateX(40px) translateY(40px);
}

/* 工具提示样式 */
.preview-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* 状态指示器 */
.preview-status {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4caf50;
    z-index: 11;
}

.preview-status.loading {
    background: #ff9800;
    animation: pulse 1.5s infinite;
}

.preview-status.error {
    background: #f44336;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}
