/**
 * 图标缓存管理器
 * 解决图标URL持久化存储问题，避免每次刷新重新获取图标
 */

class IconCacheManager {
    constructor() {
        this.cacheKey = 'moment-icon-cache';
        this.cacheVersion = '1.0.0';
        this.maxCacheAge = Infinity; // 永久缓存，不过期
        this.maxCacheSize = 1000; // 最大缓存数量
        
        // 内存缓存，提高访问速度
        this.memoryCache = new Map();
        
        // 初始化时加载持久化缓存到内存
        this.loadCacheToMemory();
    }

    /**
     * 从localStorage加载缓存到内存
     */
    loadCacheToMemory() {
        try {
            const cached = localStorage.getItem(this.cacheKey);
            if (cached) {
                const cacheData = JSON.parse(cached);
                
                // 检查版本兼容性
                if (cacheData.version === this.cacheVersion) {
                    // 加载所有缓存（永久缓存，不检查过期）
                    Object.entries(cacheData.icons || {}).forEach(([key, value]) => {
                        this.memoryCache.set(key, value);
                    });
                }
            }
        } catch (error) {
            console.warn('加载图标缓存失败:', error);
            this.clearCache();
        }
    }

    /**
     * 保存内存缓存到localStorage
     */
    saveCacheToStorage() {
        try {
            const cacheData = {
                version: this.cacheVersion,
                timestamp: Date.now(),
                icons: {}
            };
            
            // 转换Map到普通对象
            this.memoryCache.forEach((value, key) => {
                cacheData.icons[key] = value;
            });
            
            localStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
        } catch (error) {
            console.error('保存图标缓存失败:', error);
            // 如果存储空间不足，清理部分缓存
            this.cleanupCache();
            try {
                localStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
            } catch (retryError) {
                console.error('重试保存缓存失败:', retryError);
            }
        }
    }

    /**
     * 生成缓存键
     * @param {string} url 网站URL
     * @param {number} size 图标大小
     * @returns {string} 缓存键
     */
    generateCacheKey(url, size = 32) {
        try {
            const normalizedUrl = this.normalizeUrl(url);
            return `${normalizedUrl}_${size}`;
        } catch (error) {
            return `${url}_${size}`;
        }
    }

    /**
     * 标准化URL
     * @param {string} url 原始URL
     * @returns {string} 标准化后的URL
     */
    normalizeUrl(url) {
        if (!url) return '';
        
        try {
            // 添加协议
            if (!url.startsWith('http')) {
                url = 'https://' + url;
            }
            
            const urlObj = new URL(url);
            return urlObj.hostname.toLowerCase();
        } catch (error) {
            return url.toLowerCase();
        }
    }

    /**
     * 获取缓存的图标
     * @param {string} url 网站URL
     * @param {number} size 图标大小
     * @returns {Object|null} 缓存的图标数据
     */
    getCachedIcon(url, size = 32) {
        const cacheKey = this.generateCacheKey(url, size);
        const cached = this.memoryCache.get(cacheKey);

        if (cached) {
            // 更新访问时间（永久缓存，不检查过期）
            cached.lastAccess = Date.now();
            return cached;
        }

        return null;
    }

    /**
     * 缓存图标
     * @param {string} url 网站URL
     * @param {string} iconUrl 图标URL
     * @param {number} size 图标大小
     * @param {Object} metadata 额外元数据
     */
    cacheIcon(url, iconUrl, size = 32, metadata = {}) {
        if (!url || !iconUrl) return;
        
        const cacheKey = this.generateCacheKey(url, size);
        const cacheData = {
            url: iconUrl,
            originalUrl: url,
            size: size,
            timestamp: Date.now(),
            lastAccess: Date.now(),
            source: metadata.source || 'unknown',
            verified: metadata.verified || false,
            ...metadata
        };
        
        this.memoryCache.set(cacheKey, cacheData);
        
        // 检查缓存大小，如果超过限制则清理
        if (this.memoryCache.size > this.maxCacheSize) {
            this.cleanupCache();
        }
        
        // 异步保存到localStorage（避免阻塞）
        setTimeout(() => this.saveCacheToStorage(), 100);
    }

    /**
     * 批量缓存图标
     * @param {Array} iconList 图标列表 [{url, iconUrl, size, metadata}]
     */
    batchCacheIcons(iconList) {
        iconList.forEach(item => {
            this.cacheIcon(item.url, item.iconUrl, item.size, item.metadata);
        });
        
        // 批量保存
        this.saveCacheToStorage();
    }

    /**
     * 预加载图标
     * @param {Array} urls URL列表
     * @param {number} size 图标大小
     */
    async preloadIcons(urls, size = 32) {
        const faviconManager = window.app?.shortcutManager?.faviconManager;
        if (!faviconManager) return;
        
        const promises = urls.map(async (url) => {
            const cached = this.getCachedIcon(url, size);
            if (cached) return cached;
            
            try {
                const iconUrl = await faviconManager.getFavicon(url, size);
                if (iconUrl && iconUrl !== faviconManager.getDefaultIcon()) {
                    this.cacheIcon(url, iconUrl, size, {
                        source: 'preload',
                        verified: true
                    });
                    return { url, iconUrl };
                }
            } catch (error) {
                console.warn(`预加载图标失败: ${url}`, error);
            }
            
            return null;
        });
        
        const results = await Promise.allSettled(promises);
        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
        
        console.log(`图标预加载完成: ${successful}/${urls.length}`);
    }

    /**
     * 清理缓存
     * @param {boolean} force 是否强制清理所有缓存
     */
    cleanupCache(force = false) {
        if (force) {
            this.memoryCache.clear();
            localStorage.removeItem(this.cacheKey);
            return;
        }

        // 只在缓存数量超过限制时清理最久未访问的
        if (this.memoryCache.size > this.maxCacheSize) {
            const sortedEntries = Array.from(this.memoryCache.entries())
                .sort(([,a], [,b]) => a.lastAccess - b.lastAccess);

            const excessCount = this.memoryCache.size - this.maxCacheSize;
            const toDelete = [];

            for (let i = 0; i < excessCount; i++) {
                toDelete.push(sortedEntries[i][0]);
            }

            // 执行删除
            toDelete.forEach(key => this.memoryCache.delete(key));

            // 保存更新后的缓存
            this.saveCacheToStorage();

            console.log(`清理了 ${toDelete.length} 个图标缓存项（基于访问时间）`);
        }
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 缓存统计
     */
    getCacheStats() {
        let totalSize = 0;

        this.memoryCache.forEach(value => {
            totalSize += JSON.stringify(value).length;
        });

        return {
            total: this.memoryCache.size,
            valid: this.memoryCache.size, // 永久缓存，全部有效
            expired: 0, // 永久缓存，无过期
            maxSize: this.maxCacheSize,
            maxAge: this.maxCacheAge,
            sizeBytes: totalSize,
            hitRate: this.calculateHitRate()
        };
    }

    /**
     * 计算缓存命中率
     * @returns {number} 命中率百分比
     */
    calculateHitRate() {
        // 这里可以实现更复杂的命中率统计
        // 暂时返回一个估算值
        return Math.min(95, (this.memoryCache.size / this.maxCacheSize) * 100);
    }

    /**
     * 导出缓存数据
     * @returns {Object} 缓存数据
     */
    exportCache() {
        const cacheData = {};
        this.memoryCache.forEach((value, key) => {
            cacheData[key] = value;
        });
        
        return {
            version: this.cacheVersion,
            timestamp: Date.now(),
            icons: cacheData,
            stats: this.getCacheStats()
        };
    }

    /**
     * 导入缓存数据
     * @param {Object} cacheData 缓存数据
     */
    importCache(cacheData) {
        if (!cacheData || !cacheData.icons) return;
        
        try {
            // 清空现有缓存
            this.memoryCache.clear();
            
            // 导入新缓存
            Object.entries(cacheData.icons).forEach(([key, value]) => {
                // 验证数据完整性
                if (value.url && value.timestamp) {
                    this.memoryCache.set(key, value);
                }
            });
            
            // 保存到localStorage
            this.saveCacheToStorage();
            
            console.log(`导入了 ${this.memoryCache.size} 个图标缓存项`);
        } catch (error) {
            console.error('导入缓存失败:', error);
        }
    }

    /**
     * 清除所有缓存
     */
    clearCache() {
        this.memoryCache.clear();
        localStorage.removeItem(this.cacheKey);
        console.log('已清除所有图标缓存');
    }

    /**
     * 验证图标URL是否有效
     * @param {string} iconUrl 图标URL
     * @returns {Promise<boolean>} 是否有效
     */
    async validateIconUrl(iconUrl) {
        return new Promise((resolve) => {
            const img = new Image();
            const timeout = setTimeout(() => {
                resolve(false);
            }, 5000);
            
            img.onload = () => {
                clearTimeout(timeout);
                resolve(true);
            };
            
            img.onerror = () => {
                clearTimeout(timeout);
                resolve(false);
            };
            
            img.src = iconUrl;
        });
    }

    /**
     * 获取图标并自动缓存
     * @param {string} url 网站URL
     * @param {number} size 图标大小
     * @returns {Promise<string|null>} 图标URL
     */
    async getIcon(url, size = 32) {
        // 先检查缓存
        const cached = this.getCachedIcon(url, size);
        if (cached) {
            return cached.url;
        }
        
        // 缓存未命中，获取新图标
        const faviconManager = window.app?.shortcutManager?.faviconManager;
        if (!faviconManager) return null;
        
        try {
            const iconUrl = await faviconManager.getFavicon(url, size);
            if (iconUrl && iconUrl !== faviconManager.getDefaultIcon()) {
                // 验证图标有效性
                const isValid = await this.validateIconUrl(iconUrl);
                if (isValid) {
                    // 缓存有效图标
                    this.cacheIcon(url, iconUrl, size, {
                        source: 'favicon-manager',
                        verified: true
                    });
                    return iconUrl;
                }
            }
        } catch (error) {
            console.warn(`获取图标失败: ${url}`, error);
        }
        
        return null;
    }
}

// 导出类
window.IconCacheManager = IconCacheManager;
