/**
 * 配置管理器
 * 负责加载和管理静态配置文件
 */
class ConfigManager {
    constructor() {
        this.config = null;
        this.shortcuts = [];
        this.shortcutConfig = new ShortcutConfig();
    }

    /**
     * 初始化配置管理器
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async initialize() {
        try {
            console.log('🔄 开始加载快捷方式配置...');
            this.loadConfig();
            console.log('✅ 配置管理器初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 配置管理器初始化失败:', error);
            // 如果配置加载失败，使用默认配置
            this.loadDefaultConfig();
            return false;
        }
    }

    /**
     * 加载配置数据
     */
    loadConfig() {
        try {
            // 从ShortcutConfig获取配置数据（已包含base64图标）
            this.config = this.shortcutConfig.getConfigInfo();
            this.shortcuts = this.shortcutConfig.getShortcuts();

            console.log(`📋 已加载 ${this.shortcuts.length} 个快捷方式配置（含base64图标）`);
            console.log(`📅 配置版本: ${this.config.version}, 更新时间: ${this.config.lastUpdated}`);

            // 验证配置数据
            this.validateConfig();

        } catch (error) {
            console.error('配置数据加载失败:', error);
            throw error;
        }
    }

    /**
     * 加载默认配置（当配置文件不可用时）
     */
    loadDefaultConfig() {
        console.log('🔄 使用默认配置...');
        
        this.config = {
            version: '3.0.0',
            lastUpdated: new Date().toISOString().split('T')[0],
            shortcuts: [
                {
                    id: 1,
                    name: 'Google',
                    url: 'https://www.google.com',
                    src: '',
                    icon: '🔍',
                    order: 1,
                    category: 'search',
                    searchMode: 'direct',
                    pageMode: 'normal',
                    type: 'icon'
                },
                {
                    id: 2,
                    name: 'Bing',
                    url: 'https://www.bing.com',
                    src: '',
                    icon: '🌐',
                    order: 2,
                    category: 'search',
                    searchMode: 'direct',
                    pageMode: 'normal',
                    type: 'icon'
                },
                {
                    id: 3,
                    name: '百度',
                    url: 'https://www.baidu.com',
                    src: '',
                    icon: '🔎',
                    order: 3,
                    category: 'search',
                    searchMode: 'direct',
                    pageMode: 'normal',
                    type: 'icon'
                }
            ]
        };
        
        this.shortcuts = this.config.shortcuts;
        console.log(`📋 已加载 ${this.shortcuts.length} 个默认快捷方式`);
    }

    /**
     * 验证配置数据
     */
    validateConfig() {
        if (!this.config || !Array.isArray(this.shortcuts)) {
            throw new Error('配置数据格式无效');
        }

        // 验证每个快捷方式的必需字段
        this.shortcuts.forEach((shortcut, index) => {
            const requiredFields = ['id', 'name', 'url'];
            const missingFields = requiredFields.filter(field => !shortcut[field]);
            
            if (missingFields.length > 0) {
                console.warn(`快捷方式 ${index + 1} 缺少必需字段: ${missingFields.join(', ')}`);
            }

            // 设置默认值
            shortcut.src = shortcut.src || '';
            shortcut.icon = shortcut.icon || '🌐';
            shortcut.order = shortcut.order || index + 1;
            shortcut.category = shortcut.category || 'default';
            shortcut.searchMode = shortcut.searchMode || 'direct';
            shortcut.pageMode = shortcut.pageMode || 'normal';
            shortcut.type = shortcut.type || 'icon';
        });

        console.log('✅ 配置数据验证通过');
    }

    /**
     * 获取所有快捷方式
     * @returns {Array} 快捷方式数组
     */
    getShortcuts() {
        return [...this.shortcuts];
    }

    /**
     * 根据ID获取快捷方式
     * @param {number} id 快捷方式ID
     * @returns {Object|null} 快捷方式对象
     */
    getShortcutById(id) {
        return this.shortcuts.find(shortcut => shortcut.id === id) || null;
    }

    /**
     * 根据分类获取快捷方式
     * @param {string} category 分类名称
     * @returns {Array} 快捷方式数组
     */
    getShortcutsByCategory(category) {
        return this.shortcuts.filter(shortcut => shortcut.category === category);
    }

    /**
     * 根据页面模式获取快捷方式
     * @param {string} pageMode 页面模式 (normal/quick)
     * @returns {Array} 快捷方式数组
     */
    getShortcutsByPageMode(pageMode) {
        return this.shortcuts.filter(shortcut => shortcut.pageMode === pageMode);
    }

    /**
     * 获取配置信息
     * @returns {Object} 配置信息
     */
    getConfigInfo() {
        return {
            version: this.config?.version || 'unknown',
            lastUpdated: this.config?.lastUpdated || 'unknown',
            shortcutCount: this.shortcuts.length,
            categories: [...new Set(this.shortcuts.map(s => s.category))],
            hasConfigFile: !!this.config
        };
    }

    /**
     * 检查快捷方式是否有图标URL
     * @param {Object} shortcut 快捷方式对象
     * @returns {boolean} 是否有图标URL
     */
    hasIconUrl(shortcut) {
        return !!(shortcut.src && shortcut.src.trim() !== '');
    }

    /**
     * 获取需要获取图标的快捷方式
     * @returns {Array} 需要获取图标的快捷方式数组
     */
    getShortcutsNeedingIcons() {
        return this.shortcuts.filter(shortcut => !this.hasIconUrl(shortcut));
    }

    /**
     * 获取已有图标的快捷方式
     * @returns {Array} 已有图标的快捷方式数组
     */
    getShortcutsWithIcons() {
        return this.shortcuts.filter(shortcut => this.hasIconUrl(shortcut));
    }




}

// 导出类
window.ConfigManager = ConfigManager;
