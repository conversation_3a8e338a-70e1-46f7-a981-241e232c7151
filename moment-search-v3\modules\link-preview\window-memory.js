/**
 * 窗口记忆系统
 * 负责记住用户的窗口大小、位置偏好，提供智能位置建议
 */
class WindowMemory {
    constructor() {
        this.storageKey = 'linkPreview_windowMemory';
        this.maxMemoryEntries = 100; // 最大记忆条目数
        this.memory = this.loadMemory();
        this.defaultConfig = {
            width: 800,
            height: 600,
            position: null
        };
        
        // 统计信息
        this.stats = {
            totalSaves: 0,
            totalLoads: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        this.init();
    }

    /**
     * 初始化记忆系统
     */
    init() {
        // 清理过期的记忆条目
        this.cleanupExpiredEntries();
        
        // 定期保存记忆数据
        this.setupAutoSave();
        
        console.log('✅ 窗口记忆系统初始化完成');
    }

    /**
     * 获取窗口配置
     */
    getWindowConfig(url) {
        this.stats.totalLoads++;
        
        const urlKey = LinkUtils.normalizeUrl(url);
        const entry = this.memory[urlKey];
        
        if (entry && this.isEntryValid(entry)) {
            this.stats.cacheHits++;
            
            // 更新访问时间
            entry.lastAccessed = Date.now();
            entry.accessCount = (entry.accessCount || 0) + 1;
            
            console.log(`📋 窗口记忆命中: ${urlKey}`);
            return {
                width: entry.width,
                height: entry.height,
                position: entry.position
            };
        }
        
        this.stats.cacheMisses++;
        console.log(`📋 窗口记忆未命中: ${urlKey}`);
        
        // 返回智能推荐配置
        return this.getSmartRecommendation(url);
    }

    /**
     * 保存窗口配置
     */
    saveWindowConfig(url, config) {
        this.stats.totalSaves++;
        
        const urlKey = LinkUtils.normalizeUrl(url);
        const now = Date.now();
        
        // 获取现有条目或创建新条目
        const existingEntry = this.memory[urlKey] || {};
        
        // 合并配置
        const entry = {
            ...existingEntry,
            ...config,
            url: url,
            lastModified: now,
            lastAccessed: now,
            accessCount: (existingEntry.accessCount || 0) + 1
        };
        
        // 验证配置有效性
        if (this.isConfigValid(entry)) {
            this.memory[urlKey] = entry;
            
            // 检查记忆条目数量限制
            this.enforceMemoryLimit();
            
            console.log(`💾 窗口配置已保存: ${urlKey}`, entry);
        } else {
            console.warn(`⚠️ 无效的窗口配置，跳过保存: ${urlKey}`, config);
        }
    }

    /**
     * 获取智能推荐配置
     */
    getSmartRecommendation(url) {
        // 基于域名的推荐
        const domain = LinkUtils.extractDomain(url);
        const domainEntries = this.getEntriesByDomain(domain);
        
        if (domainEntries.length > 0) {
            // 计算该域名的平均配置
            const avgConfig = this.calculateAverageConfig(domainEntries);
            console.log(`🧠 基于域名的智能推荐: ${domain}`, avgConfig);
            return avgConfig;
        }
        
        // 基于全局使用频率的推荐
        const popularConfig = this.getMostPopularConfig();
        if (popularConfig) {
            console.log('🧠 基于全局流行度的智能推荐', popularConfig);
            return popularConfig;
        }
        
        // 返回默认配置
        console.log('🧠 使用默认配置');
        return { ...this.defaultConfig };
    }

    /**
     * 获取所有记忆条目
     */
    getAllEntries() {
        return Object.values(this.memory);
    }

    /**
     * 获取记忆条目数量
     */
    getEntryCount() {
        return Object.keys(this.memory).length;
    }

    /**
     * 根据域名获取条目
     */
    getEntriesByDomain(domain) {
        return this.getAllEntries().filter(entry => {
            return LinkUtils.extractDomain(entry.url) === domain;
        });
    }

    /**
     * 计算平均配置
     */
    calculateAverageConfig(entries) {
        if (entries.length === 0) return { ...this.defaultConfig };
        
        const sum = entries.reduce((acc, entry) => {
            acc.width += entry.width || this.defaultConfig.width;
            acc.height += entry.height || this.defaultConfig.height;
            acc.count++;
            return acc;
        }, { width: 0, height: 0, count: 0 });
        
        return {
            width: Math.round(sum.width / sum.count),
            height: Math.round(sum.height / sum.count),
            position: null // 位置不使用平均值
        };
    }

    /**
     * 获取最流行的配置
     */
    getMostPopularConfig() {
        const entries = this.getAllEntries();
        if (entries.length === 0) return null;
        
        // 按访问次数排序
        entries.sort((a, b) => (b.accessCount || 0) - (a.accessCount || 0));
        
        const mostPopular = entries[0];
        return {
            width: mostPopular.width,
            height: mostPopular.height,
            position: null // 不复用位置
        };
    }



    /**
     * 验证条目有效性
     */
    isEntryValid(entry) {
        const now = Date.now();
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
        
        return entry.lastAccessed && 
               (now - entry.lastAccessed) < maxAge &&
               this.isConfigValid(entry);
    }

    /**
     * 验证配置有效性
     */
    isConfigValid(config) {
        return config.width > 200 && 
               config.width < window.screen.width &&
               config.height > 150 && 
               config.height < window.screen.height;
    }

    /**
     * 清理过期条目
     */
    cleanupExpiredEntries() {
        const now = Date.now();
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天

        const expiredKeys = Object.keys(this.memory).filter(key => {
            const entry = this.memory[key];
            return !entry.lastAccessed || (now - entry.lastAccessed) > maxAge;
        });

        expiredKeys.forEach(key => delete this.memory[key]);

        if (expiredKeys.length > 0) {
            console.log(`🧹 清理了 ${expiredKeys.length} 个过期的窗口记忆条目`);
        }
    }

    /**
     * 强制执行记忆限制
     */
    enforceMemoryLimit() {
        const entries = Object.entries(this.memory);
        
        if (entries.length > this.maxMemoryEntries) {
            // 按最后访问时间排序，删除最旧的条目
            entries.sort((a, b) => (a[1].lastAccessed || 0) - (b[1].lastAccessed || 0));
            
            const toDelete = entries.length - this.maxMemoryEntries;
            for (let i = 0; i < toDelete; i++) {
                delete this.memory[entries[i][0]];
            }
            
            console.log(`🧹 删除了 ${toDelete} 个最旧的窗口记忆条目`);
        }
    }

    /**
     * 设置自动保存
     */
    setupAutoSave() {
        // 每5分钟自动保存一次
        setInterval(() => {
            this.saveToStorage();
        }, 5 * 60 * 1000);
        
        // 页面卸载时保存
        window.addEventListener('beforeunload', () => {
            this.saveToStorage();
        });
    }

    /**
     * 从存储加载记忆数据
     */
    loadMemory() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const data = JSON.parse(stored);
                console.log(`📋 加载了 ${Object.keys(data).length} 个窗口记忆条目`);
                return data;
            }
        } catch (error) {
            console.error('❌ 加载窗口记忆数据失败:', error);
        }
        
        return {};
    }

    /**
     * 保存记忆数据到存储
     */
    saveToStorage() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.memory));
            console.log(`💾 保存了 ${this.getEntryCount()} 个窗口记忆条目`);
        } catch (error) {
            console.error('❌ 保存窗口记忆数据失败:', error);
        }
    }

    /**
     * 清除所有记忆数据
     */
    clearMemory() {
        this.memory = {};
        localStorage.removeItem(this.storageKey);
        console.log('🧹 已清除所有窗口记忆数据');
    }

    /**
     * 获取记忆统计信息
     */
    getStats() {
        return {
            ...this.stats,
            totalEntries: this.getEntryCount(),
            hitRate: this.stats.totalLoads > 0 ?
                (this.stats.cacheHits / this.stats.totalLoads * 100).toFixed(2) + '%' : '0%'
        };
    }

    /**
     * 导出记忆数据
     */
    exportMemory() {
        return {
            version: '1.0.0',
            timestamp: Date.now(),
            memory: this.memory,
            stats: this.getStats()
        };
    }

    /**
     * 导入记忆数据
     */
    importMemory(data) {
        try {
            if (data.memory) {
                this.memory = { ...this.memory, ...data.memory };
                this.saveToStorage();
                console.log('✅ 窗口记忆数据导入成功');
                return true;
            }
        } catch (error) {
            console.error('❌ 窗口记忆数据导入失败:', error);
        }
        return false;
    }

    /**
     * 销毁记忆系统
     */
    destroy() {
        this.saveToStorage();
        console.log('💥 窗口记忆系统已销毁');
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.WindowMemory = WindowMemory;
}
