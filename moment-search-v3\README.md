# Moment Search V3 - 新标签页扩展

> 简洁美观的新标签页，支持多平台搜索和快捷方式管理

## ✨ 功能特色

### 🔍 智能搜索
- **多平台搜索** - 支持Google、百度、知乎、B站等9个主流平台
- **智能别名匹配** - 输入 "g 关键词" 直接在Google搜索
- **全平台搜索** - 一键在所有平台同时搜索
- **搜索历史** - 自动保存搜索记录，支持快速重复搜索

### 🚀 快捷方式
- **自定义快捷方式** - 最多添加20个常用网站
- **拖拽排序** - 自由调整快捷方式顺序
- **右键菜单** - 快速编辑和删除快捷方式
- **预设网站** - 内置常用网站，开箱即用

### 🎨 个性化设置
- **主题切换** - 支持浅色、深色和自动主题
- **自定义壁纸** - 上传个人图片作为背景
- **时间显示** - 实时显示时间和日期
- **灵活配置** - 所有功能都可以自由开关

## 🚀 快速开始

### 安装方法

1. **下载扩展文件**
   ```bash
   # 克隆或下载项目文件
   git clone https://github.com/your-repo/moment-search-v3.git
   ```

2. **加载到Chrome**
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `moment-search-v3` 文件夹

3. **开始使用**
   - 打开新标签页即可看到Moment Search界面
   - 首次使用会自动加载默认快捷方式

### 基本使用

#### 搜索功能
```
# 直接搜索（默认全平台）
输入关键词 → 回车

# 指定平台搜索
g 关键词        # Google搜索
zh 关键词       # 知乎搜索
b 关键词        # B站搜索
tb 关键词       # 淘宝搜索
```

#### 快捷方式管理
- **添加**: 点击设置 → 快捷方式设置 → 添加快捷方式
- **编辑**: 双击快捷方式图标
- **删除**: 右键点击快捷方式 → 删除
- **排序**: 拖拽快捷方式图标调整位置

## 🔧 高级功能

### 数据管理
- **导出数据**: 设置 → 数据管理 → 导出数据
- **导入数据**: 设置 → 数据管理 → 导入数据
- **清空数据**: 设置 → 数据管理 → 清空所有数据

### 搜索平台别名
| 平台 | 别名 | 示例 |
|------|------|------|
| Google | g, gg, google | `g 人工智能` |
| 百度 | bd, 百度, baidu | `bd 机器学习` |
| 知乎 | zh, 知乎, zhihu | `zh 编程问题` |
| B站 | b, b站, bili | `b 技术教程` |
| 小红书 | xhs, 小红书 | `xhs 美食推荐` |
| 淘宝 | tb, 淘宝, taobao | `tb 数码产品` |
| GitHub | gh, git, github | `gh 开源项目` |
| YouTube | yt, you, youtube | `yt 编程视频` |

### 键盘快捷键
- `Enter` - 执行搜索
- `Escape` - 关闭设置面板
- `双击` - 编辑快捷方式
- `右键` - 显示快捷方式菜单

## 🎨 界面预览

```
┌─────────────────────────────────────────┐
│  ⚙️ 🖼️                        15:30    │
│                              2025年1月18日│
│                                         │
│        ┌─────────────────────┐          │
│        │  🔍 输入关键词...    │          │
│        └─────────────────────┘          │
│                                         │
│    🧠 📺 🔍 📖 🛒 💻 ▶️ 🌐 📱 ⭐      │
│    📊 🎵 📧 📝 🎮 📷 🗂️ 🔧 📈 💡      │
│                                         │
└─────────────────────────────────────────┘
```

## 📊 性能特点

- **快速加载** - 页面加载时间 < 300ms
- **内存友好** - 内存占用 < 30MB
- **响应迅速** - 搜索响应时间 < 100ms
- **体积小巧** - 扩展包大小 < 2MB

## 🔒 隐私保护

- **完全本地化** - 所有数据存储在本地
- **无数据收集** - 不收集任何用户信息
- **无网络请求** - 除搜索外无其他网络活动
- **开源透明** - 代码完全开源可审查

## 🛠️ 技术栈

- **前端**: HTML5 + CSS3 + 原生JavaScript
- **扩展**: Chrome Extension Manifest V3
- **存储**: localStorage + Chrome Storage API
- **构建**: 无构建工具，直接运行

## 📝 更新日志

### V3.0.0 (2025-01-18)
- 🎉 全新的新标签页界面设计
- 🔍 完整保留V2的搜索功能
- 🚀 新增快捷方式管理系统
- 🎨 新增主题和壁纸自定义
- ⏰ 新增时间和日期显示
- 📊 新增数据导入导出功能
- 🔧 完整的设置管理系统

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境
1. 克隆项目到本地
2. 在Chrome中加载扩展
3. 修改代码后刷新扩展页面测试

### 提交规范
- 功能: `feat: 添加新功能`
- 修复: `fix: 修复某个问题`
- 文档: `docs: 更新文档`
- 样式: `style: 样式调整`

## 📞 支持

- **GitHub Issues**: [提交问题](https://github.com/your-repo/moment-search-v3/issues)
- **功能建议**: [提出建议](https://github.com/your-repo/moment-search-v3/discussions)
- **使用文档**: [查看文档](./docs/)

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**Moment Search V3** - 让每个新标签页都充满可能性 ✨
