# 链接预览功能实现指南

## 项目概述

本文档详细描述了在 moment-search-v3 项目中实现链接预览功能的完整方案。该功能对标 NoTab 扩展，提供在当前页面预览链接、文本拖拽搜索/翻译等高级功能。

**重要说明**：此功能设计为模块化架构，便于后期独立提取为单独的浏览器扩展程序。

## 功能需求概览

### 核心功能
1. **链接预览系统**：Alt+点击链接在当前页面内弹出预览窗口
2. **多种触发方式**：Alt+点击、右键菜单、悬停延迟、长按、拖拽
3. **阅读模式**：提取页面主要内容，移除干扰元素
4. **多链接预览**：支持同时打开多个预览窗口
5. **文本拖拽搜索**：拖拽选中文本触发搜索
6. **文本拖拽翻译**：拖拽选中文本触发翻译
7. **窗口管理系统**：预览窗口大小记忆、位置记忆、主题样式自定义

## 技术架构设计

### 核心类结构

```javascript
// 主控制器
class LinkPreviewManager {
    constructor(options = {})
    init()
    createPreview(url, options)
    destroyPreview(id)
    handleLinkClick(event)
}

// 预览窗口管理器
class PreviewWindowManager {
    constructor()
    createWindow(url, options)
    destroyWindow(id)
    arrangeWindows()
    limitWindows(maxCount)
}

// 事件管理器
class LinkPreviewEventManager {
    constructor()
    bindGlobalEvents()
    handleKeyboardState(event)
    handleLinkInteraction(event)
}

// 文本操作管理器
class TextActionManager {
    constructor()
    handleTextDrag(event)
    performSearch(text, engine)
    performTranslation(text, service)
}

// 预览窗口组件
class PreviewWindow {
    constructor(url, options)
    render()
    loadContent()
    enableReadingMode()
    destroy()
}
```

### 配置系统

```javascript
const DEFAULT_LINK_PREVIEW_CONFIG = {
    enabled: true,
    triggers: {
        altClick: true,
        rightClickMenu: true,
        hoverDelay: false,
        longPress: false,
        dragLink: false
    },
    shortcuts: {
        modifier: 'alt',
        closeKey: 'Escape',
        nextWindow: 'Tab',
        prevWindow: 'Shift+Tab'
    },
    window: {
        defaultSize: 'medium',
        defaultPosition: 'center',
        maxWindows: 5,
        resizable: true,
        draggable: true,
        sizes: {
            small: { width: 400, height: 300 },
            medium: { width: 800, height: 600 },
            large: { width: 1200, height: 800 }
        }
    },
    readingMode: {
        enabled: true,
        autoDetect: true,
        removeAds: true,
        cleanLayout: true
    },
    textActions: {
        search: {
            enabled: true,
            engines: ['bing', 'google', 'baidu'],
            defaultEngine: 'bing'
        },
        translation: {
            enabled: true,
            services: ['google', 'bing', 'baidu'],
            defaultService: 'google',
            targetLanguage: 'zh-CN'
        }
    },
    styling: {
        theme: 'auto',
        borderRadius: 8,
        shadow: true,
        backdrop: true,
        animation: true
    }
};
```

## 三阶段实现计划

### 第一阶段：核心预览功能（优先级：高）

#### 目标
实现基础的链接预览功能，支持 Alt+点击触发。

#### 实现内容
1. **LinkPreviewManager 主控制器**
   - 初始化配置系统
   - 绑定全局事件监听
   - 管理预览窗口生命周期

2. **PreviewWindow 基础组件**
   - 创建预览窗口 DOM 结构
   - iframe 内容加载
   - 基础样式和动画
   - 窗口关闭功能

3. **基础事件处理**
   - Alt+点击链接检测
   - 键盘状态管理
   - ESC 键关闭功能

4. **窗口基础管理**
   - 单窗口创建和销毁
   - 基础拖拽功能
   - 基础调整大小功能

#### 关键文件
- `modules/link-preview/link-preview-manager.js`
- `modules/link-preview/preview-window.js`
- `modules/link-preview/event-manager.js`
- `modules/link-preview/link-preview.css`

#### 集成点
- 在 `SettingsManager` 中添加 `generateAdvancedSection()`
- 在主 `script.js` 中初始化 `LinkPreviewManager`
- 在导航中添加"高级功能"选项

#### 验收标准
- [ ] Alt+点击任意链接能弹出预览窗口
- [ ] 预览窗口能正确加载目标页面
- [ ] ESC 键能关闭预览窗口
- [ ] 窗口支持基础拖拽和调整大小
- [ ] 设置页面能控制功能开关

### 第二阶段：增强交互功能（优先级：中）

#### 目标
扩展触发方式，实现多窗口管理和窗口记忆功能。

#### 实现内容
1. **多种触发方式**
   - 右键菜单选项
   - 悬停延迟触发（可配置延迟时间）
   - 长按链接触发
   - 拖拽链接到指定区域

2. **多窗口管理系统**
   - 同时预览多个链接
   - 窗口层级管理（z-index）
   - 窗口位置自动排列
   - 最大窗口数量限制

3. **窗口记忆功能**
   - 记住用户偏好的窗口大小
   - 记住窗口位置偏好
   - 智能位置建议（避免重叠）

4. **增强的快捷键系统**
   - Tab 键切换窗口焦点
   - 数字键快速切换窗口
   - Ctrl+W 关闭当前窗口

#### 关键文件
- `modules/link-preview/window-manager.js`
- `modules/link-preview/trigger-handlers.js`
- `modules/link-preview/window-memory.js`

#### 验收标准
- [ ] 支持右键菜单预览链接
- [ ] 支持悬停延迟预览（可配置）
- [ ] 能同时打开多个预览窗口
- [ ] 窗口位置智能排列，避免重叠
- [ ] 记住用户的窗口大小和位置偏好
- [ ] 快捷键能正确切换和关闭窗口

### 第三阶段：高级功能实现（优先级：中低）

#### 目标
实现阅读模式、文本操作和完整的主题集成。

#### 实现内容
1. **阅读模式系统**
   - 自动检测文章内容
   - 移除广告和导航元素
   - 提供清洁的阅读界面
   - 字体大小和样式调整

2. **文本拖拽搜索**
   - 检测文本拖拽操作
   - 集成多个搜索引擎
   - 在预览窗口中显示搜索结果
   - 搜索历史记录

3. **文本拖拽翻译**
   - 集成翻译 API
   - 支持多种翻译服务
   - 语言自动检测
   - 翻译结果缓存

4. **完整主题系统**
   - 与现有主题系统集成
   - 深色/浅色模式适配
   - 自定义样式选项
   - 动画效果配置

#### 关键文件
- `modules/link-preview/reading-mode.js`
- `modules/link-preview/text-action-manager.js`
- `modules/link-preview/content-extractor.js`
- `modules/link-preview/translation-service.js`
- `modules/link-preview/theme-adapter.js`

#### 验收标准
- [ ] 阅读模式能正确提取文章内容
- [ ] 拖拽文本能触发搜索功能
- [ ] 拖拽文本能触发翻译功能
- [ ] 主题样式与现有系统一致
- [ ] 所有动画效果流畅自然

## 文件结构规划

```
moment-search-v3/
├── script.js (现有主文件)
├── docs/
│   └── link-preview-implementation-guide.md (本文档)
├── modules/
│   └── link-preview/
│       ├── link-preview-manager.js      (主控制器)
│       ├── preview-window.js            (预览窗口组件)
│       ├── event-manager.js             (事件管理器)
│       ├── window-manager.js            (窗口管理器)
│       ├── text-action-manager.js       (文本操作管理器)
│       ├── reading-mode.js              (阅读模式)
│       ├── content-extractor.js         (内容提取器)
│       ├── cross-origin-handler.js      (跨域处理)
│       ├── translation-service.js       (翻译服务)
│       ├── theme-adapter.js             (主题适配器)
│       └── link-preview.css             (样式文件)
└── settings/
    └── advanced-settings.js             (高级设置扩展)
```

## 集成到现有项目

### 1. 修改 SettingsManager

在 `generateNavigationHTML()` 中添加：
```javascript
{ section: 'advanced', label: '高级功能' }
```

在 `generateMainContentHTML()` 中添加：
```javascript
${this.generateAdvancedSection()}
```

### 2. 修改主 script.js

在应用初始化部分添加：
```javascript
// 初始化链接预览功能
if (window.LinkPreviewManager) {
    window.app.linkPreviewManager = new LinkPreviewManager({
        hostEnvironment: 'moment-search'
    });
}
```

### 3. 样式集成

确保链接预览的样式与现有主题系统兼容，支持深色/浅色模式切换。

## 独立性设计考虑

### 配置抽象层
```javascript
class ConfigAdapter {
    static loadConfig(environment) {
        if (environment === 'moment-search') {
            return Storage.get('settings_v3', {}).advanced?.linkPreview || {};
        } else {
            return chrome.storage.sync.get('linkPreviewConfig');
        }
    }
}
```

### 事件系统解耦
使用标准 DOM 事件，减少对现有事件系统的依赖：
```javascript
// 发布标准事件
document.dispatchEvent(new CustomEvent('linkPreviewCreated', {
    detail: { windowId, url, options }
}));
```

### API 标准化
定义清晰的模块接口，便于后期移植：
```javascript
class LinkPreviewAPI {
    static createPreview(url, options) { }
    static destroyPreview(id) { }
    static configureSettings(config) { }
}
```

## 开发注意事项

1. **性能优化**：使用防抖和节流避免频繁创建预览窗口
2. **内存管理**：及时清理不用的预览窗口和事件监听器
3. **跨域处理**：考虑 CORS 限制，准备代理方案
4. **用户体验**：确保动画流畅，加载状态清晰
5. **兼容性**：测试不同浏览器的兼容性
6. **安全性**：防止 XSS 攻击，验证预览内容

## 测试策略

### 单元测试
- 各个类的核心方法
- 配置系统的正确性
- 事件处理的准确性

### 集成测试
- 与现有项目的集成
- 设置页面的功能
- 跨浏览器兼容性

### 用户测试
- 实际使用场景测试
- 性能压力测试
- 用户体验评估

---

**开发开始时间**：立即
**预计完成时间**：
- 第一阶段：1-2 周
- 第二阶段：2-3 周  
- 第三阶段：2-3 周

**负责人**：开发团队
**审核人**：项目负责人

此文档将随着开发进度持续更新和完善。
