// Moment Search V3 - 主脚本文件
(function() {
    'use strict';

    // 搜索平台配置 - 完全保留V2的配置
    const PLATFORMS = [
        { id: 'all', name: '全平台搜索', icon: '🌐', url: '', aliases: ['all', '全部'] },
        { id: 'google', name: 'Google', icon: '🔍', url: 'https://www.google.com/search?q=', aliases: ['google', 'g', 'gg'] },
        { id: 'baidu', name: '百度', icon: '🔍', url: 'https://www.baidu.com/s?wd=', aliases: ['baidu', 'bd', '百度'] },
        { id: 'zhihu', name: '知乎', icon: '🧠', url: 'https://www.zhihu.com/search?type=content&q=', aliases: ['zhihu', 'zh', '知乎'] },
        { id: 'bilibili', name: 'B站', icon: '📺', url: 'https://search.bilibili.com/all?keyword=', aliases: ['bilibili', 'bili', 'b站', 'b'] },
        { id: 'xiaoh<PERSON><PERSON>', name: '小红书', icon: '📖', url: 'https://www.xiaohongshu.com/search_result?keyword=', aliases: ['xiaohongshu', 'xhs', '小红书'] },
        { id: 'taobao', name: '淘宝', icon: '🛒', url: 'https://s.taobao.com/search?q=', aliases: ['taobao', 'tb', '淘宝'] },
        { id: 'github', name: 'GitHub', icon: '💻', url: 'https://github.com/search?q=', aliases: ['github', 'gh', 'git'] },
        { id: 'youtube', name: 'YouTube', icon: '▶️', url: 'https://www.youtube.com/results?search_query=', aliases: ['youtube', 'yt', 'you'] }
    ];

    // 创建别名映射表
    const ALIAS_MAP = new Map();
    PLATFORMS.forEach(platform => {
        platform.aliases.forEach(alias => {
            ALIAS_MAP.set(alias.toLowerCase(), platform);
        });
    });



    // 默认快捷方式配置 - 扩展到20个常用网站
    const DEFAULT_SHORTCUTS = [
        // 社交媒体
        { id: 1, name: '知乎', url: 'https://zhihu.com', icon: '🧠', order: 1, category: 'social' },
        { id: 2, name: '微博', url: 'https://weibo.com', icon: '📱', order: 2, category: 'social' },
        { id: 3, name: '小红书', url: 'https://xiaohongshu.com', icon: '📝', order: 3, category: 'social' },

        // 娱乐视频
        { id: 4, name: 'B站', url: 'https://bilibili.com', icon: '📺', order: 4, category: 'entertainment' },
        { id: 5, name: 'YouTube', url: 'https://youtube.com', icon: '▶️', order: 5, category: 'entertainment' },
        { id: 6, name: '爱奇艺', url: 'https://iqiyi.com', icon: '🎬', order: 6, category: 'entertainment' },

        // 购物电商
        { id: 7, name: '淘宝', url: 'https://taobao.com', icon: '🛒', order: 7, category: 'shopping' },
        { id: 8, name: '京东', url: 'https://jd.com', icon: '🛍️', order: 8, category: 'shopping' },
        { id: 9, name: '拼多多', url: 'https://pdd.com', icon: '🎁', order: 9, category: 'shopping' },

        // 开发工具
        { id: 10, name: 'GitHub', url: 'https://github.com', icon: '💻', order: 10, category: 'development' },
        { id: 11, name: 'Stack Overflow', url: 'https://stackoverflow.com', icon: '🔧', order: 11, category: 'development' },
        { id: 12, name: 'MDN', url: 'https://developer.mozilla.org', icon: '📖', order: 12, category: 'development' },

        // 学习教育
        { id: 13, name: '豆瓣', url: 'https://douban.com', icon: '📚', order: 13, category: 'education' },
        { id: 14, name: 'CSDN', url: 'https://csdn.net', icon: '💡', order: 14, category: 'education' },
        { id: 15, name: '掘金', url: 'https://juejin.cn', icon: '⛏️', order: 15, category: 'development' },

        // 工具服务
        { id: 16, name: '百度网盘', url: 'https://pan.baidu.com', icon: '☁️', order: 16, category: 'cloud' },
        { id: 17, name: '网易云音乐', url: 'https://music.163.com', icon: '🎵', order: 17, category: 'entertainment' },
        { id: 18, name: 'QQ邮箱', url: 'https://mail.qq.com', icon: '📧', order: 18, category: 'productivity' },

        // 新闻资讯
        { id: 19, name: '今日头条', url: 'https://toutiao.com', icon: '📰', order: 19, category: 'news' },
        { id: 20, name: '36氪', url: 'https://36kr.com', icon: '🚀', order: 20, category: 'news' },

        // AI工具
        { id: 21, name: 'ChatGPT', url: 'https://chat.openai.com', icon: '🤖', order: 21, category: 'ai' },
        { id: 22, name: 'Claude', url: 'https://claude.ai', icon: '🧠', order: 22, category: 'ai' },
        { id: 23, name: 'Midjourney', url: 'https://midjourney.com', icon: '🎨', order: 23, category: 'ai' },

        // 设计工具
        { id: 24, name: 'Figma', url: 'https://figma.com', icon: '🎨', order: 24, category: 'design' },
        { id: 25, name: 'Canva', url: 'https://canva.com', icon: '🖌️', order: 25, category: 'design' },
        { id: 26, name: 'Adobe Creative', url: 'https://adobe.com', icon: '🎭', order: 26, category: 'design' },

        // 效率工具
        { id: 27, name: 'Notion', url: 'https://notion.so', icon: '📝', order: 27, category: 'productivity' },
        { id: 28, name: 'Trello', url: 'https://trello.com', icon: '📋', order: 28, category: 'productivity' },
        { id: 29, name: 'Slack', url: 'https://slack.com', icon: '💬', order: 29, category: 'productivity' },
        { id: 30, name: 'Zoom', url: 'https://zoom.us', icon: '📹', order: 30, category: 'productivity' },

        // 社交平台
        { id: 31, name: 'Discord', url: 'https://discord.com', icon: '🎮', order: 31, category: 'social' },
        { id: 32, name: 'Telegram', url: 'https://web.telegram.org', icon: '✈️', order: 32, category: 'social' },
        { id: 33, name: 'Instagram', url: 'https://instagram.com', icon: '📷', order: 33, category: 'social' },
        { id: 34, name: 'Twitter', url: 'https://twitter.com', icon: '🐦', order: 34, category: 'social' },
        { id: 35, name: 'LinkedIn', url: 'https://linkedin.com', icon: '💼', order: 35, category: 'professional' },

        // 云存储
        { id: 36, name: 'Google Drive', url: 'https://drive.google.com', icon: '☁️', order: 36, category: 'cloud' },
        { id: 37, name: 'Dropbox', url: 'https://dropbox.com', icon: '📦', order: 37, category: 'cloud' },
        { id: 38, name: 'OneDrive', url: 'https://onedrive.live.com', icon: '🌐', order: 38, category: 'cloud' },

        // 娱乐平台
        { id: 39, name: 'Netflix', url: 'https://netflix.com', icon: '🎬', order: 39, category: 'entertainment' },
        { id: 40, name: 'Spotify', url: 'https://spotify.com', icon: '🎵', order: 40, category: 'entertainment' },
        { id: 41, name: 'YouTube Music', url: 'https://music.youtube.com', icon: '🎶', order: 41, category: 'entertainment' },
        { id: 42, name: 'Twitch', url: 'https://twitch.tv', icon: '🎮', order: 42, category: 'entertainment' },

        // 学习平台
        { id: 43, name: 'Coursera', url: 'https://coursera.org', icon: '🎓', order: 43, category: 'education' },
        { id: 44, name: 'edX', url: 'https://edx.org', icon: '📚', order: 44, category: 'education' },
        { id: 45, name: 'Khan Academy', url: 'https://khanacademy.org', icon: '🧮', order: 45, category: 'education' },
        { id: 46, name: 'Duolingo', url: 'https://duolingo.com', icon: '🦜', order: 46, category: 'education' },

        // 开发平台
        { id: 47, name: 'CodePen', url: 'https://codepen.io', icon: '✏️', order: 47, category: 'development' },
        { id: 48, name: 'JSFiddle', url: 'https://jsfiddle.net', icon: '🔧', order: 48, category: 'development' },
        { id: 49, name: 'Replit', url: 'https://replit.com', icon: '⚡', order: 49, category: 'development' },
        { id: 50, name: 'Vercel', url: 'https://vercel.com', icon: '🚀', order: 50, category: 'development' }
    ];

    // 快捷方式分类定义
    const SHORTCUT_CATEGORIES = {
        'social': { name: '社交媒体', icon: '👥', color: '#1976d2' },
        'entertainment': { name: '娱乐', icon: '🎮', color: '#7b1fa2' },
        'productivity': { name: '效率工具', icon: '🛠️', color: '#388e3c' },
        'development': { name: '开发工具', icon: '💻', color: '#f57c00' },
        'design': { name: '设计工具', icon: '🎨', color: '#e91e63' },
        'shopping': { name: '购物', icon: '🛒', color: '#ff5722' },
        'education': { name: '学习', icon: '🎓', color: '#3f51b5' },
        'news': { name: '新闻', icon: '📰', color: '#795548' },
        'finance': { name: '金融', icon: '💰', color: '#4caf50' },
        'ai': { name: 'AI工具', icon: '🤖', color: '#9c27b0' },
        'professional': { name: '职场', icon: '💼', color: '#607d8b' },
        'cloud': { name: '云存储', icon: '☁️', color: '#2196f3' }
    };

    // 全局事件管理器 - 避免重复的 document 事件监听器
    const GlobalEventManager = {
        clickHandlers: new Set(),

        init() {
            document.addEventListener('click', (e) => {
                this.clickHandlers.forEach(handler => {
                    try {
                        handler(e);
                    } catch (error) {
                        console.error('全局点击事件处理器错误:', error);
                    }
                });
            });
        },

        addClickHandler(handler) {
            this.clickHandlers.add(handler);
        },

        removeClickHandler(handler) {
            this.clickHandlers.delete(handler);
        }
    };

    // DOM管理器 - 统一管理DOM元素缓存
    class DOMManager {
        constructor() {
            this.cache = new Map();
        }

        // 获取DOM元素（带缓存）
        get(id) {
            if (!this.cache.has(id)) {
                const element = document.getElementById(id);
                if (element) {
                    this.cache.set(id, element);
                }
            }
            return this.cache.get(id) || null;
        }

        // 清除缓存
        clearCache() {
            this.cache.clear();
        }

        // 批量获取元素
        getMultiple(ids) {
            const result = {};
            ids.forEach(id => {
                result[id] = this.get(id);
            });
            return result;
        }
    }

    // 工具函数
    const Utils = {
        // 防抖函数
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // 生成唯一ID
        generateId() {
            return Date.now() + Math.random().toString(36).substr(2, 9);
        },

        // 验证URL
        isValidUrl(string) {
            try {
                new URL(string.startsWith('http') ? string : 'https://' + string);
                return true;
            } catch (_) {
                return false;
            }
        },

        // 时间格式化功能已移至 TimeManager 类中，避免重复定义
    };

    // 图标获取管理器
    class FaviconManager {
        constructor() {
            this.cache = new Map(); // 图标缓存
            this.fallbackIcon = '🌐'; // 默认图标
            this.googleFaviconAPI = 'https://www.google.com/s2/favicons';
            this.faviconImAPI = 'https://favicon.im'; // 高质量favicon服务
        }

        /**
         * 获取网站图标
         * @param {string} url - 网站URL
         * @param {number} size - 图标大小，默认32px
         * @returns {Promise<string>} 图标URL
         */
        async getFavicon(url, size = 32) {
            try {
                // 标准化URL
                const normalizedUrl = this.normalizeUrl(url);
                const cacheKey = `${normalizedUrl}_${size}`;

                // 检查缓存
                if (this.cache.has(cacheKey)) {
                    return this.cache.get(cacheKey);
                }

                // 优先使用高质量favicon.im服务
                const faviconImUrl = await this.getFaviconImIcon(normalizedUrl, size);
                if (faviconImUrl) {
                    this.cache.set(cacheKey, faviconImUrl);
                    return faviconImUrl;
                }

                // 备用1：使用Chrome Extension Favicon API
                const chromeIconUrl = await this.getChromeFavicon(normalizedUrl, size);
                if (chromeIconUrl) {
                    this.cache.set(cacheKey, chromeIconUrl);
                    return chromeIconUrl;
                }

                // 备用2：使用Google Favicon API
                const googleIconUrl = this.getGoogleFavicon(normalizedUrl, size);
                this.cache.set(cacheKey, googleIconUrl);
                return googleIconUrl;

            } catch (error) {
                console.warn('获取图标失败:', error);
                return this.getDefaultIcon();
            }
        }

        /**
         * 使用favicon.im高质量服务获取图标
         * @param {string} url - 网站URL
         * @param {number} size - 图标大小
         * @returns {Promise<string|null>} 图标URL或null
         */
        async getFaviconImIcon(url, size) {
            try {
                const domain = this.extractDomain(url);
                if (!domain) return null;

                // 根据尺寸选择合适的参数
                const larger = size > 32 ? '?larger=true' : '';
                const faviconUrl = `${this.faviconImAPI}/${domain}${larger}`;

                // 测试图标是否可用
                const isValid = await this.testImageUrl(faviconUrl);
                return isValid ? faviconUrl : null;

            } catch (error) {
                console.warn('Favicon.im服务获取失败:', error);
                return null;
            }
        }

        /**
         * 使用Chrome Extension Favicon API获取图标
         * @param {string} url - 网站URL
         * @param {number} size - 图标大小
         * @returns {Promise<string|null>} 图标URL或null
         */
        async getChromeFavicon(url, size) {
            try {
                if (!chrome?.runtime?.getURL) {
                    return null;
                }

                const faviconUrl = new URL(chrome.runtime.getURL("/_favicon/"));
                faviconUrl.searchParams.set("pageUrl", url);
                faviconUrl.searchParams.set("size", size.toString());

                // 测试图标是否可用
                const isValid = await this.testImageUrl(faviconUrl.toString());
                return isValid ? faviconUrl.toString() : null;

            } catch (error) {
                console.warn('Chrome Favicon API 调用失败:', error);
                return null;
            }
        }

        /**
         * 使用Google Favicon API获取图标
         * @param {string} url - 网站URL
         * @param {number} size - 图标大小
         * @returns {string} 图标URL
         */
        getGoogleFavicon(url, size) {
            try {
                const domain = this.extractDomain(url);
                return `${this.googleFaviconAPI}?domain=${encodeURIComponent(domain)}&sz=${size}`;
            } catch (error) {
                console.warn('Google Favicon API 调用失败:', error);
                return this.getDefaultIcon();
            }
        }

        /**
         * 测试图片URL是否有效
         * @param {string} imageUrl - 图片URL
         * @returns {Promise<boolean>} 是否有效
         */
        testImageUrl(imageUrl) {
            return new Promise((resolve) => {
                const img = new Image();
                const timeout = setTimeout(() => {
                    resolve(false);
                }, 3000); // 3秒超时

                img.onload = () => {
                    clearTimeout(timeout);
                    resolve(true);
                };

                img.onerror = () => {
                    clearTimeout(timeout);
                    resolve(false);
                };

                img.src = imageUrl;
            });
        }

        /**
         * 标准化URL
         * @param {string} url - 原始URL
         * @returns {string} 标准化后的URL
         */
        normalizeUrl(url) {
            if (!url) return '';

            // 如果不是完整URL，添加https://
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }

            return url;
        }

        /**
         * 提取域名
         * @param {string} url - URL
         * @returns {string} 域名
         */
        extractDomain(url) {
            try {
                const urlObj = new URL(this.normalizeUrl(url));
                return urlObj.hostname;
            } catch (error) {
                console.warn('提取域名失败:', error);
                return url;
            }
        }

        /**
         * 获取默认图标
         * @returns {string} 默认图标
         */
        getDefaultIcon() {
            return this.fallbackIcon;
        }

        /**
         * 清除缓存
         */
        clearCache() {
            this.cache.clear();
        }

        /**
         * 预加载常用网站图标
         * @param {Array} urls - URL列表
         */
        async preloadIcons(urls) {
            const promises = urls.map(url => this.getFavicon(url));
            try {
                await Promise.allSettled(promises);
                console.log('图标预加载完成');
            } catch (error) {
                console.warn('图标预加载失败:', error);
            }
        }
    }

    // 存储工具
    const Storage = {
        get(key, defaultValue = null) {
            try {
                const value = localStorage.getItem(key);
                return value ? JSON.parse(value) : defaultValue;
            } catch (error) {
                console.error('Storage get error:', error);
                return defaultValue;
            }
        },

        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Storage set error:', error);
                return false;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('Storage remove error:', error);
                return false;
            }
        }
    };

    // URLParser - 智能URL解析引擎
    class URLParser {
        constructor() {
            // 通用搜索参数 - 覆盖90%的搜索网站
            this.universalParams = [
                'q', 'query', 'search', 'keyword', 'wd', 'search_query',
                's', 'text', 'term', 'searchterm', 'find', 'lookup',
                'k', 'key', 'words', 'string', 'input', 'kw'
            ];

            // 域名特定参数 - 针对主流网站优化
            this.domainParams = {
                'google.com': ['q'],
                'baidu.com': ['wd'],
                'zhihu.com': ['q'],
                'bilibili.com': ['keyword'],
                'taobao.com': ['q'],
                'github.com': ['q'],
                'youtube.com': ['search_query'],
                'stackoverflow.com': ['q'],
                'reddit.com': ['q'],
                'amazon.com': ['k'],
                'jd.com': ['keyword'],
                'tmall.com': ['q'],
                'xiaohongshu.com': ['keyword'],
                'douban.com': ['q'],
                'weibo.com': ['q']
            };

            // 网站名称映射 - 统一管理
            this.siteNameMap = {
                'google.com': 'Google',
                'baidu.com': '百度',
                'zhihu.com': '知乎',
                'bilibili.com': 'B站',
                'taobao.com': '淘宝',
                'github.com': 'GitHub',
                'youtube.com': 'YouTube',
                'stackoverflow.com': 'Stack Overflow',
                'reddit.com': 'Reddit',
                'amazon.com': 'Amazon',
                'jd.com': '京东',
                'tmall.com': '天猫',
                'xiaohongshu.com': '小红书',
                'douban.com': '豆瓣',
                'weibo.com': '微博',
                'twitter.com': 'Twitter'
            };
        }

        // 核心解析方法 - 自动提取搜索关键词
        async parseURL(url, testKeyword = null) {
            try {
                // 1. URL验证和标准化
                const urlObj = this.validateAndParseURL(url);
                if (!urlObj.success) return urlObj;

                const { parsedURL, domain } = urlObj;

                // 2. 自动参数解析 (优先级最高)
                const paramResult = this.autoParseFromParams(parsedURL, domain);
                if (paramResult.success) {
                    return this.buildResult(paramResult, domain, parsedURL);
                }

                // 3. 如果提供了测试关键词，使用传统方法
                if (testKeyword) {
                    const legacyParamResult = this.parseFromParams(parsedURL, testKeyword, domain);
                    if (legacyParamResult.success) {
                        return this.buildResult(legacyParamResult, domain, parsedURL);
                    }

                    const pathResult = this.parseFromPath(parsedURL, testKeyword);
                    if (pathResult.success) {
                        return this.buildResult(pathResult, domain, parsedURL);
                    }
                }

                // 4. 解析失败
                return {
                    success: false,
                    error: '无法识别搜索参数',
                    suggestion: '请确保URL是有效的搜索结果页面，例如：https://github.com/search?q=javascript'
                };
            } catch (error) {
                return {
                    success: false,
                    error: 'URL解析异常',
                    details: error.message
                };
            }
        }

        // URL验证和标准化
        validateAndParseURL(url) {
            try {
                // 自动添加协议
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    url = 'https://' + url;
                }

                const parsedURL = new URL(url);
                const domain = this.extractDomainFromURL(parsedURL.hostname);

                // 基础验证
                if (!domain || domain.length < 3) {
                    return {
                        success: false,
                        error: '域名格式无效',
                        suggestion: '请输入完整的网站域名'
                    };
                }

                return { success: true, parsedURL, domain };
            } catch (error) {
                return {
                    success: false,
                    error: 'URL格式无效',
                    suggestion: '请输入完整的URL地址，例如：https://example.com/search?q=关键词'
                };
            }
        }

        // 自动从URL参数解析 - 不需要测试关键词
        autoParseFromParams(urlObj, domain) {
            const params = new URLSearchParams(urlObj.search);
            const priorityParams = this.getParamPriority(domain);

            for (const param of priorityParams) {
                const value = params.get(param);
                if (value && this.isValidSearchValue(value)) {
                    const template = this.generateParamTemplate(urlObj, param, params);
                    return {
                        success: true,
                        template,
                        searchParam: param,
                        method: 'parameter',
                        confidence: this.calculateConfidence(param, domain),
                        extractedKeyword: this.decodeSearchValue(value)
                    };
                }
            }

            return { success: false };
        }

        // 从URL参数解析 (传统方法，需要测试关键词)
        parseFromParams(urlObj, testKeyword, domain) {
            const params = new URLSearchParams(urlObj.search);
            const priorityParams = this.getParamPriority(domain);

            for (const param of priorityParams) {
                const value = params.get(param);
                if (value && this.containsKeyword(value, testKeyword)) {
                    const template = this.generateParamTemplate(urlObj, param, params);
                    return {
                        success: true,
                        template,
                        searchParam: param,
                        method: 'parameter',
                        confidence: this.calculateConfidence(param, domain)
                    };
                }
            }

            return { success: false };
        }

        // 从URL路径解析
        parseFromPath(urlObj, testKeyword) {
            const path = urlObj.pathname;

            if (this.containsKeyword(path, testKeyword)) {
                const template = urlObj.href.replace(
                    new RegExp(this.escapeRegex(testKeyword), 'gi'),
                    '{query}'
                );

                return {
                    success: true,
                    template,
                    method: 'path',
                    confidence: 0.7 // 路径解析置信度较低
                };
            }

            return { success: false };
        }

        // 生成搜索模板
        generateParamTemplate(urlObj, targetParam, params) {
            const newParams = new URLSearchParams(params);
            newParams.set(targetParam, '{query}');
            const queryString = newParams.toString().replace('%7Bquery%7D', '{query}');
            return `${urlObj.origin}${urlObj.pathname}?${queryString}`;
        }

        // 检查关键词匹配
        containsKeyword(value, keyword) {
            if (!value || !keyword) return false;

            try {
                const decodedValue = decodeURIComponent(value);
                return decodedValue.toLowerCase().includes(keyword.toLowerCase());
            } catch (error) {
                return value.toLowerCase().includes(keyword.toLowerCase());
            }
        }

        // 获取参数优先级
        getParamPriority(domain) {
            const domainSpecific = this.domainParams[domain] || [];
            return [...domainSpecific, ...this.universalParams];
        }

        // 计算解析置信度
        calculateConfidence(param, domain) {
            const domainSpecific = this.domainParams[domain] || [];
            if (domainSpecific.includes(param)) return 0.95;

            const commonParams = ['q', 'query', 'search', 'keyword'];
            if (commonParams.includes(param)) return 0.9;

            return 0.8;
        }

        // 构建最终结果
        buildResult(parseResult, domain, urlObj) {
            return {
                success: true,
                template: parseResult.template,
                name: this.extractSiteName(domain),
                domain: domain,
                icon: this.getIconUrl(domain),
                method: parseResult.method,
                searchParam: parseResult.searchParam,
                confidence: parseResult.confidence,
                originalUrl: urlObj.href
            };
        }

        // 提取网站名称
        extractSiteName(domain) {
            return this.siteNameMap[domain] || this.capitalizeFirst(domain.split('.')[0]);
        }

        // 从hostname提取域名（统一方法）
        extractDomainFromURL(hostname) {
            return hostname.replace(/^www\./, '');
        }

        // 验证是否为有效的搜索值
        isValidSearchValue(value) {
            if (!value || value.length < 1) return false;

            // 解码URL编码的值
            const decodedValue = this.decodeSearchValue(value);

            // 检查是否包含有意义的内容
            // 排除纯数字、单个字符、特殊符号等
            if (decodedValue.length < 2) return false;
            if (/^\d+$/.test(decodedValue)) return false; // 纯数字
            if (/^[^\w\u4e00-\u9fa5]+$/.test(decodedValue)) return false; // 只有特殊符号

            return true;
        }

        // 解码搜索值
        decodeSearchValue(value) {
            try {
                return decodeURIComponent(value);
            } catch (error) {
                return value;
            }
        }

        // 首字母大写
        capitalizeFirst(str) {
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // 转义正则表达式特殊字符
        escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
    }

    // PlatformManager - 平台管理器
    class PlatformManager {
        constructor() {
            this.platforms = new Map();
            this.urlParser = new URLParser();

            // 常量定义
            this.MAX_CUSTOM_PLATFORMS = 30;
            this.MAX_ALIAS_COUNT = 10;
            this.MAX_ALIAS_LENGTH = 20;
            this.MAX_PLATFORM_NAME_LENGTH = 50;

            this.init();
        }

        async init() {
            this.loadBuiltinPlatforms();
            await this.loadCustomPlatforms();
        }

        // 加载内置平台
        loadBuiltinPlatforms() {
            PLATFORMS.forEach(platform => {
                if (platform.id !== 'all') { // 排除全平台搜索选项
                    const platformData = {
                        ...platform,
                        isCustom: false,
                        isBuiltin: true,
                        createdAt: Date.now(),
                        updatedAt: Date.now()
                    };
                    this.platforms.set(platform.id, platformData);
                }
            });
        }

        // 加载自定义平台
        async loadCustomPlatforms() {
            try {
                const customPlatforms = Storage.get('customPlatforms_v3') || [];
                customPlatforms.forEach(platform => {
                    this.platforms.set(platform.id, platform);
                });
            } catch (error) {
                console.error('加载自定义平台失败:', error);
            }
        }

        // 从URL添加平台 (主要功能)
        async addPlatformFromURL(url, options = {}) {
            try {
                // 检查数量限制
                this.checkPlatformLimit();

                // 解析URL (自动解析，不需要测试关键词)
                const parseResult = await this.urlParser.parseURL(url);

                if (!parseResult.success) {
                    throw new Error(parseResult.error);
                }

                // 检查重复
                if (this.isDuplicatePlatform(parseResult.domain)) {
                    throw new Error(`域名 ${parseResult.domain} 已存在`);
                }

                // 构建平台数据
                const platformData = {
                    id: window.MomentSearch.Utils.generateId(),
                    name: options.name || parseResult.name,
                    url: parseResult.template,
                    domain: parseResult.domain,
                    icon: parseResult.icon,
                    aliases: this.parseAliases(options.aliases || ''),
                    isCustom: true,
                    isBuiltin: false,
                    method: parseResult.method,
                    searchParam: parseResult.searchParam,
                    confidence: parseResult.confidence,
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                };

                // 验证数据
                const validation = this.validatePlatformData(platformData);
                if (!validation.valid) {
                    throw new Error(validation.error);
                }

                // 添加到平台列表
                this.platforms.set(platformData.id, platformData);

                // 保存到存储
                await this.savePlatforms();

                // 更新别名映射
                this.updateAliasMap();

                return platformData;
            } catch (error) {
                console.error('从URL添加平台失败:', error);
                throw error;
            }
        }

        // 检查平台数量限制
        checkPlatformLimit() {
            const customCount = Array.from(this.platforms.values())
                .filter(p => p.isCustom).length;

            if (customCount >= this.MAX_CUSTOM_PLATFORMS) {
                throw new Error(`最多只能添加${this.MAX_CUSTOM_PLATFORMS}个自定义平台`);
            }
        }

        // 检查重复平台
        isDuplicatePlatform(domain) {
            return Array.from(this.platforms.values())
                .some(p => p.domain === domain);
        }

        // 验证平台数据
        validatePlatformData(data) {
            if (!data.name || data.name.trim().length === 0) {
                return { valid: false, error: '平台名称不能为空' };
            }

            if (data.name.length > this.MAX_PLATFORM_NAME_LENGTH) {
                return { valid: false, error: `平台名称不能超过${this.MAX_PLATFORM_NAME_LENGTH}个字符` };
            }

            if (!data.url || !data.url.includes('{query}')) {
                return { valid: false, error: 'URL必须包含{query}占位符' };
            }

            try {
                // 验证URL格式
                const testUrl = data.url.replace('{query}', 'test');
                new URL(testUrl);
            } catch (error) {
                return { valid: false, error: 'URL格式无效' };
            }

            if (data.aliases && data.aliases.length > this.MAX_ALIAS_COUNT) {
                return { valid: false, error: `别名数量不能超过${this.MAX_ALIAS_COUNT}个` };
            }

            return { valid: true };
        }

        // 解析别名
        parseAliases(aliasString) {
            if (!aliasString) return [];

            return aliasString
                .split(/[,，\s]+/)
                .map(alias => alias ? alias.trim().toLowerCase() : '')
                .filter(alias => alias && alias.length > 0 && alias.length <= this.MAX_ALIAS_LENGTH)
                .slice(0, this.MAX_ALIAS_COUNT);
        }

        // 更新别名映射
        updateAliasMap() {
            // 清空现有的自定义平台映射
            const customAliases = [];
            ALIAS_MAP.forEach((platform, alias) => {
                if (platform.isCustom) {
                    customAliases.push(alias);
                }
            });
            customAliases.forEach(alias => ALIAS_MAP.delete(alias));

            // 重新构建自定义平台映射
            this.platforms.forEach(platform => {
                if (platform.aliases && Array.isArray(platform.aliases)) {
                    platform.aliases.forEach(alias => {
                        ALIAS_MAP.set(alias.toLowerCase(), platform);
                    });
                }
            });
        }

        // 删除平台
        async deletePlatform(id) {
            try {
                const platform = this.platforms.get(id);
                if (!platform) {
                    throw new Error('平台不存在');
                }

                if (platform.isBuiltin) {
                    throw new Error('不能删除内置平台');
                }

                // 从平台列表移除
                this.platforms.delete(id);

                // 保存到存储
                await this.savePlatforms();

                // 更新别名映射
                this.updateAliasMap();

                return true;
            } catch (error) {
                console.error('删除平台失败:', error);
                throw error;
            }
        }

        // 保存自定义平台
        async savePlatforms() {
            try {
                const customPlatforms = Array.from(this.platforms.values())
                    .filter(p => p.isCustom);
                Storage.set('customPlatforms_v3', customPlatforms);
            } catch (error) {
                console.error('保存平台失败:', error);
                throw error;
            }
        }

        // 获取所有平台
        getAllPlatforms(options = {}) {
            let platforms = Array.from(this.platforms.values());

            if (options.customOnly) {
                platforms = platforms.filter(p => p.isCustom);
            }

            if (options.builtinOnly) {
                platforms = platforms.filter(p => p.isBuiltin);
            }

            return platforms.sort((a, b) => {
                if (a.isBuiltin && !b.isBuiltin) return -1;
                if (!a.isBuiltin && b.isBuiltin) return 1;
                return a.name.localeCompare(b.name);
            });
        }

        // 获取平台统计信息
        getStats() {
            const allPlatforms = Array.from(this.platforms.values());
            return {
                total: allPlatforms.length,
                builtin: allPlatforms.filter(p => p.isBuiltin).length,
                custom: allPlatforms.filter(p => p.isCustom).length,
                maxCustom: this.MAX_CUSTOM_PLATFORMS
            };
        }

        // 提取域名（统一方法，避免重复）
        extractDomain(url) {
            try {
                const urlObj = new URL(url.replace('{query}', 'test'));
                return urlObj.hostname.replace(/^www\./, '');
            } catch (error) {
                return null;
            }
        }
    }

    // 时间管理器
    class TimeManager {
        constructor() {
            this.timeElement = document.getElementById('currentTime');
            this.dateElement = document.getElementById('currentDate');
            this.timeDisplay = document.getElementById('timeDisplay');
            this.init();
        }

        init() {
            this.updateTime();
            setInterval(() => this.updateTime(), 1000);
            
            // 应用时间显示设置
            this.applySettings();
        }

        updateTime() {
            const now = new Date();

            // 获取时间设置
            const settings = Storage.get('settings_v3', {});
            const timeSettings = settings.time || {};

            // 更新时间 - 只在内容实际改变时更新DOM
            if (this.timeElement && timeSettings.showTime !== false) {
                const timeStr = this.formatTime(now, timeSettings);
                if (this.timeElement.textContent !== timeStr) {
                    this.timeElement.textContent = timeStr;
                }
            }

            // 更新日期（当有任何日期信息需要显示时）- 只在内容实际改变时更新DOM
            if (this.dateElement && this.hasDateInfo(timeSettings)) {
                const dateStr = this.formatDate(now, timeSettings);
                if (this.dateElement.textContent !== dateStr) {
                    this.dateElement.textContent = dateStr;
                }
            }
        }

        formatTime(date, settings) {
            const format24h = settings.format24h !== false;
            const showSeconds = settings.showSeconds || false;

            let hours = date.getHours();
            const minutes = date.getMinutes();
            const seconds = date.getSeconds();

            if (!format24h) {
                // 12小时制转换，但不显示AM/PM后缀
                hours = hours % 12;
                hours = hours ? hours : 12; // 0点显示为12点
            }

            const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}${showSeconds ? ':' + seconds.toString().padStart(2, '0') : ''}`;
            return timeStr;
        }

        // 检查是否有任何日期信息需要显示
        hasDateInfo(settings) {
            const showDate = settings.showDate !== false;
            const showWeekday = settings.showWeekday !== false;
            return showDate || showWeekday;
        }

        formatDate(date, settings) {
            const showDate = settings.showDate !== false; // 控制月日显示
            const showWeekday = settings.showWeekday !== false; // 控制星期显示

            let dateParts = [];

            // 月日部分（受"月日显示"控制）
            if (showDate) {
                const dateOptions = {
                    month: 'long',
                    day: 'numeric'
                };
                const dateStr = date.toLocaleDateString('zh-CN', dateOptions);
                dateParts.push(dateStr);
            }

            // 星期部分（受"星期显示"控制）
            if (showWeekday) {
                const weekdayOptions = {
                    weekday: 'long'
                };
                const weekdayStr = date.toLocaleDateString('zh-CN', weekdayOptions);
                dateParts.push(weekdayStr);
            }

            return dateParts.join('');
        }

        applySettings() {
            const settings = Storage.get('settings_v3', {});
            const timeSettings = settings.time || {};

            // 修正控制逻辑：
            // showTime - 控制整个时间展示区域（包括时间、日期、星期）
            // showDate - 控制月日显示
            // showWeekday - 控制星期显示
            const showTimeArea = timeSettings.showTime !== false; // 控制整个时间区域
            const showTimeOnly = true; // 时间部分始终显示（当时间区域显示时）
            const showDate = timeSettings.showDate !== false; // 控制月日显示
            const showWeekday = timeSettings.showWeekday !== false; // 控制星期显示
            const fontSizePercent = Math.max(30, timeSettings.fontSize || 50); // 百分比值，最小30%
            const color = timeSettings.color || '#ffffff';
            const fontFamily = timeSettings.fontFamily || 'system-ui';
            const fontWeight = timeSettings.fontWeight || 400;

            // 将百分比转换为实际像素值
            // 基础大小：时间48px，日期16px，50%对应这些基础值
            const baseTimeFontSize = 48;
            const baseDateFontSize = 16;
            const actualTimeFontSize = Math.round((fontSizePercent / 50) * baseTimeFontSize);
            const actualDateFontSize = Math.round((fontSizePercent / 50) * baseDateFontSize);

            // 控制整个时间显示区域
            if (this.timeDisplay) {
                this.timeDisplay.style.display = showTimeArea ? 'block' : 'none';
                this.timeDisplay.style.color = color;
            }

            // 控制时间部分（当时间区域显示时，时间部分始终显示）
            if (this.timeElement) {
                this.timeElement.style.display = (showTimeArea && showTimeOnly) ? 'block' : 'none';
                this.timeElement.style.fontSize = `${actualTimeFontSize}px`;
                this.timeElement.style.fontFamily = this.getFontFamily(fontFamily);
                this.timeElement.style.fontWeight = fontWeight;
            }

            // 控制日期部分（当时间区域显示且有任何日期信息需要显示时显示）
            if (this.dateElement) {
                const hasDateInfo = this.hasDateInfo(timeSettings);
                this.dateElement.style.display = (showTimeArea && hasDateInfo) ? 'block' : 'none';
                this.dateElement.style.fontSize = `${actualDateFontSize}px`;
                this.dateElement.style.fontFamily = this.getFontFamily(fontFamily);
                this.dateElement.style.fontWeight = fontWeight;
            }

            // 重置位置调整缓存，因为字体大小可能已改变
            this.resetPositionCache();

            // 动态调整时间显示区域位置以避免重叠
            this.adjustTimeDisplayPosition();
        }

        resetPositionCache() {
            this._positionAdjusted = false;
            this._cachedTimeHeight = null;
            this._fontSizeChanged = true; // 标记字体大小已改变
        }

        getFontFamily(fontValue) {
            const fontMap = {
                'arial': '"Arial", "Helvetica", sans-serif',
                'monospace': '"Consolas", "Monaco", "Courier New", monospace',
                'yahei': '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif',
                'times': '"Times New Roman", "Times", serif'
            };
            return fontMap[fontValue] || fontMap['arial'];
        }

        adjustTimeDisplayPosition() {
            // 进一步减少位置调整的频率，只在字体大小实际改变时调整
            if (this._positionAdjusted && !this._fontSizeChanged) return;

            // 使用requestAnimationFrame确保在下一帧进行调整，获得更好的性能
            requestAnimationFrame(() => {
                const timeDisplay = document.querySelector('.time-display');
                const searchContainer = document.querySelector('.search-container');

                if (timeDisplay && searchContainer) {
                    // 使用缓存的高度值，避免频繁的DOM查询
                    if (!this._cachedTimeHeight || this._fontSizeChanged) {
                        const timeRect = timeDisplay.getBoundingClientRect();
                        this._cachedTimeHeight = timeRect.height;
                        this._fontSizeChanged = false;
                    }

                    const minGap = 20; // 最小间距20px
                    const defaultTimeTop = 80; // 默认时间显示位置
                    const searchTop = 220; // 搜索容器固定位置
                    const minTimeTop = 20; // 时间区域最小top值，避免超出页面顶部

                    // 使用缓存的高度值
                    const timeHeight = this._cachedTimeHeight;

                    // 计算理想的时间区域top位置：搜索栏位置 - 时间高度 - 最小间距
                    const idealTimeTop = searchTop - timeHeight - minGap;

                    // 确定最终的时间区域位置：默认位置和理想位置的较小值，但不小于最小值
                    const newTimeTop = Math.max(minTimeTop, Math.min(defaultTimeTop, idealTimeTop));

                    // 只有当需要调整时才修改位置，避免不必要的重排
                    const currentTop = parseInt(timeDisplay.style.top) || defaultTimeTop;
                    if (Math.abs(currentTop - newTimeTop) > 2) { // 增加阈值，减少微小调整
                        timeDisplay.style.top = `${newTimeTop}px`;
                        console.log(`时间显示区域位置调整: ${currentTop}px → ${newTimeTop}px`);
                        this._positionAdjusted = true;
                    }
                }
            });
        }
    }

    // 搜索模式管理器
    class SearchModeManager {
        constructor() {
            this.currentMode = 'normal'; // 'normal' | 'quick'
            this.modes = {
                normal: {
                    name: '常规搜索',
                    icon: '🔍',
                    description: '选择平台后搜索',
                    searchMethod: 'platform-select',
                    defaultPlatforms: [
                        'all', 'google', 'baidu', 'bing', 'zhihu', 'weibo',
                        'xiaohongshu', 'bilibili', 'youtube', 'github'
                    ]
                },
                quick: {
                    name: '快捷搜索',
                    icon: '⚡',
                    description: '使用别名直达搜索',
                    searchMethod: 'alias-match',
                    defaultPlatforms: [
                        'google', 'github', 'stackoverflow', 'mdn', 'zhihu',
                        'csdn', 'juejin', 'chatgpt', 'claude', 'figma'
                    ]
                }
            };

            this.init();
        }

        init() {
            this.loadSavedMode();
            this.bindShortcuts();
            this.updateSearchPlaceholder();
        }

        // 加载保存的模式（默认为常规搜索）
        loadSavedMode() {
            this.currentMode = 'normal'; // 始终默认为常规搜索模式
        }

        // 保存当前模式（优化：减少重复的设置读取）
        saveMode() {
            this.updateSetting('search.currentMode', this.currentMode);
        }

        // 统一的设置更新方法
        updateSetting(path, value) {
            const settings = Storage.get('settings_v3', {});
            const keys = path.split('.');
            let current = settings;

            // 创建嵌套对象路径
            for (let i = 0; i < keys.length - 1; i++) {
                if (!current[keys[i]]) current[keys[i]] = {};
                current = current[keys[i]];
            }

            current[keys[keys.length - 1]] = value;
            Storage.set('settings_v3', settings);
        }

        // 更新搜索框占位符
        updateSearchPlaceholder() {
            // 使用统一的DOM管理器
            if (!this.domManager) {
                this.domManager = new DOMManager();
            }

            const searchInput = this.domManager.get('searchInput');
            const platformName = this.domManager.get('platformName');

            if (searchInput && platformName) {
                if (this.currentMode === 'normal') {
                    searchInput.placeholder = `在 ${platformName.textContent} 中搜索...`;
                } else {
                    searchInput.placeholder = '输入别名和关键词，如 "g 搜索内容"...';
                }
            }
        }

        // 绑定平台选择器事件
        bindPlatformSelector() {
            const { platformGrid } = this.domElements;
            if (!platformGrid) return;

            platformGrid.addEventListener('click', (e) => {
                const platformItem = e.target.closest('.platform-item');
                if (!platformItem) return;

                const platformId = platformItem.dataset.platform;

                // 更新选中状态
                platformGrid.querySelectorAll('.platform-item').forEach(item => {
                    item.classList.remove('selected');
                });
                platformItem.classList.add('selected');

                // 通知搜索管理器
                if (window.app?.searchManager) {
                    window.app.searchManager.setSelectedPlatform(platformId);
                }
            });
        }

        // 绑定快捷键
        bindShortcuts() {
            document.addEventListener('keydown', (e) => {
                const settings = Storage.get('settings_v3', {});
                const shortcutKey = settings.shortcuts?.modeSwitch || 'Ctrl+Q';

                if (this.isShortcutPressed(e, shortcutKey)) {
                    e.preventDefault();
                    this.toggleMode();
                }
            });
        }

        // 检查快捷键是否被按下（支持自定义快捷键）
        isShortcutPressed(event, shortcutKey) {
            if (!shortcutKey || !shortcutKey.includes('+')) return false;

            const parts = shortcutKey.split('+');
            const modifiers = parts.slice(0, -1);
            const key = parts[parts.length - 1];

            // 检查修饰键
            const hasCtrl = modifiers.includes('Ctrl') ? (event.ctrlKey || event.metaKey) : !event.ctrlKey && !event.metaKey;
            const hasAlt = modifiers.includes('Alt') ? event.altKey : !event.altKey;
            const hasShift = modifiers.includes('Shift') ? event.shiftKey : !event.shiftKey;

            if (!hasCtrl || !hasAlt || !hasShift) return false;

            // 检查主键
            const normalizedKey = this.normalizeEventKey(event.key);
            return normalizedKey === key.toUpperCase();
        }

        // 标准化事件按键名称
        normalizeEventKey(key) {
            const keyMap = {
                ' ': 'SPACE',
                'ArrowUp': 'UP',
                'ArrowDown': 'DOWN',
                'ArrowLeft': 'LEFT',
                'ArrowRight': 'RIGHT',
                'Escape': 'ESC'
            };

            return keyMap[key] || key.toUpperCase();
        }

        // 更新快捷键（由设置页面调用）
        updateShortcutKey(newShortcut) {
            const settings = Storage.get('settings_v3', {});
            if (!settings.shortcuts) settings.shortcuts = {};
            settings.shortcuts.modeSwitch = newShortcut;
            Storage.set('settings_v3', settings);

            console.log(`快捷键已更新为: ${newShortcut}`);
        }

        // 切换模式
        switchMode(mode) {
            if (this.modes[mode] && this.currentMode !== mode) {
                const oldMode = this.currentMode;
                this.currentMode = mode;
                this.saveMode();
                this.updateSearchPlaceholder();
                this.updateSearchBehavior();
                this.updatePlatformList();
                this.switchToModePages(mode);
                this.showModeChangeToast(mode, oldMode);
            }
        }

        // 切换模式（在当前两种模式间切换）
        toggleMode() {
            const newMode = this.currentMode === 'normal' ? 'quick' : 'normal';
            this.switchMode(newMode);
        }

        // 切换到对应模式的页面
        switchToModePages(mode) {
            if (window.app && window.app.pageManager) {
                const pageManager = window.app.pageManager;
                const modePages = pageManager.getPagesByMode(mode);

                if (modePages.length > 0) {
                    // 切换到该模式的第一个页面
                    pageManager.switchToPage(modePages[0].id);
                }
            }
        }

        // 更新搜索行为
        updateSearchBehavior() {
            // 通知搜索管理器模式已改变
            if (window.app?.searchManager) {
                window.app.searchManager.setSearchMode(this.currentMode);
            }
        }

        // 更新平台列表
        updatePlatformList() {
            // 通知搜索管理器重新初始化平台列表
            if (window.app?.searchManager) {
                window.app.searchManager.initializePlatformList();
            }
        }

        // 模式改变回调
        onModeChange(mode) {
            console.log(`搜索模式切换到: ${this.modes[mode].name}`);

            // 触发自定义事件
            const event = new CustomEvent('searchModeChanged', {
                detail: { mode, modeInfo: this.modes[mode] }
            });
            document.dispatchEvent(event);
        }

        // 显示模式切换提示
        showModeChangeToast(mode, oldMode) {
            const modeInfo = this.modes[mode];
            const oldModeInfo = this.modes[oldMode];

            // 移除现有的提示
            const existingToast = document.querySelector('.mode-change-toast');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的提示
            const toast = document.createElement('div');
            toast.className = 'mode-change-toast enhanced';
            toast.innerHTML = `
                <div class="toast-content">
                    <div class="toast-header">
                        <span class="toast-transition">${oldModeInfo.icon} → ${modeInfo.icon}</span>
                        <span class="toast-title">切换到${modeInfo.name}</span>
                    </div>
                    <div class="toast-description">${modeInfo.description}</div>
                    <div class="toast-platforms">
                        可用平台: ${this.getModePlatformNames(mode).join(', ')}
                    </div>
                </div>
            `;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => toast.classList.add('show'), 10);

            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 4000);
        }

        // 获取模式的平台名称列表
        getModePlatformNames(mode) {
            const modeConfig = this.modes[mode];
            if (!modeConfig || !modeConfig.defaultPlatforms) {
                return ['所有平台'];
            }

            // 通过SearchManager获取平台列表，避免直接使用PLATFORMS常量
            const searchManager = window.app?.searchManager;
            if (!searchManager) {
                return ['所有平台'];
            }

            return modeConfig.defaultPlatforms
                .map(platformId => {
                    const platform = searchManager.platforms.find(p => p.id === platformId);
                    return platform ? platform.name : platformId;
                })
                .slice(0, 5); // 只显示前5个
        }

        // 获取当前模式
        getCurrentMode() {
            return this.currentMode;
        }

        // 获取模式信息
        getModeInfo(mode = this.currentMode) {
            return this.modes[mode];
        }
    }

    // 搜索管理器
    class SearchManager {
        constructor(platforms = PLATFORMS) {
            this.platforms = platforms; // 存储平台列表
            this.aliasMap = ALIAS_MAP;
            this.currentPlatform = 'all';

            // 初始化平台管理器
            this.platformManager = new PlatformManager();

            // 搜索模式相关
            this.searchMode = 'normal'; // 'normal' | 'quick'
            this.selectedPlatform = 'all'; // 常规模式下选中的平台

            // DOM元素缓存
            this.domElements = {
                searchInput: document.getElementById('searchInput'),
                platformSelector: document.getElementById('platformSelector'),
                platformFavicon: document.getElementById('platformFavicon'),
                platformEmoji: document.getElementById('platformEmoji'),
                platformDropdown: document.getElementById('platformDropdown'),
                platformList: document.getElementById('platformList'),
                platformSearchInput: document.getElementById('platformSearchInput')
            };

            // 初始化图标管理器
            this.faviconManager = new FaviconManager();

            // 当前选中的平台
            this.currentSelectedPlatform = null;

            this.init();
        }

        init() {
            this.bindEvents();
            this.updatePlatformIndicator(this.getCurrentPlatform());
        }

        bindEvents() {
            const { searchInput, platformSelector, platformDropdown, platformSearchInput } = this.domElements;
            if (!searchInput || !platformSelector) return;

            // 回车键搜索
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.executeSearch();
                }
            });

            // 平台选择器点击
            platformSelector.addEventListener('click', (e) => {
                e.stopPropagation();
                this.togglePlatformDropdown();
            });

            // 平台搜索输入
            if (platformSearchInput) {
                platformSearchInput.addEventListener('input', (e) => {
                    this.filterPlatforms(e.target.value);
                });
            }

            // 点击其他地方关闭下拉框
            document.addEventListener('click', (e) => {
                if (!platformDropdown.contains(e.target) && !platformSelector.contains(e.target)) {
                    this.hidePlatformDropdown();
                }
            });

            // 输入时智能匹配 - 使用防抖优化
            const debouncedMatch = Utils.debounce((value) => {
                this.handleSmartMatch(value);
            }, 300);

            searchInput.addEventListener('input', (e) => {
                debouncedMatch(e.target.value);
            });

            // 初始化平台列表
            this.initializePlatformList();
        }

        // 初始化平台列表
        async initializePlatformList() {
            const platforms = this.getModePlatforms();
            await this.renderPlatformList(platforms);

            // 设置默认选中的平台
            this.selectPlatform(platforms[0]);
        }

        // 获取当前模式的平台列表
        getModePlatforms() {
            const searchModeManager = window.app?.searchModeManager;
            if (!searchModeManager) {
                return [...this.platforms];
            }

            const currentMode = searchModeManager.currentMode;
            const modeConfig = searchModeManager.modes[currentMode];

            if (!modeConfig || !modeConfig.defaultPlatforms) {
                return [...this.platforms];
            }

            // 根据模式配置过滤平台
            const modePlatforms = modeConfig.defaultPlatforms
                .map(platformId => this.platforms.find(p => p.id === platformId))
                .filter(platform => platform !== undefined);

            // 如果没有找到任何平台，返回默认平台列表
            if (modePlatforms.length === 0) {
                return [...this.platforms];
            }

            // 确保包含全平台搜索选项（如果配置中没有的话，添加到开头）
            if (!modePlatforms.find(p => p.id === 'all')) {
                const allPlatform = this.platforms.find(p => p.id === 'all');
                if (allPlatform) {
                    modePlatforms.unshift(allPlatform);
                }
            }

            return modePlatforms;
        }

        // 渲染平台列表
        async renderPlatformList(platforms) {
            const { platformList } = this.domElements;
            if (!platformList) return;

            const platformItems = await Promise.all(platforms.map(async (platform) => {
                let iconHtml = '';

                if (platform.id !== 'all') {
                    // 尝试获取真实图标
                    try {
                        const faviconUrl = await this.faviconManager.getFavicon(platform.url || `https://${platform.name.toLowerCase()}.com`);
                        if (faviconUrl && faviconUrl !== this.faviconManager.getDefaultIcon()) {
                            iconHtml = `<img src="${faviconUrl}" alt="${platform.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                       <span style="display: none;">${platform.icon}</span>`;
                        } else {
                            iconHtml = `<span>${platform.icon}</span>`;
                        }
                    } catch (error) {
                        iconHtml = `<span>${platform.icon}</span>`;
                    }
                } else {
                    iconHtml = `<span>${platform.icon}</span>`;
                }

                return `
                    <div class="platform-item" data-platform-id="${platform.id}" data-platform-name="${platform.name}">
                        <div class="platform-item-icon">${iconHtml}</div>
                        <div class="platform-item-name">${platform.name}</div>
                    </div>
                `;
            }));

            platformList.innerHTML = platformItems.join('');

            // 绑定点击事件
            platformList.addEventListener('click', (e) => {
                const platformItem = e.target.closest('.platform-item');
                if (platformItem) {
                    const platformId = platformItem.dataset.platformId;
                    const platform = platforms.find(p => p.id === platformId);
                    if (platform) {
                        this.selectPlatform(platform);
                        this.hidePlatformDropdown();
                    }
                }
            });
        }

        // 选择平台
        async selectPlatform(platform) {
            this.currentSelectedPlatform = platform;
            this.selectedPlatform = platform.id;

            // 更新UI显示
            await this.updatePlatformDisplay(platform);

            // 更新搜索框占位符
            const { searchInput } = this.domElements;
            if (searchInput) {
                searchInput.placeholder = `在 ${platform.name} 中搜索...`;
            }
        }

        // 更新平台显示
        async updatePlatformDisplay(platform) {
            const { platformFavicon, platformEmoji } = this.domElements;

            if (platform.id === 'all') {
                // 全平台搜索显示emoji
                if (platformEmoji) {
                    platformEmoji.textContent = platform.icon;
                    platformEmoji.style.display = 'inline';
                }
                if (platformFavicon) {
                    platformFavicon.style.display = 'none';
                }
            } else {
                // 尝试显示真实图标
                try {
                    const faviconUrl = await this.faviconManager.getFavicon(platform.url || `https://${platform.name.toLowerCase()}.com`);
                    if (faviconUrl && faviconUrl !== this.faviconManager.getDefaultIcon()) {
                        if (platformFavicon) {
                            platformFavicon.src = faviconUrl;
                            platformFavicon.alt = platform.name;
                            platformFavicon.style.display = 'inline';
                        }
                        if (platformEmoji) {
                            platformEmoji.style.display = 'none';
                        }
                    } else {
                        // 使用emoji图标
                        if (platformEmoji) {
                            platformEmoji.textContent = platform.icon;
                            platformEmoji.style.display = 'inline';
                        }
                        if (platformFavicon) {
                            platformFavicon.style.display = 'none';
                        }
                    }
                } catch (error) {
                    // 使用emoji图标作为备用
                    if (platformEmoji) {
                        platformEmoji.textContent = platform.icon;
                        platformEmoji.style.display = 'inline';
                    }
                    if (platformFavicon) {
                        platformFavicon.style.display = 'none';
                    }
                }
            }
        }

        // 切换平台下拉框显示
        togglePlatformDropdown() {
            const { platformDropdown } = this.domElements;
            if (!platformDropdown) return;

            const isVisible = platformDropdown.style.display !== 'none';
            if (isVisible) {
                this.hidePlatformDropdown();
            } else {
                this.showPlatformDropdown();
            }
        }

        // 显示平台下拉框
        showPlatformDropdown() {
            const { platformDropdown, platformSearchInput } = this.domElements;
            if (!platformDropdown) return;

            platformDropdown.style.display = 'block';

            // 聚焦搜索框
            if (platformSearchInput) {
                setTimeout(() => platformSearchInput.focus(), 100);
            }
        }

        // 隐藏平台下拉框
        hidePlatformDropdown() {
            const { platformDropdown, platformSearchInput } = this.domElements;
            if (!platformDropdown) return;

            platformDropdown.style.display = 'none';

            // 清空搜索框
            if (platformSearchInput) {
                platformSearchInput.value = '';
                this.filterPlatforms('');
            }
        }

        // 过滤平台
        filterPlatforms(query) {
            const { platformList } = this.domElements;
            if (!platformList) return;

            const items = platformList.querySelectorAll('.platform-item');
            const lowerQuery = query.toLowerCase();

            items.forEach(item => {
                const name = item.dataset.platformName.toLowerCase();
                const isMatch = name.includes(lowerQuery);
                item.style.display = isMatch ? 'flex' : 'none';
            });
        }

        executeSearch() {
            const { searchInput } = this.domElements;
            const query = searchInput.value.trim();
            if (!query) return;

            if (this.searchMode === 'normal') {
                this.executeNormalSearch(query);
            } else {
                this.executeQuickSearch(query);
            }

            // 清空搜索框
            searchInput.value = '';

            // 重置平台指示器
            this.currentPlatform = 'all';
            this.updatePlatformIndicator(this.getCurrentPlatform());
        }

        handleSmartMatch(query) {
            const words = query.toLowerCase().split(' ');
            let matchedPlatform = null;
            
            for (const word of words) {
                if (this.aliasMap.has(word)) {
                    matchedPlatform = this.aliasMap.get(word);
                    this.currentPlatform = matchedPlatform.id;
                    break;
                }
            }
            
            if (matchedPlatform) {
                this.updatePlatformIndicator(matchedPlatform);
            } else {
                // 重置为全平台搜索
                this.currentPlatform = 'all';
                this.updatePlatformIndicator(this.getCurrentPlatform());
            }
        }

        getCurrentPlatform() {
            // 使用PlatformManager统一查找平台
            if (this.platformManager && this.platformManager.platforms) {
                const platform = this.platformManager.platforms.get(this.currentPlatform);
                if (platform) {
                    return platform;
                }
            }

            // 默认返回全平台搜索（从this.platforms获取）
            return this.platforms[0];
        }

        executeSinglePlatformSearch(query, platform) {
            const searchUrl = platform.url.replace('{query}', encodeURIComponent(query));
            window.open(searchUrl, '_blank');
        }

        executeAllPlatformSearch(query) {
            // 获取所有可搜索的平台（内置 + 自定义）
            const builtinPlatforms = this.platforms.filter(p => p.id !== 'all');
            const customPlatforms = this.platformManager ?
                Array.from(this.platformManager.platforms.values()) : [];

            const allSearchPlatforms = [...builtinPlatforms, ...customPlatforms];

            allSearchPlatforms.forEach((platform, index) => {
                setTimeout(() => {
                    this.executeSinglePlatformSearch(query, platform);
                }, index * 100);
            });
        }

        async updatePlatformIndicator(platform) {
            // 使用新的selectPlatform方法
            await this.selectPlatform(platform);
        }

        // 设置搜索模式
        setSearchMode(mode) {
            this.searchMode = mode;
            console.log(`搜索管理器模式设置为: ${mode}`);
        }

        // 设置选中的平台（常规搜索模式使用）
        setSelectedPlatform(platformName, platformUrl) {
            // 根据平台名称或URL查找对应的平台ID
            const platform = this.platforms.find(p =>
                p.name === platformName ||
                p.url === platformUrl ||
                p.searchUrl === platformUrl
            );

            if (platform) {
                this.selectedPlatform = platform.id;
                console.log(`选中平台: ${platformName} (${platform.id})`);
            } else {
                // 如果找不到匹配的平台，创建一个临时平台
                this.selectedPlatform = 'custom';
                this.customPlatform = {
                    id: 'custom',
                    name: platformName,
                    url: platformUrl,
                    searchUrl: platformUrl
                };
                console.log(`创建临时平台: ${platformName}`);
            }
        }

        // 常规搜索
        executeNormalSearch(query) {
            const platform = this.getPlatformById(this.selectedPlatform);

            if (platform.id === 'all') {
                this.executeAllPlatformSearch(query);
            } else {
                this.executeSinglePlatformSearch(query, platform);
            }
        }

        // 快捷搜索 (现有逻辑)
        executeQuickSearch(query) {
            const platform = this.matchPlatform(query);

            if (platform) {
                const actualQuery = query.substring(query.indexOf(' ') + 1);
                this.executeSinglePlatformSearch(actualQuery, platform);
            } else {
                this.executeAllPlatformSearch(query);
            }
        }

        // 匹配平台（统一的平台匹配逻辑）
        matchPlatform(query) {
            const words = query.toLowerCase().split(' ');
            for (const word of words) {
                if (this.aliasMap.has(word)) {
                    return this.aliasMap.get(word);
                }
            }
            return null;
        }

        // 根据ID获取平台（统一的平台获取逻辑）
        getPlatformById(platformId) {
            if (platformId === 'custom' && this.customPlatform) {
                return this.customPlatform;
            }

            // 首先从PlatformManager查找自定义平台
            if (this.platformManager && this.platformManager.platforms) {
                const customPlatform = this.platformManager.platforms.get(platformId);
                if (customPlatform) {
                    return customPlatform;
                }
            }

            // 然后从内置平台查找
            const builtinPlatform = PLATFORMS.find(p => p.id === platformId);
            if (builtinPlatform) {
                return builtinPlatform;
            }

            // 默认返回全平台搜索
            return PLATFORMS[0];
        }


    }

    // 主应用类
    class NewTabApp {
        constructor() {
            this.timeManager = null;
            this.searchManager = null;
            this.searchModeManager = null;
            this.shortcutManager = null;
            this.settingsManager = null;
            this.backgroundManager = null;

            this.init();
        }

        async init() {
            try {
                console.log('🚀 Moment Search V3 启动中...');

                // 初始化全局事件管理器
                GlobalEventManager.init();

                // 检查数据迁移
                await this.checkDataMigration();

                // 初始化各个模块
                this.timeManager = new TimeManager();
                this.searchManager = new SearchManager();
                this.searchModeManager = new SearchModeManager();

                // 延迟初始化非关键模块
                setTimeout(async () => {
                    await this.initNonCriticalModules();
                }, 100);

                console.log('✅ Moment Search V3 启动完成');

            } catch (error) {
                console.error('❌ 初始化失败:', error);
                this.showError('启动失败，请刷新页面重试');
            }
        }

        async initNonCriticalModules() {
            console.log('🔄 初始化非关键模块...');

            // 初始化页面管理器（必须在快捷方式管理器之前）
            this.pageManager = new PageManager();

            // 初始化快捷方式管理器
            this.shortcutManager = new ShortcutManager();

            // 初始化设置管理器
            this.settingsManager = new SettingsManager();

            // 初始化背景管理器（异步初始化）
            this.backgroundManager = new BackgroundManager();
            await this.backgroundManager.init();

            console.log('✅ 非关键模块初始化完成');
        }

        async checkDataMigration() {
            // 检查是否需要从V2迁移数据
            const v2History = localStorage.getItem('momentSearchHistory');
            const v3Migrated = localStorage.getItem('v3_migrated');
            
            if (v2History && !v3Migrated) {
                console.log('🔄 检测到V2数据，开始迁移...');
                await this.migrateFromV2();
                localStorage.setItem('v3_migrated', 'true');
                console.log('✅ V2数据迁移完成');
            }
        }

        async migrateFromV2() {
            try {
                // 迁移主题设置
                const v2Theme = localStorage.getItem('momentSearchTheme');
                if (v2Theme) {
                    const settings = Storage.get('settings_v3', {});
                    settings.appearance = settings.appearance || {};
                    settings.appearance.theme = v2Theme;
                    Storage.set('settings_v3', settings);
                }

                console.log('✅ V2设置迁移完成');
            } catch (error) {
                console.error('❌ 数据迁移失败:', error);
            }
        }

        showError(message) {
            document.body.innerHTML = `
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    color: white;
                    text-align: center;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                ">
                    <div>
                        <h2>⚠️ ${message}</h2>
                        <p style="margin-top: 16px; opacity: 0.8;">
                            如果问题持续存在，请尝试清除浏览器缓存
                        </p>
                    </div>
                </div>
            `;
        }
    }

    // 启动应用
    document.addEventListener('DOMContentLoaded', () => {
        window.app = new NewTabApp();
    });

    // 快捷方式管理器 - 使用新的图标系统
    // 注意：ShortcutManager现在从外部模块导入
    class ShortcutManager {
        constructor() {
            // 占位符类，实际功能由新的图标系统提供
            console.log('旧的ShortcutManager已被禁用，等待新系统加载...');
            this.shortcuts = [];
            this.container = document.getElementById('shortcutsGrid');
        }

        // 兼容性方法
        init() {
            console.log('ShortcutManager.init() - 等待新系统接管');
        }

        render() {
            console.log('ShortcutManager.render() - 等待新系统接管');
        }

        loadShortcuts() {
            console.log('ShortcutManager.loadShortcuts() - 等待新系统接管');
        }

        saveShortcuts() {
            console.log('ShortcutManager.saveShortcuts() - 等待新系统接管');
        }

        addShortcut(shortcut) {
            console.log('ShortcutManager.addShortcut() - 等待新系统接管', shortcut);
        }

        deleteShortcut(id) {
            console.log('ShortcutManager.deleteShortcut() - 等待新系统接管', id);
        }

        editShortcut(id) {
            console.log('ShortcutManager.editShortcut() - 等待新系统接管', id);
        }

        getShortcuts() {
            console.log('ShortcutManager.getShortcuts() - 等待新系统接管');
            return this.shortcuts;
        }

        bindEvents() {
            console.log('ShortcutManager.bindEvents() - 等待新系统接管');
        }
    }

            contextMenu.style.display = 'block';
            contextMenu.style.left = event.pageX + 'px';
            contextMenu.style.top = event.pageY + 'px';
        }

        hideContextMenu() {
            const contextMenu = document.getElementById('contextMenu');
            if (contextMenu) {
                contextMenu.style.display = 'none';
            }
        }

        showModal(title = '添加图标', shortcut = null) {
            const modal = document.getElementById('modalOverlay');
            const modalTitle = document.getElementById('modalTitle');
            const nameInput = document.getElementById('shortcutName');
            const urlInput = document.getElementById('shortcutUrl');
            const iconStatus = document.getElementById('iconStatus');

            if (!modal) return;

            if (modalTitle) modalTitle.textContent = title;

            if (shortcut) {
                if (nameInput) nameInput.value = shortcut.name;
                if (urlInput) urlInput.value = shortcut.url;
                this.currentEditingId = shortcut.id;

                // 初始化图标预览
                this.updateIconPreview(shortcut.icon, 'emoji');
                if (iconStatus) iconStatus.textContent = '已设置图标';
            } else {
                if (nameInput) {
                    nameInput.value = '';
                    nameInput.placeholder = '自动解析中...';
                }
                if (urlInput) urlInput.value = '';
                this.currentEditingId = null;

                // 初始化图标预览为默认图标
                this.updateIconPreview('🌐', 'emoji');
                if (iconStatus) iconStatus.textContent = '请输入URL自动获取图标';
            }

            modal.style.display = 'flex';

            // 聚焦到URL输入框
            setTimeout(() => {
                if (urlInput) urlInput.focus();
            }, 100);
        }

        hideModal() {
            const modal = document.getElementById('modalOverlay');
            if (modal) {
                modal.style.display = 'none';
            }
            this.currentEditingId = null;
        }

        handleModalConfirm() {
            const nameInput = document.getElementById('shortcutName');
            const urlInput = document.getElementById('shortcutUrl');
            const previewFavicon = document.getElementById('previewFavicon');
            const previewEmoji = document.getElementById('previewEmoji');

            const name = nameInput?.value.trim();
            const url = urlInput?.value.trim();

            // 获取当前显示的图标
            let icon = '🌐'; // 默认图标
            if (previewFavicon && previewFavicon.style.display !== 'none' && previewFavicon.src) {
                icon = previewFavicon.src;
            } else if (previewEmoji && previewEmoji.style.display !== 'none') {
                icon = previewEmoji.textContent;
            }

            if (!name || !url) {
                Utils.showToast('请填写名称和URL', 'warning');
                return;
            }

            if (!Utils.isValidUrl(url)) {
                Utils.showToast('请输入有效的URL', 'warning');
                return;
            }

            if (this.currentEditingId) {
                // 编辑模式
                this.updateShortcut(this.currentEditingId, { name, url, icon });
            } else {
                // 添加模式
                this.addShortcut(name, url, icon);
            }

            this.hideModal();
        }

        addShortcut(name, url, icon, category = 'productivity') {
            if (this.shortcuts.length >= this.maxShortcuts) {
                Utils.showToast(`最多只能添加 ${this.maxShortcuts} 个快捷方式`, 'warning');
                return false;
            }

            const newShortcut = {
                id: Utils.generateId(),
                name,
                url: url.startsWith('http') ? url : 'https://' + url,
                icon,
                order: this.shortcuts.length + 1,
                category
            };

            this.shortcuts.push(newShortcut);
            this.saveShortcuts();
            this.render();
            return true;
        }

        updateShortcut(id, data) {
            const index = this.shortcuts.findIndex(s => s.id == id);
            if (index !== -1) {
                this.shortcuts[index] = {
                    ...this.shortcuts[index],
                    ...data,
                    url: data.url && !data.url.startsWith('http') ? 'https://' + data.url : data.url
                };
                this.saveShortcuts();
                this.render();
            }
        }

        deleteShortcut(id) {
            if (confirm('确定要删除这个快捷方式吗？')) {
                this.shortcuts = this.shortcuts.filter(s => s.id != id);
                this.saveShortcuts();
                this.render();
            }
        }

        editShortcut(id) {
            const shortcut = this.shortcuts.find(s => s.id == id);
            if (shortcut) {
                this.showModal('编辑图标', shortcut);
            }
        }

        // 公共方法供设置面板调用
        showAddModal() {
            this.showModal('添加快捷方式');
        }

        resetToDefault() {
            if (confirm('确定要重置为默认快捷方式吗？这将删除所有自定义快捷方式。')) {
                this.shortcuts = [...DEFAULT_SHORTCUTS];
                this.saveShortcuts();
                this.render();
            }
        }

        // 清除拖拽状态的通用方法
        clearDragStates() {
            if (!this.container) return;

            // 清除拖拽中的元素状态
            this.container.querySelectorAll('.dragging').forEach(item => {
                item.classList.remove('dragging');
            });

            // 清除插入指示器
            this.container.querySelectorAll('.drag-over').forEach(item => {
                item.classList.remove('drag-over');
            });

            // 清除容器状态
            this.container.classList.remove('drag-active');

            // 清除叠放目标状态
            this.container.querySelectorAll('.stack-target').forEach(item => {
                item.classList.remove('stack-target');
            });
        }

        // 启用拖拽排序功能
        enableDragAndDrop() {
            if (!this.container) return;

            // 先清理现有的拖拽事件监听器
            this.disableDragAndDrop();

            // 保存事件处理函数的引用，便于后续清理
            this.dragStartHandler = (e) => {
                const shortcutItem = e.target.closest('.shortcut-item');
                if (shortcutItem) {
                    shortcutItem.classList.add('dragging');
                    this.container.classList.add('drag-active');
                    e.dataTransfer.effectAllowed = 'move';
                    e.dataTransfer.setData('text/html', shortcutItem.outerHTML);
                    e.dataTransfer.setData('text/plain', shortcutItem.dataset.id);
                }
            };

            this.dragOverHandler = (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                // 清除之前的指示器
                this.container.querySelectorAll('.drag-over, .stack-target').forEach(item => {
                    item.classList.remove('drag-over', 'stack-target');
                });

                const dragging = this.container.querySelector('.dragging');
                if (!dragging) return;

                // 检查是否悬停在其他快捷方式上（叠放）
                const targetElement = this.getElementUnderCursor(e.clientX, e.clientY, dragging);

                if (targetElement && targetElement.classList.contains('shortcut-item')) {
                    // 叠放模式
                    targetElement.classList.add('stack-target');
                    this.currentStackTarget = targetElement;
                } else {
                    // 排序模式
                    this.currentStackTarget = null;
                    const afterElement = this.getDragAfterElement(this.container, e.clientY);

                    // 添加插入指示器
                    if (afterElement && afterElement !== dragging) {
                        afterElement.classList.add('drag-over');
                    }

                    if (dragging && afterElement == null) {
                        this.container.appendChild(dragging);
                    } else if (dragging && afterElement) {
                        this.container.insertBefore(dragging, afterElement);
                    }
                }
            };

            this.dragEndHandler = (e) => {
                const shortcutItem = e.target.closest('.shortcut-item');
                if (shortcutItem) {
                    this.clearDragStates();
                    this.updateShortcutOrder();
                }
            };

            this.dropHandler = (e) => {
                e.preventDefault();

                const dragging = this.container.querySelector('.dragging');
                if (!dragging) return;

                // 检查是否是叠放操作
                if (this.currentStackTarget) {
                    this.handleStackDrop(dragging, this.currentStackTarget);
                } else {
                    // 普通排序操作
                    this.updateShortcutOrder();
                }

                this.clearDragStates();
                this.currentStackTarget = null;
            };

            // 绑定事件监听器
            this.container.addEventListener('dragstart', this.dragStartHandler);
            this.container.addEventListener('dragover', this.dragOverHandler);
            this.container.addEventListener('dragend', this.dragEndHandler);
            this.container.addEventListener('drop', this.dropHandler);
        }

        // 禁用拖拽排序功能
        disableDragAndDrop() {
            if (!this.container) return;

            if (this.dragStartHandler) {
                this.container.removeEventListener('dragstart', this.dragStartHandler);
            }
            if (this.dragOverHandler) {
                this.container.removeEventListener('dragover', this.dragOverHandler);
            }
            if (this.dragEndHandler) {
                this.container.removeEventListener('dragend', this.dragEndHandler);
            }
            if (this.dropHandler) {
                this.container.removeEventListener('drop', this.dropHandler);
            }
        }

        // 获取拖拽后的插入位置
        getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.shortcut-item:not(.dragging)')];

            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;

                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        // 更新快捷方式顺序
        updateShortcutOrder() {
            const items = this.container.querySelectorAll('.shortcut-item');
            items.forEach((item, index) => {
                const shortcutId = parseInt(item.dataset.id);
                const shortcut = this.shortcuts.find(s => s.id === shortcutId);
                if (shortcut) {
                    shortcut.order = index + 1;
                }
            });

            this.saveShortcuts();
        }

        // 获取鼠标位置下的元素（排除拖拽中的元素）
        getElementUnderCursor(x, y, excludeElement) {
            // 临时隐藏拖拽中的元素
            const originalDisplay = excludeElement.style.display;
            excludeElement.style.display = 'none';

            // 获取鼠标位置下的元素
            const elementBelow = document.elementFromPoint(x, y);

            // 恢复拖拽元素的显示
            excludeElement.style.display = originalDisplay;

            // 返回最近的快捷方式项
            return elementBelow?.closest('.shortcut-item');
        }

        // 处理叠放操作
        handleStackDrop(draggedElement, targetElement) {
            const draggedId = draggedElement.dataset.id;
            const targetId = targetElement.dataset.id;

            if (draggedId === targetId) return; // 不能叠放到自己身上

            const draggedShortcut = this.shortcuts.find(s => s.id == draggedId);
            const targetShortcut = this.shortcuts.find(s => s.id == targetId);

            if (!draggedShortcut || !targetShortcut) return;

            // 显示叠放选项对话框
            this.showStackDialog(draggedShortcut, targetShortcut);
        }

        // 显示叠放选项对话框
        showStackDialog(draggedShortcut, targetShortcut) {
            // 移除现有对话框，避免重复
            const existingDialog = document.querySelector('.stack-dialog-overlay');
            if (existingDialog) {
                existingDialog.remove();
            }

            const dialog = document.createElement('div');
            dialog.className = 'stack-dialog-overlay';
            dialog.innerHTML = `
                <div class="stack-dialog">
                    <div class="stack-dialog-header">
                        <h3>图标叠放选项</h3>
                        <button class="stack-dialog-close">×</button>
                    </div>
                    <div class="stack-dialog-content">
                        <div class="stack-preview">
                            <div class="stack-item">
                                <div class="shortcut-icon">
                                    <img class="shortcut-favicon" src="" alt="${draggedShortcut.name}" style="display: none;">
                                    <span class="shortcut-emoji">${draggedShortcut.icon}</span>
                                </div>
                                <span>${draggedShortcut.name}</span>
                            </div>
                            <div class="stack-arrow">→</div>
                            <div class="stack-item">
                                <div class="shortcut-icon">
                                    <img class="shortcut-favicon" src="" alt="${targetShortcut.name}" style="display: none;">
                                    <span class="shortcut-emoji">${targetShortcut.icon}</span>
                                </div>
                                <span>${targetShortcut.name}</span>
                            </div>
                        </div>
                        <div class="stack-options">
                            <button class="stack-option-btn create-folder">
                                📁 创建文件夹
                            </button>
                            <button class="stack-option-btn merge-shortcuts">
                                🔗 合并快捷方式
                            </button>
                            <button class="stack-option-btn cancel-stack">
                                ❌ 取消
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            // 使用事件委托，避免多个事件监听器
            const handleDialogClick = (e) => {
                const target = e.target;

                if (target.classList.contains('stack-dialog-close') ||
                    target.classList.contains('cancel-stack') ||
                    target === dialog) {
                    closeDialog();
                } else if (target.classList.contains('create-folder')) {
                    this.createFolder(draggedShortcut, targetShortcut);
                    closeDialog();
                } else if (target.classList.contains('merge-shortcuts')) {
                    this.mergeShortcuts(draggedShortcut, targetShortcut);
                    closeDialog();
                }
            };

            const closeDialog = () => {
                dialog.removeEventListener('click', handleDialogClick);
                if (dialog.parentNode) {
                    document.body.removeChild(dialog);
                }
            };

            dialog.addEventListener('click', handleDialogClick);
        }

        // 创建文件夹
        createFolder(shortcut1, shortcut2) {
            // 文件夹功能暂未实现，使用更优雅的提示方式
            Utils.showToast(`文件夹功能开发中，将包含: ${shortcut1.name} 和 ${shortcut2.name}`, 'info');
        }

        // 合并快捷方式
        mergeShortcuts(draggedShortcut, targetShortcut) {
            const mergedName = `${targetShortcut.name} & ${draggedShortcut.name}`;
            const mergedUrl = targetShortcut.url; // 保留目标的URL
            const mergedIcon = targetShortcut.icon; // 保留目标的图标

            // 更新目标快捷方式
            this.updateShortcut(targetShortcut.id, {
                name: mergedName,
                url: mergedUrl,
                icon: mergedIcon
            });

            // 删除被拖拽的快捷方式
            this.shortcuts = this.shortcuts.filter(s => s.id != draggedShortcut.id);
            this.saveShortcuts();
            this.render();
        }

        exportShortcuts() {
            const data = {
                shortcuts: this.shortcuts,
                exportTime: new Date().toISOString(),
                version: '3.0.0'
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'moment-search-shortcuts.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        importShortcuts(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    if (data.shortcuts && Array.isArray(data.shortcuts)) {
                        if (confirm('确定要导入快捷方式吗？这将覆盖现有的快捷方式。')) {
                            this.shortcuts = data.shortcuts;
                            this.saveShortcuts();
                            this.render();
                            alert('导入成功！');
                        }
                    } else {
                        alert('无效的文件格式');
                    }
                } catch (error) {
                    alert('文件解析失败');
                    console.error('导入失败:', error);
                }
            };
            reader.readAsText(file);
        }
    }

    // 设置管理器
    class SettingsManager {
        constructor() {
            this.settings = this.getDefaultSettings();
            this.panel = document.getElementById('settingsPanel');
            this.content = document.getElementById('settingsContent');
            this.currentSection = 'appearance'; // 跟踪当前激活的页面

            this.init();
        }

        getDefaultSettings() {
            return {
                search: {
                    defaultPlatform: 'all',
                    smartMatch: true,
                    autoClear: true
                },
                appearance: {
                    theme: 'auto',
                    wallpaper: 'default'
                },
                time: {
                    showTime: true,
                    showDate: true,
                    showWeekday: true,
                    format24h: true,
                    showSeconds: false,
                    fontSize: 50, // 现在使用百分比，50%为默认值
                    color: '#ffffff',
                    fontFamily: 'arial', // 字体系列，默认使用Arial
                    fontWeight: 400 // 字体粗细
                },
                shortcuts: {
                    maxCount: 20,
                    showNames: true,
                    iconSize: 60, // 改为数值，默认60px
                    iconSpacing: 16, // 图标间距，默认16px
                    containerWidth: 80, // 展示区域宽度百分比，默认80%
                    textColor: '#ffffff', // 图标名称文字颜色
                    modeSwitch: 'Ctrl+Q' // 模式切换快捷键
                }
            };
        }

        // 字体系列映射配置
        getFontFamilyMap() {
            return {
                'arial': '"Arial", "Helvetica", sans-serif',
                'monospace': '"Consolas", "Monaco", "Courier New", monospace',
                'yahei': '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif',
                'times': '"Times New Roman", "Times", serif'
            };
        }

        // 字体粗细名称映射配置
        getFontWeightNames() {
            return {
                400: 'Normal',
                600: 'Semi Bold'
            };
        }

        init() {
            this.loadSettings();
            this.bindEvents();
            this.applySettings();
        }

        loadSettings() {
            const saved = Storage.get('settings_v3');
            if (saved) {
                this.settings = this.mergeSettings(this.settings, saved);
                // 数据迁移：将旧的字符串图标大小转换为数字
                this.migrateIconSizeSettings();
            }
        }

        migrateIconSizeSettings() {
            const shortcuts = this.settings.shortcuts;
            if (!shortcuts || typeof shortcuts.iconSize !== 'string') return;

            // 迁移旧的字符串图标大小到数字
            const sizeMap = { 'small': 48, 'medium': 60, 'large': 72 };
            shortcuts.iconSize = sizeMap[shortcuts.iconSize] || 60;

            // 添加缺失的新设置项
            const defaults = {
                iconSpacing: 16,
                containerWidth: 80,
                textColor: '#ffffff'
            };

            Object.entries(defaults).forEach(([key, value]) => {
                if (!shortcuts[key]) shortcuts[key] = value;
            });

            this.saveSettings();
            console.log('图标设置已迁移到新格式');
        }

        mergeSettings(defaults, saved) {
            const result = { ...defaults };
            for (const key in saved) {
                if (typeof saved[key] === 'object' && saved[key] !== null) {
                    result[key] = { ...defaults[key], ...saved[key] };
                } else {
                    result[key] = saved[key];
                }
            }
            return result;
        }

        saveSettings() {
            Storage.set('settings_v3', this.settings);
        }

        bindEvents() {
            const settingsBtn = document.getElementById('settingsBtn');
            const closeBtn = document.getElementById('closeSettings');
            const overlay = document.getElementById('settingsOverlay');

            if (settingsBtn) {
                settingsBtn.addEventListener('click', () => this.showPanel());
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.hidePanel());
            }

            if (overlay) {
                overlay.addEventListener('click', () => this.hidePanel());
            }

            // ESC键关闭设置面板
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.panel?.classList.contains('show')) {
                    this.hidePanel();
                }
            });
        }

        showPanel() {
            const overlay = document.getElementById('settingsOverlay');
            if (this.panel) {
                this.panel.classList.add('show');
                this.renderSettingsContent();
            }
            if (overlay) {
                overlay.classList.add('show');
            }
        }

        hidePanel() {
            const overlay = document.getElementById('settingsOverlay');
            if (this.panel) {
                this.panel.classList.remove('show');
            }
            if (overlay) {
                overlay.classList.remove('show');
            }
        }

        renderSettingsContent() {
            if (!this.content) return;

            this.content.innerHTML = `
                ${this.generateNavigationHTML()}
                ${this.generateMainContentHTML()}
            `;

            // 恢复当前激活的页面状态
            this.switchSection(this.currentSection);

            // 每次重新渲染后都需要重新绑定事件，因为DOM元素被重新创建
            this.bindSettingsEvents();
        }

        generateNavigationHTML() {
            const navItems = [
                { section: 'appearance', label: '外观设置' },
                { section: 'time', label: '时间设置' },
                { section: 'shortcuts', label: '图标设置' },
                { section: 'search', label: '搜索设置' },
                { section: 'platforms', label: '搜索直达' },
                { section: 'data', label: '数据管理' },
                { section: 'about', label: '关于' }
            ];

            return `
                <div class="settings-nav">
                    ${navItems.map(item => `
                        <button class="settings-nav-item" data-section="${item.section}">
                            ${item.label}
                        </button>
                    `).join('')}
                </div>
            `;
        }

        generateMainContentHTML() {
            return `
                <div class="settings-main">
                    ${this.generateSearchSection()}
                    ${this.generateAppearanceSection()}
                    ${this.generateTimeSection()}
                    ${this.generateShortcutsSection()}
                    ${this.generatePlatformsSection()}
                    ${this.generateDataSection()}
                    ${this.generateAboutSection()}
                </div>
            `;
        }

        generateSearchSection() {
            return `
                <div class="settings-section" data-section="search">
                    <div class="setting-group">
                        <h4>搜索模式</h4>
                        ${this.generateSettingItem('模式切换快捷键', this.generateCustomShortcutInput())}
                    </div>
                    <div class="setting-group">
                        <h4>搜索行为</h4>
                        ${this.generateSettingItem('智能匹配', this.generateSwitch('search.smartMatch', this.settings.search?.smartMatch !== false))}
                        ${this.generateSettingItem('自动清空搜索框', this.generateSwitch('search.autoClear', this.settings.search?.autoClear !== false))}
                    </div>
                </div>
            `;
        }

        generateAppearanceSection() {
            return `
                <div class="settings-section" data-section="appearance">
                    <div class="setting-group">
                        ${this.generateSettingItem('主题模式', this.generateBasicThemeSelect())}
                        ${this.generateSettingItem('自定义壁纸', '<button class="setting-btn secondary setting-inline-btn" data-action="wallpaper">选择壁纸</button>')}
                    </div>
                </div>
            `;
        }

        generateTimeSection() {
            return `
                <div class="settings-section" data-section="time">
                    <div class="setting-group">
                        <h4>显示控制</h4>
                        ${this.generateSettingItem('显示时间', this.generateSwitch('time.showTime', this.settings.time?.showTime !== false))}
                        ${this.generateSettingItem('月日显示', this.generateSwitch('time.showDate', this.settings.time?.showDate !== false))}
                        ${this.generateSettingItem('星期显示', this.generateSwitch('time.showWeekday', this.settings.time?.showWeekday !== false))}
                        ${this.generateSettingItem('24小时制', this.generateSwitch('time.format24h', this.settings.time?.format24h !== false))}
                        ${this.generateSettingItem('秒数显示', this.generateSwitch('time.showSeconds', this.settings.time?.showSeconds || false))}
                    </div>
                    <div class="setting-group">
                        <h4>样式自定义</h4>
                        ${this.generateSettingItem('大小', this.generateSizeSlider())}
                        ${this.generateSettingItem('颜色', this.generateColorPicker())}
                        ${this.generateSettingItem('字体', this.generateFontSelector())}
                        ${this.generateSettingItem('粗细', this.generateFontWeightSelector())}
                    </div>
                </div>
            `;
        }

        generateShortcutsSection() {
            return `
                <div class="settings-section" data-section="shortcuts">
                    <div class="setting-group">
                        ${this.generateSettingItem('图标大小', this.generateIconSizeSlider())}
                        ${this.generateSettingItem('图标间距', this.generateIconSpacingSlider())}
                        ${this.generateSettingItem('展示区域宽度', this.generateContainerWidthSlider())}
                        ${this.generateSettingItem('图标名称显示控制', this.generateSwitch('shortcuts.showNames', this.settings.shortcuts.showNames))}
                        ${this.generateSettingItem('文字颜色', this.generateTextColorPicker())}
                    </div>
                </div>
            `;
        }

        generatePlatformsSection() {
            return `
                <div class="settings-section" data-section="platforms">
                    <!-- 标签页切换 -->
                    <div class="platform-tabs">
                        <button class="platform-tab active" data-tab="url-parser">URL解析</button>
                        <button class="platform-tab" data-tab="platform-list">平台列表</button>
                    </div>

                    <!-- URL解析标签页 -->
                    <div class="platform-tab-content active" data-tab-content="url-parser">
                        <div class="setting-group">
                            <h4>智能URL解析</h4>
                            <p class="setting-description">粘贴完整的搜索结果URL，系统将自动识别搜索参数并生成搜索模板</p>

                            <div class="url-parser-form">
                                <div class="form-row">
                                    <label>搜索结果URL:</label>
                                    <input type="text" id="urlInput" placeholder="例如: https://github.com/search?q=javascript" class="setting-input">
                                </div>

                                <div class="form-row">
                                    <button id="parseUrlBtn" class="setting-btn primary">解析URL</button>
                                </div>
                            </div>

                            <!-- 解析结果显示 -->
                            <div id="parseResult" class="parse-result" style="display: none;">
                                <div class="result-header">
                                    <span class="result-status"></span>
                                    <span class="result-title"></span>
                                </div>
                                <div class="result-details"></div>

                                <!-- 平台配置 -->
                                <div class="platform-config" style="display: none;">
                                    <div class="form-row">
                                        <label>平台名称:</label>
                                        <input type="text" id="platformName" class="setting-input">
                                    </div>
                                    <div class="form-row">
                                        <label>别名 (用逗号分隔):</label>
                                        <input type="text" id="platformAliases" placeholder="例如: gh, github" class="setting-input">
                                    </div>
                                    <div class="form-row">
                                        <button id="savePlatformBtn" class="setting-btn success">保存平台</button>
                                        <button id="cancelParseBtn" class="setting-btn secondary">取消</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 平台列表标签页 -->
                    <div class="platform-tab-content" data-tab-content="platform-list">
                        <div class="setting-group">
                            <h4>已添加的平台</h4>
                            <div id="platformStats" class="platform-stats"></div>

                            <div class="platform-filter">
                                <button class="filter-btn active" data-filter="all">全部</button>
                                <button class="filter-btn" data-filter="builtin">内置</button>
                                <button class="filter-btn" data-filter="custom">自定义</button>
                            </div>

                            <div id="platformList" class="platform-list">
                                <!-- 平台列表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        generateDataSection() {
            return `
                <div class="settings-section" data-section="data">
                    <div class="setting-group">
                        <div class="button-group">
                            <button data-action="export-data" class="setting-btn secondary">📤 导出数据</button>
                            <button data-action="import-data" class="setting-btn secondary">📥 导入数据</button>
                        </div>
                        <input type="file" id="importFile" accept=".json" style="display: none;">
                        <button data-action="clear-data" class="setting-btn danger">🗑️ 清空所有数据</button>
                    </div>
                </div>
            `;
        }

        generateAboutSection() {
            return `
                <div class="settings-section" data-section="about">
                    <div class="setting-group">
                        <div class="about-content">
                            <div class="about-title">Moment Search V3.0.0</div>
                            <div class="about-description">简洁美观的新标签页扩展</div>
                            <a href="https://github.com/moment-search" target="_blank" class="about-link">
                                GitHub 项目主页
                            </a>
                        </div>
                    </div>
                </div>
            `;
        }

        // HTML生成辅助方法
        generateSettingItem(label, control) {
            return `
                <div class="setting-item">
                    <span class="setting-label">${label}</span>
                    <div class="setting-control">${control}</div>
                </div>
            `;
        }

        generateSwitch(togglePath, isActive) {
            return `<div class="switch ${isActive ? 'active' : ''}" data-toggle="${togglePath}"></div>`;
        }

        generateBasicThemeSelect() {
            const options = [
                { value: 'light', label: '浅色' },
                { value: 'dark', label: '深色' },
                { value: 'auto', label: '自动' }
            ];
            return this.generateSelect('appearance.theme', options, this.settings.appearance.theme);
        }

        // 搜索设置相关生成方法
        generateCustomShortcutInput() {
            const currentShortcut = this.settings.shortcuts?.modeSwitch || 'Ctrl+Q';
            return `
                <div class="custom-shortcut-input">
                    <div class="shortcut-display" id="shortcutDisplay">
                        <span class="shortcut-keys">${currentShortcut}</span>
                        <button type="button" class="shortcut-edit-btn" id="shortcutEditBtn">修改</button>
                    </div>
                    <div class="shortcut-input-container" id="shortcutInputContainer" style="display: none;">
                        <input type="text"
                               class="shortcut-input"
                               id="shortcutInput"
                               placeholder="按下你想要的快捷键组合..."
                               readonly>
                        <div class="shortcut-actions">
                            <button type="button" class="shortcut-save-btn" id="shortcutSaveBtn" disabled>保存</button>
                            <button type="button" class="shortcut-cancel-btn" id="shortcutCancelBtn">取消</button>
                        </div>
                        <div class="shortcut-help">
                            <p>支持的修饰键：Ctrl, Alt, Shift</p>
                            <p>支持的主键：字母、数字、功能键等</p>
                        </div>
                        <div class="shortcut-error" id="shortcutError" style="display: none;"></div>
                    </div>
                </div>
            `;
        }



        generateTimeFormatSelect() {
            const options = [
                { value: '24h', label: '24小时制' },
                { value: '12h', label: '12小时制' }
            ];
            return this.generateSelect('appearance.timeFormat', options, this.settings.appearance.timeFormat);
        }

        generateSelect(settingPath, options, currentValue) {
            return `
                <select class="setting-select" data-setting="${settingPath}">
                    ${options.map(option =>
                        `<option value="${option.value}" ${currentValue === option.value ? 'selected' : ''}>${option.label}</option>`
                    ).join('')}
                </select>
            `;
        }

        // 通用滑动条生成方法
        generateSlider(settingPath, min, max, defaultValue, unit = '%', id = null) {
            const keys = settingPath.split('.');
            let current = this.settings;
            for (const key of keys) {
                current = current?.[key];
            }
            const currentValue = current || defaultValue;
            let actualValue = currentValue;

            // 特殊处理：确保值在有效范围内
            if (settingPath === 'time.fontSize') {
                actualValue = Math.max(30, currentValue);
            }

            // 生成显示值
            let displayValue = `${actualValue}${unit}`;

            return `
                <div class="slider-container">
                    <input type="range" class="setting-slider"
                           data-setting="${settingPath}"
                           min="${min}" max="${max}" value="${actualValue}"
                           ${id ? `id="${id}"` : ''}>
                    <span class="slider-value">${displayValue}</span>
                </div>
            `;
        }

        generateSizeSlider() {
            return this.generateSlider('time.fontSize', 30, 100, 50, '%', 'timeFontSize');
        }

        // 通用颜色选择器生成方法
        generateColorPickerComponent(settingPath, defaultColor = '#ffffff') {
            const keys = settingPath.split('.');
            let current = this.settings;
            for (const key of keys) {
                current = current?.[key];
            }
            const currentColor = current || defaultColor;

            const presetColors = [
                '#ffffff', '#000000', '#ff0000', '#00ff00', '#0000ff',
                '#ffff00', '#ff00ff', '#00ffff', '#ffa500'
            ];

            return `
                <div class="color-picker-container">
                    <div class="preset-colors">
                        ${presetColors.map(color => `
                            <div class="color-preset ${currentColor === color ? 'active' : ''}"
                                 data-color="${color}"
                                 data-setting="${settingPath}"
                                 style="background-color: ${color}"></div>
                        `).join('')}
                        <div class="custom-color-picker">
                            <input type="color" class="custom-color-input"
                                   data-setting="${settingPath}"
                                   value="${currentColor}"
                                   title="自定义颜色">
                            <div class="custom-color-icon"></div>
                        </div>
                    </div>
                </div>
            `;
        }

        generateColorPicker() {
            return this.generateColorPickerComponent('time.color');
        }

        generateIconSizeSlider() {
            return this.generateSlider('shortcuts.iconSize', 50, 100, 60, 'px', 'iconSize');
        }

        generateIconSpacingSlider() {
            return this.generateSlider('shortcuts.iconSpacing', 8, 60, 16, 'px', 'iconSpacing');
        }

        generateContainerWidthSlider() {
            return this.generateSlider('shortcuts.containerWidth', 60, 85, 80, '%', 'containerWidth');
        }

        generateTextColorPicker() {
            return this.generateColorPickerComponent('shortcuts.textColor');
        }

        generateFontSelector() {
            const currentFont = this.settings.time?.fontFamily || 'arial';
            const fontMap = this.getFontFamilyMap();

            const fontOptions = [
                {
                    value: 'arial',
                    name: '系统默认',
                    family: fontMap['arial']
                },
                {
                    value: 'monospace',
                    name: '等宽字体',
                    family: fontMap['monospace']
                },
                {
                    value: 'yahei',
                    name: '微软雅黑',
                    family: fontMap['yahei']
                },
                {
                    value: 'times',
                    name: 'Times',
                    family: fontMap['times']
                }
            ];

            return `
                <div class="font-selector-container">
                    <div class="font-grid">
                        ${fontOptions.map(font => `
                            <div class="font-option ${currentFont === font.value ? 'selected' : ''}"
                                 data-font="${font.value}"
                                 data-setting="time.fontFamily"
                                 style="font-family: ${font.family}">
                                <div class="font-name">${font.name}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        generateFontWeightSelector() {
            const currentWeight = this.settings.time?.fontWeight || 400;

            const weightOptions = [
                {
                    value: 400,
                    name: '正常'
                },
                {
                    value: 600,
                    name: '粗体'
                }
            ];

            return `
                <div class="font-weight-selector-container">
                    <div class="font-weight-grid">
                        ${weightOptions.map(weight => `
                            <div class="font-weight-option ${currentWeight === weight.value ? 'selected' : ''}"
                                 data-weight="${weight.value}"
                                 data-setting="time.fontWeight"
                                 style="font-weight: ${weight.value}">
                                <div class="weight-name">${weight.name}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        updateSetting(path, value) {
            const keys = path.split('.');
            let current = this.settings;

            for (let i = 0; i < keys.length - 1; i++) {
                current = current[keys[i]];
            }

            current[keys[keys.length - 1]] = value;
            this.saveSettings();
            this.applySettings();

            console.log('设置已更新:', path, '=', value);
        }

        toggleSetting(path) {
            const keys = path.split('.');
            let current = this.settings;

            for (let i = 0; i < keys.length - 1; i++) {
                current = current[keys[i]];
            }

            const currentValue = current[keys[keys.length - 1]];
            const newValue = !currentValue;

            current[keys[keys.length - 1]] = newValue;
            this.saveSettings();
            this.applySettings();

            // 只更新开关的视觉状态，不重新渲染整个页面
            this.updateSwitchVisualState(path, newValue);

            console.log('设置已切换:', path, '=', newValue);
        }

        updateSwitchVisualState(path, value) {
            // 找到对应的开关并更新其视觉状态
            const switches = this.content.querySelectorAll('.switch');
            const targetSwitch = Array.from(switches).find(sw => sw.dataset.toggle === path);
            if (targetSwitch) {
                if (value) {
                    targetSwitch.classList.add('active');
                } else {
                    targetSwitch.classList.remove('active');
                }
            }
        }

        bindSettingsEvents() {
            if (!this.content) return;

            // 移除旧的事件监听器（如果存在）
            if (this.clickHandler) {
                this.content.removeEventListener('click', this.clickHandler);
            }
            if (this.changeHandler) {
                this.content.removeEventListener('change', this.changeHandler);
            }
            if (this.inputHandler) {
                this.content.removeEventListener('input', this.inputHandler);
            }

            // 创建新的事件处理器
            this.clickHandler = (e) => {
                const target = e.target;

                // 处理导航分类切换
                if (target.classList.contains('settings-nav-item') || target.closest('.settings-nav-item')) {
                    const navItem = target.classList.contains('settings-nav-item') ? target : target.closest('.settings-nav-item');
                    const section = navItem.dataset.section;
                    if (section) {
                        this.switchSection(section);
                    }
                    return;
                }

                // 处理开关切换
                if (target.classList.contains('switch')) {
                    const togglePath = target.dataset.toggle;
                    if (togglePath) {
                        this.toggleSetting(togglePath);
                    }
                }

                // 处理按钮点击
                if (target.dataset.action) {
                    this.handleAction(target.dataset.action);
                }

                // 处理平台管理标签页切换
                if (target.classList.contains('platform-tab')) {
                    const tabName = target.dataset.tab;
                    if (tabName) {
                        // 切换标签页
                        document.querySelectorAll('.platform-tab').forEach(tab => tab.classList.remove('active'));
                        document.querySelectorAll('.platform-tab-content').forEach(content => content.classList.remove('active'));

                        target.classList.add('active');
                        const content = document.querySelector(`[data-tab-content="${tabName}"]`);
                        if (content) {
                            content.classList.add('active');

                            // 如果切换到平台列表标签页，刷新列表
                            if (tabName === 'platform-list') {
                                setTimeout(() => this.refreshPlatformList(), 100);
                            }
                        }
                    }
                }

                // 处理平台过滤器
                if (target.classList.contains('filter-btn')) {
                    const filter = target.dataset.filter;
                    if (filter) {
                        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
                        target.classList.add('active');
                        this.refreshPlatformList();
                    }
                }

                // 处理平台管理按钮
                if (target.id === 'parseUrlBtn') {
                    this.handlePlatformAction('parse-url');
                }

                // 处理颜色预设点击
                if (target.classList.contains('color-preset')) {
                    const color = target.dataset.color;
                    const settingPath = target.dataset.setting || 'time.color'; // 支持不同的设置路径
                    this.updateSetting(settingPath, color);
                    // 更新预设颜色的选中状态
                    target.parentElement.querySelectorAll('.color-preset').forEach(el => el.classList.remove('active'));
                    target.classList.add('active');
                    // 同步自定义颜色输入框
                    const colorInput = target.parentElement.parentElement.querySelector('.custom-color-input');
                    if (colorInput) colorInput.value = color;
                }

                // 处理字体选择器点击
                if (target.classList.contains('font-option') || target.closest('.font-option')) {
                    const fontOption = target.classList.contains('font-option') ? target : target.closest('.font-option');
                    const fontValue = fontOption.dataset.font;
                    const settingPath = fontOption.dataset.setting;
                    if (fontValue && settingPath) {
                        this.updateSetting(settingPath, fontValue);
                        // 更新字体选择器的选中状态
                        fontOption.parentElement.querySelectorAll('.font-option').forEach(el => el.classList.remove('selected'));
                        fontOption.classList.add('selected');
                    }
                }

                // 处理字体粗细选择器点击
                if (target.classList.contains('font-weight-option') || target.closest('.font-weight-option')) {
                    const weightOption = target.classList.contains('font-weight-option') ? target : target.closest('.font-weight-option');
                    const weightValue = parseInt(weightOption.dataset.weight);
                    const settingPath = weightOption.dataset.setting;
                    if (weightValue && settingPath) {
                        this.updateSetting(settingPath, weightValue);
                        // 更新字体粗细选择器的选中状态
                        weightOption.parentElement.querySelectorAll('.font-weight-option').forEach(el => el.classList.remove('selected'));
                        weightOption.classList.add('selected');
                    }
                }
            };

            // 处理选择框变化和滑动条拖动结束
            this.changeHandler = (e) => {
                const target = e.target;
                if (target.classList.contains('setting-select')) {
                    const settingPath = target.dataset.setting;
                    if (settingPath) {
                        this.updateSetting(settingPath, target.value);
                    }
                }



                // 处理自定义颜色输入
                if (target.classList.contains('custom-color-input')) {
                    const settingPath = target.dataset.setting;
                    if (settingPath) {
                        this.updateSetting(settingPath, target.value);
                        // 清除预设颜色的选中状态
                        const presetColors = target.parentElement.querySelector('.preset-colors');
                        if (presetColors) {
                            presetColors.querySelectorAll('.color-preset').forEach(el => el.classList.remove('active'));
                        }
                    }
                }
            };

            // 处理滑动条实时变化（拖动过程中）
            this.inputHandler = (e) => {
                const target = e.target;
                if (target.classList.contains('setting-slider')) {
                    const settingPath = target.dataset.setting;
                    if (settingPath) {
                        let value = parseInt(target.value);



                        this.updateSetting(settingPath, value);
                        // 更新显示的数值
                        const valueDisplay = target.parentElement.querySelector('.slider-value');
                        if (valueDisplay) {
                            const unit = settingPath.includes('iconSize') || settingPath.includes('iconSpacing') ? 'px' : '%';
                            const displayValue = `${value}${unit}`;
                            valueDisplay.textContent = displayValue;
                        }
                    }
                }
            };

            // 绑定事件监听器
            this.content.addEventListener('click', this.clickHandler);
            this.content.addEventListener('change', this.changeHandler);
            this.content.addEventListener('input', this.inputHandler);

            // 绑定自定义快捷键输入事件
            this.bindShortcutInputEvents();

            // 处理文件输入
            const importFile = document.getElementById('importFile');
            if (importFile) {
                importFile.addEventListener('change', (e) => {
                    if (e.target.files[0]) {
                        this.importData(e.target.files[0]);
                    }
                });
            }
        }

        bindShortcutInputEvents() {
            // 延迟绑定，确保DOM已渲染
            setTimeout(() => {
                const editBtn = document.getElementById('shortcutEditBtn');
                const saveBtn = document.getElementById('shortcutSaveBtn');
                const cancelBtn = document.getElementById('shortcutCancelBtn');
                const input = document.getElementById('shortcutInput');
                const display = document.getElementById('shortcutDisplay');
                const container = document.getElementById('shortcutInputContainer');
                const errorDiv = document.getElementById('shortcutError');

                if (!editBtn || !saveBtn || !cancelBtn || !input) return;

                let currentShortcut = '';
                let isRecording = false;

                // 编辑按钮点击
                editBtn.addEventListener('click', () => {
                    display.style.display = 'none';
                    container.style.display = 'block';
                    input.focus();
                    input.value = '';
                    input.placeholder = '按下你想要的快捷键组合...';
                    input.classList.add('recording');
                    isRecording = true;
                    saveBtn.disabled = true;
                    this.hideShortcutError();
                });

                // 取消按钮点击
                cancelBtn.addEventListener('click', () => {
                    this.cancelShortcutEdit();
                });

                // 保存按钮点击
                saveBtn.addEventListener('click', () => {
                    if (currentShortcut) {
                        this.saveShortcut(currentShortcut);
                        this.cancelShortcutEdit();
                    }
                });

                // 快捷键录制
                input.addEventListener('keydown', (e) => {
                    if (!isRecording) return;

                    e.preventDefault();
                    e.stopPropagation();

                    const shortcut = this.captureShortcut(e);
                    if (shortcut) {
                        currentShortcut = shortcut;
                        input.value = shortcut;
                        input.classList.remove('recording');

                        // 验证快捷键
                        const validation = this.validateShortcut(shortcut);
                        if (validation.valid) {
                            saveBtn.disabled = false;
                            this.hideShortcutError();
                        } else {
                            saveBtn.disabled = true;
                            this.showShortcutError(validation.error);
                        }

                        isRecording = false;
                    }
                });

                // 防止输入框失去焦点时的默认行为
                input.addEventListener('blur', (e) => {
                    if (isRecording) {
                        setTimeout(() => input.focus(), 10);
                    }
                });
            }, 100);
        }

        captureShortcut(event) {
            const modifiers = [];
            const key = event.key;

            // 收集修饰键
            if (event.ctrlKey) modifiers.push('Ctrl');
            if (event.altKey) modifiers.push('Alt');
            if (event.shiftKey) modifiers.push('Shift');

            // 忽略单独的修饰键
            if (['Control', 'Alt', 'Shift', 'Meta'].includes(key)) {
                return null;
            }

            // 构建快捷键字符串
            if (modifiers.length === 0) {
                return null; // 必须有修饰键
            }

            const keyName = this.normalizeKeyName(key);
            return modifiers.join('+') + '+' + keyName;
        }

        normalizeKeyName(key) {
            // 标准化按键名称
            const keyMap = {
                ' ': 'Space',
                'ArrowUp': 'Up',
                'ArrowDown': 'Down',
                'ArrowLeft': 'Left',
                'ArrowRight': 'Right',
                'Escape': 'Esc'
            };

            return keyMap[key] || key.toUpperCase();
        }

        validateShortcut(shortcut) {
            // 系统保留的快捷键
            const reservedShortcuts = [
                'Ctrl+C', 'Ctrl+V', 'Ctrl+X', 'Ctrl+Z', 'Ctrl+Y',
                'Ctrl+A', 'Ctrl+S', 'Ctrl+O', 'Ctrl+N', 'Ctrl+P',
                'Ctrl+F', 'Ctrl+H', 'Ctrl+R', 'Ctrl+T', 'Ctrl+W',
                'Alt+F4', 'Ctrl+Alt+Delete', 'Ctrl+Shift+Esc'
            ];

            if (reservedShortcuts.includes(shortcut)) {
                return {
                    valid: false,
                    error: '此快捷键为系统保留，请选择其他组合'
                };
            }

            // 检查是否为有效的快捷键格式
            if (!shortcut.includes('+') || shortcut.split('+').length < 2) {
                return {
                    valid: false,
                    error: '请使用修饰键+主键的组合'
                };
            }

            return { valid: true };
        }

        showShortcutError(message) {
            const errorDiv = document.getElementById('shortcutError');
            if (errorDiv) {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }
        }

        hideShortcutError() {
            const errorDiv = document.getElementById('shortcutError');
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        }

        saveShortcut(shortcut) {
            // 保存到设置
            this.updateSetting('shortcuts.modeSwitch', shortcut);

            // 更新显示
            const shortcutKeys = document.querySelector('.shortcut-keys');
            if (shortcutKeys) {
                shortcutKeys.textContent = shortcut;
            }

            // 通知SearchModeManager更新快捷键
            if (window.app?.searchModeManager) {
                window.app.searchModeManager.updateShortcutKey(shortcut);
            }
        }

        cancelShortcutEdit() {
            const display = document.getElementById('shortcutDisplay');
            const container = document.getElementById('shortcutInputContainer');
            const input = document.getElementById('shortcutInput');

            if (display && container && input) {
                display.style.display = 'flex';
                container.style.display = 'none';
                input.classList.remove('recording');
                this.hideShortcutError();
            }
        }

        switchSection(sectionName) {
            // 更新当前激活的页面
            this.currentSection = sectionName;

            // 通用的状态切换方法
            this.updateActiveState('.settings-nav-item', sectionName);
            this.updateActiveState('.settings-section', sectionName);
        }

        updateActiveState(selector, activeSectionName) {
            const elements = this.content.querySelectorAll(selector);
            elements.forEach(element => {
                if (element.dataset.section === activeSectionName) {
                    element.classList.add('active');
                } else {
                    element.classList.remove('active');
                }
            });
        }



        handleAction(action) {
            switch (action) {
                case 'wallpaper':
                    if (window.app?.backgroundManager) {
                        window.app.backgroundManager.showWallpaperOptions();
                    } else {
                        console.error('backgroundManager未初始化');
                        alert('背景管理器尚未初始化，请稍后再试');
                    }
                    break;
                case 'add-shortcut':
                    if (window.app.shortcutManager) {
                        window.app.shortcutManager.showAddModal();
                    }
                    break;
                case 'reset-shortcuts':
                    if (window.app.shortcutManager) {
                        window.app.shortcutManager.resetToDefault();
                    }
                    break;
                case 'export-data':
                    this.exportData();
                    break;
                case 'import-data':
                    document.getElementById('importFile').click();
                    break;
                case 'clear-data':
                    this.clearAllData();
                    break;
                default:
                    console.warn('未知的操作:', action);
            }
        }

        applySettings() {
            // 应用时间显示设置
            if (window.app?.timeManager) {
                window.app.timeManager.applySettings();
            }

            // 应用主题设置
            this.applyTheme();

            // 应用快捷方式设置
            this.applyShortcutSettings();
        }

        applyTheme() {
            const theme = this.settings.appearance.theme;
            const body = document.body;

            // 移除现有主题类
            body.classList.remove('light-theme', 'dark-theme');

            if (theme === 'auto') {
                // 根据系统主题自动切换
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                body.classList.add(prefersDark ? 'dark-theme' : 'light-theme');
            } else {
                body.classList.add(theme + '-theme');
            }
        }



        applyShortcutSettings() {
            const grid = document.getElementById('shortcutsGrid');
            if (grid) {
                // 控制快捷方式名称显示
                if (this.settings.shortcuts.showNames) {
                    grid.classList.remove('hide-names');
                } else {
                    grid.classList.add('hide-names');
                }

                // 应用图标设置
                const { iconSize = 60, iconSpacing = 16, containerWidth = 80, textColor = '#ffffff' } = this.settings.shortcuts;

                // 应用动态样式
                const styleProperties = {
                    '--icon-size': `${iconSize}px`,
                    '--icon-spacing': `${iconSpacing}px`,
                    '--container-width': `${containerWidth}%`,
                    '--text-color': textColor
                };

                Object.entries(styleProperties).forEach(([property, value]) => {
                    grid.style.setProperty(property, value);
                });

                // 计算并设置网格列数
                const containerWidthPx = window.innerWidth * (containerWidth / 100);
                const totalIconWidth = iconSize + iconSpacing;
                const columns = Math.max(1, Math.floor(containerWidthPx / totalIconWidth));
                grid.style.setProperty('--grid-columns', columns);
            }
        }

        exportData() {
            try {
                const data = {
                    settings: this.settings,
                    pages: window.app?.pageManager?.exportPages() || { pages: [], currentPageIndex: 0 },
                    shortcuts: Storage.get('shortcuts_v3', []), // 兼容旧版本
                    exportTime: new Date().toISOString(),
                    version: '3.0.0'
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `moment-search-data-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                console.log('✅ 数据导出成功');
            } catch (error) {
                console.error('❌ 数据导出失败:', error);
                alert('导出失败，请重试');
            }
        }

        importData(file) {
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);

                    if (confirm('确定要导入数据吗？这将覆盖现有的所有设置和数据。')) {
                        let importSuccess = false;

                        // 导入设置
                        if (data.settings) {
                            this.settings = this.mergeSettings(this.getDefaultSettings(), data.settings);
                            this.saveSettings();
                            importSuccess = true;
                        }

                        // 导入页面数据（V3格式）
                        if (data.pages && window.app?.pageManager) {
                            if (window.app.pageManager.importPages(data.pages)) {
                                importSuccess = true;
                            }
                        }
                        // 兼容旧版本快捷方式数据
                        else if (data.shortcuts) {
                            Storage.set('shortcuts_v3', data.shortcuts);
                            if (window.app?.shortcutManager) {
                                window.app.shortcutManager.loadShortcuts();
                                window.app.shortcutManager.render();
                            }
                            importSuccess = true;
                        }



                        if (importSuccess) {
                            this.applySettings();
                            this.renderSettingsContent();
                            alert('导入成功！页面将在3秒后刷新以应用所有更改。');

                            // 延迟刷新页面以确保所有更改生效
                            setTimeout(() => {
                                location.reload();
                            }, 3000);
                        } else {
                            alert('没有找到有效的数据进行导入');
                        }
                    }
                } catch (error) {
                    alert('文件格式错误，导入失败');
                    console.error('❌ 导入失败:', error);
                }
            };
            reader.readAsText(file);
        }

        clearAllData() {
            if (confirm('确定要清空所有数据吗？这个操作不可恢复！')) {
                if (confirm('请再次确认：这将删除所有设置、快捷方式和搜索历史！')) {
                    // 清空所有数据
                    Storage.remove('settings_v3');
                    Storage.remove('shortcuts_v3');
                    Storage.remove('pages_v3');

                    // 重新加载页面
                    location.reload();
                }
            }
        }

        // 获取PlatformManager实例（避免重复代码）
        getPlatformManager() {
            const platformManager = window.app?.searchManager?.platformManager;
            if (!platformManager) {
                throw new Error('PlatformManager 未初始化');
            }
            return platformManager;
        }

        // 统一的错误处理方法
        handleError(error, operation = '操作') {
            console.error(`${operation}失败:`, error);
            const message = error.message || `${operation}失败，请重试`;
            alert(message);
        }

        // 获取常用DOM元素（避免重复查询）
        getParseElements() {
            return {
                urlInput: document.getElementById('urlInput'),
                parseResult: document.getElementById('parseResult')
            };
        }

        // 平台管理相关方法
        async handlePlatformAction(action, data = {}) {
            try {
                const platformManager = this.getPlatformManager();

                switch (action) {
                    case 'parse-url':
                        await this.parseURL();
                        break;
                    case 'save-platform':
                        await this.savePlatform();
                        break;

                    case 'delete-platform':
                        await this.deletePlatform(data.id);
                        break;

                    default:
                        console.warn('未知的平台操作:', action);
                }
            } catch (error) {
                this.handleError(error, '平台操作');
            }
        }

        async parseURL() {
            const urlInput = document.getElementById('urlInput');
            const parseResult = document.getElementById('parseResult');

            if (!urlInput || !parseResult) return;

            const url = urlInput.value.trim();

            if (!url) {
                alert('请输入URL');
                return;
            }

            // 显示解析中状态
            parseResult.style.display = 'block';
            parseResult.innerHTML = `
                <div class="result-header">
                    <span class="result-status" style="background: #ff9800;"></span>
                    <span class="result-title">正在解析...</span>
                </div>
            `;

            try {
                const platformManager = this.getPlatformManager();
                const result = await platformManager.urlParser.parseURL(url);

                if (result.success) {
                    this.showParseSuccess(result);
                } else {
                    this.showParseError(result);
                }
            } catch (error) {
                this.showParseError({ error: error.message });
            }
        }

        showParseSuccess(result) {
            const parseResult = document.getElementById('parseResult');
            parseResult.innerHTML = `
                <div class="result-header">
                    <span class="result-status success"></span>
                    <span class="result-title">解析成功</span>
                </div>
                <div class="result-details">
                    <strong>网站名称:</strong> ${result.name}<br>
                    <strong>域名:</strong> ${result.domain}<br>
                    <strong>搜索模板:</strong> ${result.template}<br>
                    <strong>解析方法:</strong> ${result.method === 'parameter' ? 'URL参数' : 'URL路径'} ${result.searchParam ? `(${result.searchParam})` : ''}<br>
                    <strong>置信度:</strong> ${Math.round(result.confidence * 100)}%
                </div>
                <div class="platform-config">
                    <div class="form-row">
                        <label>平台名称:</label>
                        <input type="text" id="parsePlatformName" class="setting-input" value="${result.name}">
                    </div>
                    <div class="form-row">
                        <label>别名 (用逗号分隔):</label>
                        <input type="text" id="parsePlatformAliases" placeholder="例如: gh, github" class="setting-input">
                    </div>
                    <div class="form-row">
                        <button id="savePlatformBtn" class="setting-btn success">保存平台</button>
                        <button id="cancelParseBtn" class="setting-btn secondary">取消</button>
                    </div>
                </div>
            `;

            // 绑定按钮事件
            document.getElementById('savePlatformBtn')?.addEventListener('click', () => this.handlePlatformAction('save-platform'));
            document.getElementById('cancelParseBtn')?.addEventListener('click', () => this.cancelParse());
        }

        showParseError(result) {
            const parseResult = document.getElementById('parseResult');
            parseResult.innerHTML = `
                <div class="result-header">
                    <span class="result-status error"></span>
                    <span class="result-title">解析失败</span>
                </div>
                <div class="result-details">
                    <strong>错误:</strong> ${result.error}<br>
                    ${result.suggestion ? `<strong>建议:</strong> ${result.suggestion}` : ''}
                </div>
            `;
        }

        async savePlatform() {
            const urlInput = document.getElementById('urlInput');
            const platformName = document.getElementById('parsePlatformName');
            const platformAliases = document.getElementById('parsePlatformAliases');

            if (!urlInput || !platformName) return;

            const url = urlInput.value.trim();
            const name = platformName.value.trim();
            const aliases = platformAliases ? platformAliases.value.trim() : '';

            if (!name) {
                alert('请输入平台名称');
                return;
            }

            try {
                const platformManager = this.getPlatformManager();
                await platformManager.addPlatformFromURL(url, {
                    name: name,
                    aliases: aliases
                });

                alert('平台添加成功！');
                this.cancelParse();
                this.refreshPlatformList();
            } catch (error) {
                this.handleError(error, '保存平台');
            }
        }

        cancelParse() {
            const { urlInput, parseResult } = this.getParseElements();

            if (parseResult) parseResult.style.display = 'none';
            if (urlInput) urlInput.value = '';
        }

        async deletePlatform(id) {
            if (!id) return;

            if (!confirm('确定要删除这个平台吗？')) return;

            try {
                const platformManager = this.getPlatformManager();
                await platformManager.deletePlatform(id);
                alert('平台删除成功！');
                this.refreshPlatformList();
            } catch (error) {
                this.handleError(error, '删除平台');
            }
        }

        refreshPlatformList() {
            const platformList = document.getElementById('platformList');
            const platformStats = document.getElementById('platformStats');

            if (!platformList || !platformStats) return;

            try {
                const platformManager = this.getPlatformManager();

            // 更新统计信息
            const stats = platformManager.getStats();
            platformStats.innerHTML = `
                总计: ${stats.total} 个平台 |
                内置: ${stats.builtin} 个 |
                自定义: ${stats.custom}/${stats.maxCustom} 个
            `;

            // 获取当前过滤器
            const activeFilter = document.querySelector('.filter-btn.active')?.dataset.filter || 'all';

            // 获取平台列表
            let platforms = [];
            switch (activeFilter) {
                case 'builtin':
                    platforms = platformManager.getAllPlatforms({ builtinOnly: true });
                    break;
                case 'custom':
                    platforms = platformManager.getAllPlatforms({ customOnly: true });
                    break;
                default:
                    platforms = platformManager.getAllPlatforms();
            }

            // 渲染平台列表
            if (platforms.length === 0) {
                platformList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        ${activeFilter === 'custom' ? '还没有自定义平台' : '没有找到平台'}
                    </div>
                `;
                return;
            }

            platformList.innerHTML = platforms.map(platform => `
                <div class="platform-item ${platform.isBuiltin ? 'builtin' : ''}">
                    <div class="platform-icon">
                        ${platform.icon || '🔍'}
                    </div>
                    <div class="platform-info">
                        <div class="platform-name">${platform.name}</div>
                        <div class="platform-details">
                            搜索直达: ${platform.url || '内置搜索'}<br>
                            别名: ${platform.aliases ? platform.aliases.join(', ') : '无'}
                        </div>
                    </div>
                    <div class="platform-actions">
                        ${!platform.isBuiltin ? `
                            <button class="platform-action-btn delete" onclick="window.app.settingsManager.handlePlatformAction('delete-platform', {id: '${platform.id}'})">
                                删除
                            </button>
                        ` : `
                            <span style="color: #666; font-size: 12px;">内置平台</span>
                        `}
                    </div>
                </div>
            `).join('');
            } catch (error) {
                console.error('刷新平台列表失败:', error);
                if (platformList) {
                    platformList.innerHTML = '<div style="text-align: center; padding: 40px; color: #f44336;">加载平台列表失败</div>';
                }
            }
        }
    }

    // 页面管理器
    class PageManager {
        constructor() {
            this.pages = [];
            this.currentPageIndex = 0;
            this.maxPages = 16; // 增加最大页面数以支持双模式

            // DOM元素
            this.normalPageList = document.getElementById('normalPageList');
            this.quickPageList = document.getElementById('quickPageList');
            this.addNormalPageBtn = document.getElementById('addNormalPageBtn');
            this.addQuickPageBtn = document.getElementById('addQuickPageBtn');
            this.currentPageName = document.getElementById('currentPageName');
            this.pageCounter = document.getElementById('pageCounter');
            this.mainContent = document.getElementById('mainContent');

            this.init();
        }

        init() {
            this.loadPages();
            this.render();
            this.bindEvents();
            this.bindWheelEvents();
        }

        loadPages() {
            const saved = Storage.get('pages_v3');
            if (saved && Array.isArray(saved) && saved.length > 0) {
                // 迁移旧数据：为没有mode属性的页面添加默认mode
                this.pages = saved.map(page => ({
                    ...page,
                    mode: page.mode || 'normal' // 默认为常规搜索模式
                }));
            } else {
                // 创建默认页面（每种模式一个）
                this.pages = [
                    {
                        id: Utils.generateId(),
                        name: '常规搜索',
                        icon: '🏠',
                        mode: 'normal',
                        shortcuts: this.getDefaultShortcutsForMode('normal'),
                        order: 1
                    },
                    {
                        id: Utils.generateId(),
                        name: '快捷搜索',
                        icon: '⚡',
                        mode: 'quick',
                        shortcuts: this.getDefaultShortcutsForMode('quick'),
                        order: 2
                    }
                ];
                this.savePages();
            }
        }

        savePages() {
            Storage.set('pages_v3', this.pages);
        }

        // 获取模式特定的默认快捷方式
        getDefaultShortcutsForMode(mode) {
            const searchModeManager = window.app?.searchModeManager;
            const searchManager = window.app?.searchManager;

            if (!searchModeManager || !searchModeManager.modes[mode] || !searchManager) {
                return [...DEFAULT_SHORTCUTS];
            }

            const modeConfig = searchModeManager.modes[mode];
            const defaultPlatforms = modeConfig.defaultPlatforms || [];

            // 根据模式的默认平台创建快捷方式
            const modeShortcuts = defaultPlatforms
                .map(platformId => {
                    const platform = searchManager.platforms.find(p => p.id === platformId);
                    if (!platform || platform.id === 'all') return null;

                    return {
                        id: Utils.generateId(),
                        name: platform.name,
                        url: platform.url.replace('{query}', ''),
                        icon: platform.icon,
                        order: defaultPlatforms.indexOf(platformId)
                    };
                })
                .filter(shortcut => shortcut !== null);

            // 如果没有生成任何快捷方式，返回默认的
            if (modeShortcuts.length === 0) {
                return [...DEFAULT_SHORTCUTS];
            }

            return modeShortcuts;
        }

        // 根据模式获取页面
        getPagesByMode(mode) {
            return this.pages.filter(page => page.mode === mode);
        }

        render() {
            this.renderPageList();
            this.renderCurrentPage();
            this.updatePageIndicator();
        }

        renderPageList() {
            this.renderNormalPages();
            this.renderQuickPages();
            this.bindPageEvents();
        }

        renderNormalPages() {
            this.renderPagesByMode('normal', this.normalPageList);
        }

        renderQuickPages() {
            this.renderPagesByMode('quick', this.quickPageList);
        }

        // 通用的页面渲染方法，避免重复代码
        renderPagesByMode(mode, container) {
            if (!container) return;

            const pages = this.pages.filter(page => page.mode === mode);
            container.innerHTML = pages
                .sort((a, b) => a.order - b.order)
                .map((page) => {
                    const pageIndex = this.pages.findIndex(p => p.id === page.id);
                    return `
                        <div class="page-item ${pageIndex === this.currentPageIndex ? 'active' : ''}"
                             data-page-id="${page.id}"
                             data-page-index="${pageIndex}"
                             title="${page.name}">
                            ${page.icon}
                        </div>
                    `;
                }).join('');
        }

        renderCurrentPage() {
            const currentPage = this.pages[this.currentPageIndex];
            if (!currentPage || !window.app?.shortcutManager) return;

            // 更新快捷方式管理器的数据
            window.app.shortcutManager.shortcuts = currentPage.shortcuts || [];
            window.app.shortcutManager.render();
        }

        updatePageIndicator() {
            const currentPage = this.pages[this.currentPageIndex];
            if (this.currentPageName && currentPage) {
                this.currentPageName.textContent = currentPage.name;
            }
            if (this.pageCounter) {
                this.pageCounter.textContent = `${this.currentPageIndex + 1} / ${this.pages.length}`;
            }
        }

        bindEvents() {
            // 添加常规搜索页面按钮
            if (this.addNormalPageBtn) {
                this.addNormalPageBtn.addEventListener('click', () => this.showAddPageModal('normal'));
            }

            // 添加快捷搜索页面按钮
            if (this.addQuickPageBtn) {
                this.addQuickPageBtn.addEventListener('click', () => this.showAddPageModal('quick'));
            }

            // 页面模态框事件
            this.bindPageModalEvents();
        }

        bindPageEvents() {
            // 绑定常规搜索页面事件
            const normalPageItems = this.normalPageList?.querySelectorAll('.page-item');
            if (normalPageItems) {
                normalPageItems.forEach(item => {
                    this.bindSinglePageEvents(item);
                });
            }

            // 绑定快捷搜索页面事件
            const quickPageItems = this.quickPageList?.querySelectorAll('.page-item');
            if (quickPageItems) {
                quickPageItems.forEach(item => {
                    this.bindSinglePageEvents(item);
                });
            }
        }

        bindSinglePageEvents(item) {
            // 点击切换页面
            item.addEventListener('click', (e) => {
                const pageIndex = parseInt(item.dataset.pageIndex);
                this.switchToPage(pageIndex);
            });

            // 右键菜单
            item.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                const pageId = item.dataset.pageId;
                this.showPageContextMenu(e, pageId);
            });
        }

        bindWheelEvents() {
            // 鼠标滚轮切换页面
            if (this.mainContent) {
                this.mainContent.addEventListener('wheel', (e) => {
                    e.preventDefault();

                    if (e.deltaY > 0) {
                        // 向下滚动，切换到下一页
                        this.switchToNextPage();
                    } else {
                        // 向上滚动，切换到上一页
                        this.switchToPrevPage();
                    }
                }, { passive: false });
            }
        }

        bindPageModalEvents() {
            const modal = document.getElementById('pageModalOverlay');
            const closeBtn = document.getElementById('closePageModal');
            const cancelBtn = document.getElementById('cancelPageBtn');
            const confirmBtn = document.getElementById('confirmPageBtn');

            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.hidePageModal());
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => this.hidePageModal());
            }

            if (confirmBtn) {
                confirmBtn.addEventListener('click', () => this.handlePageModalConfirm());
            }

            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.hidePageModal();
                    }
                });
            }

            // 图标选择器事件
            this.bindIconSelectorEvents();

            // 页面右键菜单事件
            this.bindPageContextMenuEvents();
        }

        bindIconSelectorEvents() {
            // 使用事件委托避免重复绑定事件监听器
            const iconGrid = document.getElementById('iconGrid');
            const iconInput = document.getElementById('pageIcon');

            if (!iconGrid || iconGrid.dataset.eventsBound) return;

            iconGrid.addEventListener('click', (e) => {
                const option = e.target.closest('.icon-option');
                if (!option) return;

                e.stopPropagation();
                const selectedIconValue = option.dataset.icon;

                // 更新隐藏输入框的值
                if (iconInput) iconInput.value = selectedIconValue;

                // 更新选中状态
                const iconOptions = iconGrid.querySelectorAll('.icon-option');
                iconOptions.forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
            });

            // 标记事件已绑定，避免重复绑定
            iconGrid.dataset.eventsBound = 'true';
        }

        updateIconSelection(iconValue) {
            const iconOptions = document.querySelectorAll('.icon-option');
            iconOptions.forEach(option => {
                if (option.dataset.icon === iconValue) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });
        }

        bindPageContextMenuEvents() {
            const contextMenu = document.getElementById('pageContextMenu');
            const editItem = document.getElementById('editPage');
            const deleteItem = document.getElementById('deletePage');

            if (editItem) {
                editItem.addEventListener('click', () => {
                    this.editPage(this.contextMenuTargetId);
                    this.hidePageContextMenu();
                });
            }

            if (deleteItem) {
                deleteItem.addEventListener('click', () => {
                    this.deletePage(this.contextMenuTargetId);
                    this.hidePageContextMenu();
                });
            }

            // 使用全局事件管理器避免重复监听器
            this.pageGlobalClickHandler = () => {
                this.hidePageContextMenu();
            };
            GlobalEventManager.addClickHandler(this.pageGlobalClickHandler);
        }

        switchToPage(pageIndexOrId) {
            let pageIndex;

            // 支持通过页面ID或索引切换
            if (typeof pageIndexOrId === 'string') {
                // 通过页面ID查找索引
                pageIndex = this.pages.findIndex(page => page.id === pageIndexOrId);
                if (pageIndex === -1) return;
            } else {
                // 直接使用索引
                pageIndex = pageIndexOrId;
                if (pageIndex < 0 || pageIndex >= this.pages.length) return;
            }

            // 保存当前页面的快捷方式
            this.saveCurrentPageShortcuts();

            // 切换到新页面
            this.currentPageIndex = pageIndex;
            this.render();

            // 添加切换动画
            this.addSwitchAnimation();
        }

        switchToNextPage() {
            const nextIndex = (this.currentPageIndex + 1) % this.pages.length;
            this.switchToPage(nextIndex);
        }

        switchToPrevPage() {
            const prevIndex = (this.currentPageIndex - 1 + this.pages.length) % this.pages.length;
            this.switchToPage(prevIndex);
        }

        saveCurrentPageShortcuts() {
            if (window.app?.shortcutManager && this.pages[this.currentPageIndex]) {
                this.pages[this.currentPageIndex].shortcuts = [...window.app.shortcutManager.shortcuts];
                this.savePages();
            }
        }

        addSwitchAnimation() {
            const shortcutsGrid = document.getElementById('shortcutsGrid');
            if (shortcutsGrid) {
                shortcutsGrid.style.opacity = '0';
                shortcutsGrid.style.transform = 'translateX(-50%) translateY(10px)';

                setTimeout(() => {
                    shortcutsGrid.style.opacity = '1';
                    shortcutsGrid.style.transform = 'translateX(-50%) translateY(0)';
                }, 150);
            }
        }

        showAddPageModal(mode = 'normal') {
            const modeNames = {
                'normal': '常规搜索页面',
                'quick': '快捷搜索页面'
            };
            this.showPageModal(`添加${modeNames[mode]}`, null, mode);
        }

        showPageModal(title = '添加页面', page = null, mode = 'normal') {
            const modal = document.getElementById('pageModalOverlay');
            const modalTitle = document.getElementById('pageModalTitle');
            const nameInput = document.getElementById('pageName');
            const iconInput = document.getElementById('pageIcon');

            if (!modal) return;

            if (modalTitle) modalTitle.textContent = title;

            // 设置表单数据
            const iconValue = page ? page.icon : (mode === 'quick' ? '⚡' : '📄');
            const nameValue = page ? page.name : '';

            if (nameInput) nameInput.value = nameValue;
            if (iconInput) iconInput.value = iconValue;

            // 更新图标选中状态（统一处理）
            this.updateIconSelection(iconValue);

            this.currentEditingPageId = page ? page.id : null;
            this.currentEditingPageMode = page ? page.mode : mode; // 保存当前编辑的页面模式

            modal.style.display = 'flex';

            // 聚焦到名称输入框
            setTimeout(() => {
                if (nameInput) nameInput.focus();
            }, 100);
        }

        hidePageModal() {
            const modal = document.getElementById('pageModalOverlay');

            if (modal) {
                modal.style.display = 'none';
            }

            this.currentEditingPageId = null;
        }

        handlePageModalConfirm() {
            const nameInput = document.getElementById('pageName');
            const iconInput = document.getElementById('pageIcon');

            const name = nameInput?.value.trim();
            const icon = iconInput?.value.trim();

            if (!name || !icon) {
                alert('请填写完整信息');
                return;
            }

            if (this.currentEditingPageId) {
                // 编辑模式
                this.updatePage(this.currentEditingPageId, { name, icon });
            } else {
                // 添加模式
                this.addPage(name, icon, this.currentEditingPageMode || 'normal');
            }

            this.hidePageModal();
        }

        addPage(name, icon, mode = 'normal') {
            if (this.pages.length >= this.maxPages) {
                alert(`最多只能创建 ${this.maxPages} 个页面`);
                return false;
            }

            const newPage = {
                id: Utils.generateId(),
                name,
                icon,
                mode, // 添加模式信息
                shortcuts: [],
                order: this.pages.length + 1
            };

            this.pages.push(newPage);
            this.savePages();
            this.render();
            return true;
        }

        updatePage(id, data) {
            const index = this.pages.findIndex(p => p.id === id);
            if (index !== -1) {
                this.pages[index] = { ...this.pages[index], ...data };
                this.savePages();
                this.render();
            }
        }

        deletePage(id) {
            if (this.pages.length <= 1) {
                alert('至少需要保留一个页面');
                return;
            }

            if (confirm('确定要删除这个页面吗？页面中的所有快捷方式也会被删除。')) {
                const index = this.pages.findIndex(p => p.id === id);
                if (index !== -1) {
                    this.pages.splice(index, 1);

                    // 调整当前页面索引
                    if (this.currentPageIndex >= this.pages.length) {
                        this.currentPageIndex = this.pages.length - 1;
                    }

                    this.savePages();
                    this.render();
                }
            }
        }

        editPage(id) {
            const page = this.pages.find(p => p.id === id);
            if (page) {
                this.showPageModal('编辑页面', page);
            }
        }

        showPageContextMenu(event, pageId) {
            const contextMenu = document.getElementById('pageContextMenu');
            if (!contextMenu) return;

            this.contextMenuTargetId = pageId;

            contextMenu.style.display = 'block';
            contextMenu.style.left = event.pageX + 'px';
            contextMenu.style.top = event.pageY + 'px';
        }

        hidePageContextMenu() {
            const contextMenu = document.getElementById('pageContextMenu');
            if (contextMenu) {
                contextMenu.style.display = 'none';
            }
        }

        // 获取当前页面
        getCurrentPage() {
            return this.pages[this.currentPageIndex];
        }

        // 导出页面数据
        exportPages() {
            return {
                pages: this.pages,
                currentPageIndex: this.currentPageIndex
            };
        }

        // 导入页面数据
        importPages(data) {
            if (data.pages && Array.isArray(data.pages)) {
                this.pages = data.pages;
                this.currentPageIndex = data.currentPageIndex || 0;

                // 确保索引有效
                if (this.currentPageIndex >= this.pages.length) {
                    this.currentPageIndex = 0;
                }

                this.savePages();
                this.render();
                return true;
            }
            return false;
        }
    }

    // IndexedDB壁纸存储类
    class WallpaperDB {
        constructor() {
            this.dbName = 'MomentSearchWallpapers';
            this.version = 1;
            this.storeName = 'wallpapers';
        }

        async openDatabase() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(this.dbName, this.version);

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(this.storeName)) {
                        db.createObjectStore(this.storeName);
                    }
                };

                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        }

        async saveWallpaper(imageBlob) {
            const db = await this.openDatabase();
            return new Promise((resolve, reject) => {
                const transaction = db.transaction(this.storeName, "readwrite");
                const store = transaction.objectStore(this.storeName);

                store.put(imageBlob, "backgroundImage");
                store.put(new Date().toISOString(), "timestamp");

                transaction.oncomplete = () => resolve();
                transaction.onerror = () => reject(transaction.error);
            });
        }

        async loadWallpaper() {
            const db = await this.openDatabase();
            return new Promise((resolve) => {
                const transaction = db.transaction(this.storeName, "readonly");
                const store = transaction.objectStore(this.storeName);
                const request = store.get("backgroundImage");

                request.onsuccess = () => resolve(request.result);
                request.onerror = () => resolve(null);
            });
        }

        async clearWallpaper() {
            const db = await this.openDatabase();
            return new Promise((resolve, reject) => {
                const transaction = db.transaction(this.storeName, "readwrite");
                const store = transaction.objectStore(this.storeName);

                store.delete("backgroundImage");
                store.delete("timestamp");

                transaction.oncomplete = () => resolve();
                transaction.onerror = () => reject(transaction.error);
            });
        }
    }

    // 背景管理器
    class BackgroundManager {
        constructor() {
            this.backgroundLayer = document.getElementById('backgroundLayer');
            this.wallpaperDB = new WallpaperDB(); // 添加IndexedDB支持
        }

        async init() {
            await this.applyBackground();
            this.bindEvents();
        }

        bindEvents() {
            // 壁纸功能通过设置面板调用，无需额外事件绑定
        }

        async applyBackground() {
            const settings = Storage.get('settings_v3', {});
            const wallpaperType = settings.appearance?.wallpaper || 'default';

            if (wallpaperType === 'default') {
                this.applyDefaultBackground();
            } else if (wallpaperType === 'custom') {
                await this.loadCustomWallpaper();
            } else if (wallpaperType.startsWith('data:') || wallpaperType.startsWith('http')) {
                // 兼容旧版本：直接使用data URL并迁移
                this.applyLegacyWallpaper(wallpaperType);
                await this.migrateOldWallpaper(wallpaperType);
            }
        }

        applyDefaultBackground() {
            if (this.backgroundLayer) {
                this.backgroundLayer.style.setProperty('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 'important');
                this.backgroundLayer.style.setProperty('background-image', 'none', 'important');
            }
        }

        applyLegacyWallpaper(wallpaperType) {
            if (this.backgroundLayer) {
                this.backgroundLayer.style.backgroundImage = `url(${wallpaperType})`;
                this.backgroundLayer.style.background = '';
            }
        }

        async migrateOldWallpaper(dataUrl) {
            await this.handleAsyncOperation(async () => {
                const response = await fetch(dataUrl);
                const blob = await response.blob();
                await this.wallpaperDB.saveWallpaper(blob);
                this.updateWallpaperSetting('custom');
            }, '壁纸迁移失败');
        }

        updateWallpaperSetting(wallpaperType) {
            const settings = Storage.get('settings_v3', {});
            settings.appearance = settings.appearance || {};
            settings.appearance.wallpaper = wallpaperType;
            Storage.set('settings_v3', settings);
        }

        showWallpaperOptions() {
            // 防止重复调用
            if (this.isSelectingWallpaper) {
                return;
            }
            this.isSelectingWallpaper = true;

            try {
                const input = this.createFileInput();
                this.setupFileInputEvents(input);
                this.triggerFileSelection(input);
            } catch (error) {
                console.error('创建文件选择器失败:', error);
                this.isSelectingWallpaper = false;
                this.showError('无法打开文件选择器，请重试');
            }
        }

        createFileInput() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = false;

            // 设置样式确保不可见但可交互
            input.style.position = 'absolute';
            input.style.left = '-9999px';
            input.style.opacity = '0';
            input.style.pointerEvents = 'none';

            return input;
        }

        setupFileInputEvents(input) {
            const cleanup = () => {
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            };

            const handleChange = (e) => {
                this.isSelectingWallpaper = false;
                const file = e.target.files[0];
                if (file) {
                    this.handleWallpaperUpload(file);
                }
                cleanup();
            };

            const handleCancel = () => {
                this.isSelectingWallpaper = false;
                cleanup();
            };

            input.addEventListener('change', handleChange);
            input.addEventListener('cancel', handleCancel);

            // 安全清理机制
            setTimeout(() => {
                if (this.isSelectingWallpaper) {
                    this.isSelectingWallpaper = false;
                    cleanup();
                }
            }, 30000);
        }

        triggerFileSelection(input) {
            document.body.appendChild(input);
            input.click();
        }

        async handleWallpaperUpload(file) {
            try {
                // 检查文件大小限制
                const maxSize = 50 * 1024 * 1024; // 50MB
                if (file.size > maxSize) {
                    this.showError('图片文件过大，请选择小于50MB的图片');
                    return;
                }

                // 保存到IndexedDB
                await this.wallpaperDB.saveWallpaper(file);

                // 应用壁纸
                const imageUrl = URL.createObjectURL(file);
                this.applyWallpaper(imageUrl);

                // 更新设置
                this.updateWallpaperSetting('custom');

            } catch (error) {
                console.error('保存壁纸失败:', error);
                this.showError('壁纸保存失败，请重试');
            }
        }

        showError(message) {
            // 统一的错误提示方法
            alert(message);
        }

        async handleAsyncOperation(operation, errorMessage) {
            try {
                return await operation();
            } catch (error) {
                console.error(errorMessage, error);
                this.showError(errorMessage);
                return null;
            }
        }

        async loadCustomWallpaper() {
            return await this.handleAsyncOperation(async () => {
                const imageBlob = await this.wallpaperDB.loadWallpaper();
                if (imageBlob) {
                    const imageUrl = URL.createObjectURL(imageBlob);
                    this.applyWallpaper(imageUrl);
                    return imageUrl;
                }
                return null;
            }, '加载壁纸失败');
        }

        applyWallpaper(imageUrl) {
            if (!this.backgroundLayer) {
                console.error('backgroundLayer不存在');
                return;
            }

            // 清除默认背景并设置自定义壁纸
            this.backgroundLayer.style.background = 'none';
            this.backgroundLayer.style.setProperty('background-image', `url("${imageUrl}")`, 'important');
            this.backgroundLayer.style.setProperty('background-size', 'cover', 'important');
            this.backgroundLayer.style.setProperty('background-position', 'center', 'important');
            this.backgroundLayer.style.setProperty('background-repeat', 'no-repeat', 'important');

            // 验证设置是否成功，如果失败则使用备用方案
            const computedStyle = window.getComputedStyle(this.backgroundLayer);
            if (!computedStyle.backgroundImage || computedStyle.backgroundImage === 'none') {
                this.applyWallpaperFallback(imageUrl);
            }
        }

        applyWallpaperFallback(imageUrl) {
            // 备用方案：直接替换整个style属性
            this.backgroundLayer.setAttribute('style',
                `position: fixed !important; ` +
                `top: 0 !important; ` +
                `left: 0 !important; ` +
                `width: 100% !important; ` +
                `height: 100% !important; ` +
                `background-image: url("${imageUrl}") !important; ` +
                `background-size: cover !important; ` +
                `background-position: center !important; ` +
                `background-repeat: no-repeat !important; ` +
                `filter: blur(1px) !important; ` +
                `z-index: -1 !important;`
            );
        }

        async resetToDefault() {
            await this.handleAsyncOperation(async () => {
                await this.wallpaperDB.clearWallpaper();
                this.updateWallpaperSetting('default');
                await this.applyBackground();
            }, '重置壁纸失败，请重试');
        }
    }

    // 暴露到全局作用域供调试和测试使用
    window.MomentSearch = {
        PLATFORMS,
        ALIAS_MAP,
        Utils,
        Storage,
        URLParser,
        PlatformManager
    };

})();
