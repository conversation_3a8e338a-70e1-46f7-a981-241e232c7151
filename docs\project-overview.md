# Moment Search V3 - 项目概览

> 基于iTab设计理念的现代化Chrome新标签页扩展

## 🚀 项目简介

**Moment Search V3** 是一个功能丰富、设计精美的Chrome浏览器扩展，旨在替换默认的新标签页，为用户提供更加智能和个性化的浏览体验。

### 核心特性

- **🔍 智能搜索系统** - 支持多平台搜索和别名快捷搜索
- **⚡ 快捷方式管理** - 多页面快捷方式，支持拖拽排序
- **⏰ 时间显示** - 可自定义的时间和日期显示
- **🎨 个性化主题** - 多种主题和背景选择
- **🛠️ 自定义平台** - 智能URL解析，添加任意搜索网站
- **📊 数据管理** - 完整的导入导出功能

## 🏗️ 技术架构

### 技术栈选择

| 技术 | 版本 | 用途 |
|------|------|------|
| HTML5 | - | 语义化页面结构 |
| CSS3 | - | 现代样式和动画 |
| JavaScript | ES6+ | 核心业务逻辑 |
| Chrome Extension API | Manifest V3 | 浏览器扩展功能 |

### 架构优势

- **🚀 性能优异** - 无框架开销，加载速度快
- **📦 体积小巧** - 扩展包大小控制在2MB以内
- **🔧 易于维护** - 模块化设计，代码结构清晰
- **🌐 兼容性好** - 支持所有现代Chromium内核浏览器

## 📁 项目结构

```
moment_web/
├── moment-search-v3/          # V3主要代码
│   ├── index.html            # 主页面入口
│   ├── script.js             # 核心业务逻辑 (5000+ 行)
│   ├── style.css             # 样式系统 (2800+ 行)
│   ├── manifest.json         # 扩展配置文件
│   ├── background.js         # 后台服务脚本
│   ├── INSTALL.md           # 安装指南
│   └── js/                  # 辅助模块
│       ├── DataStorageManager.js    # 数据存储管理
│       └── SearchPlatformConfig.js  # 搜索平台配置
├── docs/                     # 项目文档
│   ├── project-overview.md          # 项目概览 (本文档)
│   ├── architecture-design.md      # 架构设计文档
│   ├── api-reference.md            # API参考文档
│   ├── development-guide.md        # 开发指南
│   ├── deployment-guide.md         # 部署指南
│   ├── v3_refactor_plan.md         # V3重构方案
│   ├── v3_technical_specs.md       # 技术规格
│   ├── v3_implementation_guide.md  # 实施指南
│   └── plugin_system_design.md     # 插件系统设计
└── README.md                 # 项目说明
```

## 🎯 核心功能模块

### 1. 搜索系统
- **多平台支持**: Google、Bing、百度等主流搜索引擎
- **智能别名**: 支持 "g 关键词" 快速搜索
- **搜索历史**: 自动保存和管理搜索记录
- **平台管理**: 支持自定义搜索平台

### 2. 快捷方式系统
- **多页面组织**: 支持创建多个快捷方式页面
- **拖拽排序**: 直观的拖拽操作重新排列
- **图标管理**: 自动获取网站图标，支持自定义
- **批量操作**: 导入导出快捷方式配置

### 3. 个性化设置
- **主题切换**: 浅色、深色、自动主题
- **壁纸系统**: 支持自定义背景图片
- **布局调整**: 灵活的界面布局配置
- **时间显示**: 可自定义的时间格式

### 4. 数据管理
- **本地存储**: 使用Chrome Storage API安全存储
- **数据备份**: 完整的导入导出功能
- **版本迁移**: 自动处理数据格式升级
- **隐私保护**: 完全本地化，不收集用户数据

## 🌟 设计理念

### 用户体验优先
- **简洁美观**: 参考iTab的设计风格，界面简洁大方
- **响应迅速**: 优化加载性能，确保流畅体验
- **直观操作**: 符合用户习惯的交互设计
- **个性化**: 丰富的自定义选项满足不同需求

### 技术实现原则
- **模块化设计**: 清晰的职责分离，便于维护和扩展
- **性能优化**: 延迟加载、缓存管理、内存优化
- **错误处理**: 完善的异常捕获和恢复机制
- **安全考虑**: 严格的CSP策略和权限控制

## 📊 性能指标

### 加载性能
- **首屏加载时间**: < 300ms
- **搜索响应时间**: < 100ms
- **快捷方式点击响应**: < 50ms

### 资源占用
- **扩展包大小**: < 2MB
- **内存占用**: < 30MB
- **CPU占用**: < 5%

### 用户体验
- **界面流畅度**: 60fps
- **动画延迟**: < 16ms
- **操作响应**: 即时反馈

## 🔒 安全与隐私

### 数据安全
- **本地存储**: 所有数据存储在用户本地
- **加密保护**: 敏感信息采用加密存储
- **权限最小化**: 只申请必要的浏览器权限

### 隐私保护
- **无数据收集**: 不收集任何用户搜索数据
- **无网络请求**: 除搜索外无其他网络活动
- **开源透明**: 代码完全开源，可审查

## 🚀 快速开始

### 系统要求
- **浏览器**: Chrome 88+ 或 Edge 88+
- **系统**: Windows 10+, macOS 10.14+, Linux Ubuntu 18.04+
- **权限**: storage, tabs, favicon

### 安装步骤
1. 下载项目文件到本地
2. 打开Chrome扩展管理页面 (`chrome://extensions/`)
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `moment-search-v3` 文件夹

### 验证安装
- 打开新标签页，应该看到Moment Search界面
- 测试搜索功能和快捷方式点击
- 检查设置面板是否正常工作

## 📚 文档导航

- **[架构设计](./architecture-design.md)** - 详细的系统架构和模块设计
- **[API参考](./api-reference.md)** - 完整的API接口文档
- **[开发指南](./development-guide.md)** - 开发环境搭建和编码规范
- **[部署指南](./deployment-guide.md)** - 构建、测试和发布流程

## 🤝 贡献指南

欢迎参与项目开发！请查看 [开发指南](./development-guide.md) 了解详细的贡献流程。

### 开发流程
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

### 代码规范
- 遵循 ES6+ 语法标准
- 使用有意义的变量和函数命名
- 添加必要的注释和文档
- 确保代码通过测试

---

**文档版本**: V1.0  
**创建时间**: 2025-01-22  
**维护团队**: Moment Search Development Team
