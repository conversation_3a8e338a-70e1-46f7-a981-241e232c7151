/**
 * 预览窗口组件
 * 负责创建和管理单个预览窗口
 */
class PreviewWindow {
    constructor(url, options = {}) {
        this.url = url;
        this.id = options.id || this.generateId();
        this.options = {
            width: 800,
            height: 600,
            position: 'center',
            resizable: true,
            draggable: true,
            ...options
        };
        
        // 窗口状态
        this.state = 'initializing'; // initializing, loading, loaded, error, minimized
        this.element = null;
        this.iframe = null;
        this.loadingElement = null;
        this.headerElement = null;
        this.resizeHandle = null;
        
        // 事件监听器
        this.eventListeners = new Map();
        this.boundEventHandlers = new Map();
        
        // 窗口属性
        this.zIndex = options.zIndex || 10000;
        this.isFocused = false;
        this.isMinimized = false;
        this.isFullscreen = false;
        
        // 拖拽和调整大小状态
        this.dragState = {
            isDragging: false,
            startX: 0,
            startY: 0,
            offsetX: 0,
            offsetY: 0
        };
        
        this.resizeState = {
            isResizing: false,
            startX: 0,
            startY: 0,
            startWidth: 0,
            startHeight: 0
        };
    }

    /**
     * 渲染预览窗口
     */
    async render() {
        try {
            this.state = 'loading';
            
            // 创建DOM元素
            this.createElement();
            
            // 应用样式和位置
            this.applyStyles();
            this.calculatePosition();
            
            // 添加到DOM
            document.body.appendChild(this.element);
            
            // 绑定事件
            this.bindEvents();
            
            // 显示动画
            this.showWithAnimation();
            
            // 加载内容
            await this.loadContent();
            
            this.state = 'loaded';
            this.emit('rendered');
            
            console.log(`✅ 预览窗口已渲染: ${this.getDisplayUrl()}`);
            
        } catch (error) {
            this.state = 'error';
            this.showError(error.message);
            this.emit('error', error);
            console.error('预览窗口渲染失败:', error);
        }
    }

    /**
     * 创建DOM元素
     */
    createElement() {
        this.element = document.createElement('div');
        this.element.className = 'link-preview-window entering';
        this.element.dataset.windowId = this.id;
        this.element.tabIndex = -1; // 使窗口可以获得焦点
        
        this.element.innerHTML = `
            <div class="preview-status loading"></div>
            <div class="preview-header">
                <div class="preview-title">
                    <span class="preview-url" title="${this.url}">${this.getDisplayUrl()}</span>
                </div>
                <div class="preview-controls">
                    <button class="preview-btn reading-mode-btn" title="阅读模式" style="display: none;">
                        📖
                    </button>
                    <button class="preview-btn minimize-btn" title="最小化">
                        ➖
                    </button>
                    <button class="preview-btn close-btn" title="关闭">
                        ✕
                    </button>
                </div>
            </div>
            <div class="preview-content">
                <div class="preview-loading">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在加载 ${this.getDisplayUrl()}...</div>
                </div>
                <iframe class="preview-iframe" 
                        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
                        loading="lazy">
                </iframe>
            </div>
            <div class="preview-resize-handle" title="拖拽调整大小"></div>
        `;

        // 获取关键元素引用
        this.iframe = this.element.querySelector('.preview-iframe');
        this.loadingElement = this.element.querySelector('.preview-loading');
        this.headerElement = this.element.querySelector('.preview-header');
        this.resizeHandle = this.element.querySelector('.preview-resize-handle');
        this.statusElement = this.element.querySelector('.preview-status');
        this.readingModeBtn = this.element.querySelector('.reading-mode-btn');
    }

    /**
     * 应用样式
     */
    applyStyles() {
        const { width, height } = this.options;
        
        Object.assign(this.element.style, {
            width: `${width}px`,
            height: `${height}px`,
            zIndex: this.zIndex,
            opacity: '0',
            transform: 'scale(0.9) translateY(-20px)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        });
    }

    /**
     * 计算窗口位置
     */
    calculatePosition() {
        const position = this.options.position;
        const rect = this.element.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        let left, top;
        
        switch (position) {
            case 'left':
                left = 50;
                top = (viewportHeight - rect.height) / 2;
                break;
            case 'right':
                left = viewportWidth - rect.width - 50;
                top = (viewportHeight - rect.height) / 2;
                break;
            case 'mouse':
                if (this.options.triggerEvent) {
                    left = this.options.triggerEvent.clientX + 10;
                    top = this.options.triggerEvent.clientY + 10;
                } else {
                    left = (viewportWidth - rect.width) / 2;
                    top = (viewportHeight - rect.height) / 2;
                }
                break;
            default: // center
                left = (viewportWidth - rect.width) / 2;
                top = (viewportHeight - rect.height) / 2;
        }
        
        // 确保窗口在视口内
        left = Math.max(10, Math.min(left, viewportWidth - rect.width - 10));
        top = Math.max(10, Math.min(top, viewportHeight - rect.height - 10));
        
        this.element.style.left = `${left}px`;
        this.element.style.top = `${top}px`;
    }

    /**
     * 加载内容
     */
    async loadContent() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.showError('加载超时');
                reject(new Error('加载超时'));
            }, 15000); // 15秒超时

            this.iframe.onload = () => {
                clearTimeout(timeout);
                this.hideLoading();
                this.updateStatus('loaded');
                this.emit('loaded');
                
                // 检查是否可以启用阅读模式
                this.checkReadingModeAvailability();
                
                resolve();
            };

            this.iframe.onerror = () => {
                clearTimeout(timeout);
                this.showError('页面加载失败');
                reject(new Error('iframe加载失败'));
            };

            // 设置iframe源
            this.iframe.src = this.url;
        });
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 控制按钮事件
        this.bindControlEvents();
        
        // 拖拽事件
        if (this.options.draggable) {
            this.bindDragEvents();
        }
        
        // 调整大小事件
        if (this.options.resizable) {
            this.bindResizeEvents();
        }
        
        // 焦点事件
        this.bindFocusEvents();
        
        // 键盘事件
        this.bindKeyboardEvents();
    }

    /**
     * 绑定控制按钮事件
     */
    bindControlEvents() {
        // 关闭按钮
        const closeBtn = this.element.querySelector('.close-btn');
        const closeHandler = () => this.close();
        closeBtn.addEventListener('click', closeHandler);
        this.boundEventHandlers.set('close', closeHandler);

        // 最小化按钮
        const minimizeBtn = this.element.querySelector('.minimize-btn');
        const minimizeHandler = () => this.toggleMinimize();
        minimizeBtn.addEventListener('click', minimizeHandler);
        this.boundEventHandlers.set('minimize', minimizeHandler);

        // 阅读模式按钮
        const readingModeHandler = () => this.toggleReadingMode();
        this.readingModeBtn.addEventListener('click', readingModeHandler);
        this.boundEventHandlers.set('readingMode', readingModeHandler);
    }

    /**
     * 绑定拖拽事件
     */
    bindDragEvents() {
        const mouseDownHandler = (e) => {
            if (e.target.closest('.preview-controls')) return;
            
            this.startDrag(e);
        };

        const mouseMoveHandler = (e) => {
            if (this.dragState.isDragging) {
                this.drag(e);
            }
        };

        const mouseUpHandler = () => {
            if (this.dragState.isDragging) {
                this.endDrag();
            }
        };

        this.headerElement.addEventListener('mousedown', mouseDownHandler);
        document.addEventListener('mousemove', mouseMoveHandler);
        document.addEventListener('mouseup', mouseUpHandler);
        
        this.boundEventHandlers.set('dragStart', mouseDownHandler);
        this.boundEventHandlers.set('dragMove', mouseMoveHandler);
        this.boundEventHandlers.set('dragEnd', mouseUpHandler);
    }

    /**
     * 开始拖拽
     */
    startDrag(e) {
        this.dragState.isDragging = true;
        const rect = this.element.getBoundingClientRect();
        this.dragState.offsetX = e.clientX - rect.left;
        this.dragState.offsetY = e.clientY - rect.top;
        
        this.element.classList.add('dragging');
        this.focus();
        
        e.preventDefault();
    }

    /**
     * 拖拽中
     */
    drag(e) {
        const left = e.clientX - this.dragState.offsetX;
        const top = e.clientY - this.dragState.offsetY;
        
        // 限制在视口内
        const maxLeft = window.innerWidth - this.element.offsetWidth;
        const maxTop = window.innerHeight - this.element.offsetHeight;
        
        this.element.style.left = `${Math.max(0, Math.min(left, maxLeft))}px`;
        this.element.style.top = `${Math.max(0, Math.min(top, maxTop))}px`;
    }

    /**
     * 结束拖拽
     */
    endDrag() {
        this.dragState.isDragging = false;
        this.element.classList.remove('dragging');
    }

    /**
     * 显示动画
     */
    showWithAnimation() {
        requestAnimationFrame(() => {
            this.element.classList.remove('entering');
            this.element.classList.add('entered');
            this.element.style.opacity = '1';
            this.element.style.transform = 'scale(1) translateY(0)';
        });
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        if (this.loadingElement) {
            this.loadingElement.style.display = 'none';
        }
    }

    /**
     * 显示错误
     */
    showError(message) {
        this.updateStatus('error');
        this.element.querySelector('.preview-content').innerHTML = `
            <div class="preview-error">
                <div class="error-icon">⚠️</div>
                <div class="error-message">${message}</div>
                <button class="retry-btn">重试</button>
            </div>
        `;
        
        // 绑定重试按钮
        const retryBtn = this.element.querySelector('.retry-btn');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.reload();
            });
        }
    }

    /**
     * 更新状态指示器
     */
    updateStatus(status) {
        if (this.statusElement) {
            this.statusElement.className = `preview-status ${status}`;
        }
    }

    /**
     * 关闭窗口
     */
    close() {
        this.element.classList.add('exiting');
        this.element.style.opacity = '0';
        this.element.style.transform = 'scale(0.9) translateY(-20px)';
        
        setTimeout(() => {
            this.destroy();
        }, 300);
        
        this.emit('close');
    }

    /**
     * 销毁窗口
     */
    destroy() {
        // 清理事件监听器
        this.unbindAllEvents();
        
        // 移除DOM元素
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        // 清理引用
        this.element = null;
        this.iframe = null;
        this.eventListeners.clear();
        
        this.emit('destroyed');
        console.log(`🗑️ 预览窗口已销毁: ${this.getDisplayUrl()}`);
    }

    /**
     * 获取显示URL
     */
    getDisplayUrl() {
        try {
            const url = new URL(this.url);
            return url.hostname + (url.pathname !== '/' ? url.pathname : '');
        } catch {
            return this.url.length > 50 ? this.url.substring(0, 50) + '...' : this.url;
        }
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 绑定调整大小事件
     */
    bindResizeEvents() {
        const mouseDownHandler = (e) => {
            this.startResize(e);
        };

        const mouseMoveHandler = (e) => {
            if (this.resizeState.isResizing) {
                this.resize(e);
            }
        };

        const mouseUpHandler = () => {
            if (this.resizeState.isResizing) {
                this.endResize();
            }
        };

        this.resizeHandle.addEventListener('mousedown', mouseDownHandler);
        document.addEventListener('mousemove', mouseMoveHandler);
        document.addEventListener('mouseup', mouseUpHandler);

        this.boundEventHandlers.set('resizeStart', mouseDownHandler);
        this.boundEventHandlers.set('resizeMove', mouseMoveHandler);
        this.boundEventHandlers.set('resizeEnd', mouseUpHandler);
    }

    /**
     * 开始调整大小
     */
    startResize(e) {
        this.resizeState.isResizing = true;
        const rect = this.element.getBoundingClientRect();
        this.resizeState.startX = e.clientX;
        this.resizeState.startY = e.clientY;
        this.resizeState.startWidth = rect.width;
        this.resizeState.startHeight = rect.height;

        this.element.classList.add('resizing');
        this.focus();

        e.preventDefault();
        e.stopPropagation();
    }

    /**
     * 调整大小中
     */
    resize(e) {
        const deltaX = e.clientX - this.resizeState.startX;
        const deltaY = e.clientY - this.resizeState.startY;

        const newWidth = Math.max(300, this.resizeState.startWidth + deltaX);
        const newHeight = Math.max(200, this.resizeState.startHeight + deltaY);

        // 限制最大尺寸
        const maxWidth = window.innerWidth - parseInt(this.element.style.left);
        const maxHeight = window.innerHeight - parseInt(this.element.style.top);

        this.element.style.width = `${Math.min(newWidth, maxWidth)}px`;
        this.element.style.height = `${Math.min(newHeight, maxHeight)}px`;
    }

    /**
     * 结束调整大小
     */
    endResize() {
        this.resizeState.isResizing = false;
        this.element.classList.remove('resizing');
        this.emit('resized', {
            width: this.element.offsetWidth,
            height: this.element.offsetHeight
        });
    }

    /**
     * 绑定焦点事件
     */
    bindFocusEvents() {
        const focusHandler = () => {
            this.focus();
        };

        this.element.addEventListener('mousedown', focusHandler);
        this.boundEventHandlers.set('focus', focusHandler);
    }

    /**
     * 绑定键盘事件
     */
    bindKeyboardEvents() {
        const keydownHandler = (e) => {
            if (!this.isFocused) return;

            switch (e.key) {
                case 'Escape':
                    this.close();
                    break;
                case 'F11':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'm':
                case 'M':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.toggleMinimize();
                    }
                    break;
            }
        };

        document.addEventListener('keydown', keydownHandler);
        this.boundEventHandlers.set('keyboard', keydownHandler);
    }

    /**
     * 获得焦点
     */
    focus() {
        if (this.isFocused) return;

        // 移除其他窗口的焦点
        document.querySelectorAll('.link-preview-window.focused').forEach(win => {
            win.classList.remove('focused');
        });

        // 设置当前窗口焦点
        this.element.classList.add('focused');
        this.element.focus();
        this.isFocused = true;
        this.bringToFront();

        this.emit('focus');
    }

    /**
     * 失去焦点
     */
    blur() {
        this.element.classList.remove('focused');
        this.isFocused = false;
        this.emit('blur');
    }

    /**
     * 置于最前
     */
    bringToFront() {
        // 获取当前最高的z-index
        const allWindows = document.querySelectorAll('.link-preview-window');
        let maxZIndex = 10000;

        allWindows.forEach(win => {
            const zIndex = parseInt(win.style.zIndex) || 10000;
            if (zIndex > maxZIndex) {
                maxZIndex = zIndex;
            }
        });

        this.zIndex = maxZIndex + 1;
        this.element.style.zIndex = this.zIndex;
    }

    /**
     * 切换最小化状态
     */
    toggleMinimize() {
        this.isMinimized = !this.isMinimized;

        if (this.isMinimized) {
            this.element.classList.add('minimized');
            this.emit('minimized');
        } else {
            this.element.classList.remove('minimized');
            this.emit('restored');
        }
    }

    /**
     * 切换全屏状态
     */
    toggleFullscreen() {
        this.isFullscreen = !this.isFullscreen;

        if (this.isFullscreen) {
            this.element.classList.add('fullscreen');
            this.emit('fullscreen');
        } else {
            this.element.classList.remove('fullscreen');
            this.emit('exitFullscreen');
        }
    }

    /**
     * 重新加载
     */
    reload() {
        this.state = 'loading';
        this.updateStatus('loading');
        this.showLoading();
        this.iframe.src = this.url;
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        if (this.loadingElement) {
            this.loadingElement.style.display = 'flex';
        }
    }

    /**
     * 检查阅读模式可用性
     */
    checkReadingModeAvailability() {
        // 简单检查：如果是文章类网站，显示阅读模式按钮
        try {
            const url = new URL(this.url);
            const articleSites = ['medium.com', 'zhihu.com', 'jianshu.com', 'csdn.net', 'cnblogs.com'];

            if (articleSites.some(site => url.hostname.includes(site))) {
                this.readingModeBtn.style.display = 'block';
            }
        } catch (error) {
            // 忽略URL解析错误
        }
    }

    /**
     * 切换阅读模式
     */
    toggleReadingMode() {
        // 这里将在第三阶段实现
        console.log('阅读模式功能将在第三阶段实现');
        this.emit('toggleReadingMode');
    }

    /**
     * 解绑所有事件
     */
    unbindAllEvents() {
        this.boundEventHandlers.forEach((handler, eventType) => {
            switch (eventType) {
                case 'dragMove':
                case 'dragEnd':
                case 'resizeMove':
                case 'resizeEnd':
                case 'keyboard':
                    document.removeEventListener('mousemove', handler);
                    document.removeEventListener('mouseup', handler);
                    document.removeEventListener('keydown', handler);
                    break;
                default:
                    // 其他事件在元素销毁时自动清理
                    break;
            }
        });

        this.boundEventHandlers.clear();
    }

    /**
     * 事件系统 - 添加事件监听器
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 事件系统 - 移除事件监听器
     */
    off(event, callback) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 事件系统 - 触发事件
     */
    emit(event, data) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`预览窗口事件处理错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 获取窗口信息
     */
    getInfo() {
        return {
            id: this.id,
            url: this.url,
            state: this.state,
            isFocused: this.isFocused,
            isMinimized: this.isMinimized,
            isFullscreen: this.isFullscreen,
            position: {
                left: parseInt(this.element.style.left),
                top: parseInt(this.element.style.top)
            },
            size: {
                width: this.element.offsetWidth,
                height: this.element.offsetHeight
            },
            zIndex: this.zIndex
        };
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.PreviewWindow = PreviewWindow;
}
}
