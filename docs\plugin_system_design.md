# Moment Search V3 插件系统设计方案

> 基于Mue项目插件系统的简化实现方案

## 📋 概述

### 设计目标
- **简洁性**: 保持核心系统的简洁，插件作为可选增强
- **安全性**: 确保插件不能破坏核心功能
- **易用性**: 用户友好的插件管理界面
- **扩展性**: 为未来功能扩展提供基础

### 核心理念
- **渐进式增强**: 插件系统不影响核心功能
- **沙箱隔离**: 插件在受控环境中运行
- **标准化API**: 统一的插件开发接口
- **热插拔**: 支持动态加载和卸载

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────────────────────────┐
│           Moment Search V3          │
├─────────────────────────────────────┤
│         Plugin Manager              │
├─────────────────────────────────────┤
│  Plugin API  │  Plugin Sandbox     │
├─────────────────────────────────────┤
│  Plugin A    │  Plugin B │ Plugin C │
└─────────────────────────────────────┘
```

### 核心组件

#### 1. Plugin Manager (插件管理器)
```javascript
class PluginManager {
  constructor() {
    this.plugins = new Map();
    this.hooks = new Map();
    this.api = new PluginAPI();
    this.sandbox = new PluginSandbox();
  }

  // 插件生命周期管理
  async loadPlugin(pluginConfig) { }
  async unloadPlugin(pluginId) { }
  async enablePlugin(pluginId) { }
  async disablePlugin(pluginId) { }
  
  // 钩子系统
  registerHook(hookName, callback) { }
  executeHook(hookName, data) { }
}
```

#### 2. Plugin API (插件接口)
```javascript
class PluginAPI {
  constructor() {
    this.allowedMethods = [
      'storage.get',
      'storage.set', 
      'ui.addWidget',
      'ui.showNotification',
      'search.addPlatform',
      'shortcuts.add'
    ];
  }

  // 安全的API调用
  call(method, ...args) {
    if (!this.allowedMethods.includes(method)) {
      throw new Error(`Method ${method} not allowed`);
    }
    return this.executeMethod(method, args);
  }
}
```

#### 3. Plugin Sandbox (插件沙箱)
```javascript
class PluginSandbox {
  createSandbox(pluginCode) {
    // 创建受限的执行环境
    const sandbox = {
      console: this.createSafeConsole(),
      setTimeout: this.createSafeTimeout(),
      // 限制全局对象访问
      window: undefined,
      document: undefined,
      eval: undefined
    };
    
    return new Function('api', 'sandbox', pluginCode);
  }
}
```

## 🔌 插件标准

### 插件结构
```javascript
// plugin.json - 插件配置文件
{
  "id": "weather-widget",
  "name": "天气小组件",
  "version": "1.0.0",
  "description": "显示当前天气信息",
  "author": "Plugin Developer",
  "main": "index.js",
  "permissions": [
    "network.fetch",
    "ui.addWidget",
    "storage.local"
  ],
  "hooks": [
    "onPageLoad",
    "onSettingsChange"
  ],
  "settings": {
    "city": {
      "type": "string",
      "default": "北京",
      "label": "城市"
    },
    "unit": {
      "type": "select",
      "options": ["celsius", "fahrenheit"],
      "default": "celsius",
      "label": "温度单位"
    }
  }
}
```

### 插件主文件
```javascript
// index.js - 插件主逻辑
class WeatherWidget {
  constructor(api) {
    this.api = api;
    this.settings = {};
  }

  // 插件初始化
  async init() {
    this.settings = await this.api.call('storage.get', 'weather-settings');
    this.createWidget();
    this.startWeatherUpdate();
  }

  // 创建UI组件
  createWidget() {
    const widget = {
      id: 'weather-widget',
      position: 'top-right',
      html: this.generateWeatherHTML(),
      style: this.getWidgetStyle()
    };
    
    this.api.call('ui.addWidget', widget);
  }

  // 获取天气数据
  async fetchWeather() {
    try {
      const response = await this.api.call('network.fetch', 
        `https://api.weather.com/v1/current?city=${this.settings.city}`
      );
      return response.json();
    } catch (error) {
      this.api.call('ui.showNotification', {
        type: 'error',
        message: '获取天气信息失败'
      });
    }
  }

  // 生成HTML
  generateWeatherHTML() {
    return `
      <div class="weather-widget">
        <div class="weather-icon">☀️</div>
        <div class="weather-temp">25°C</div>
        <div class="weather-desc">晴朗</div>
      </div>
    `;
  }

  // 插件清理
  destroy() {
    this.api.call('ui.removeWidget', 'weather-widget');
  }
}

// 插件导出
window.PluginExport = WeatherWidget;
```

## 🛠️ 实现细节

### 1. 插件加载流程
```javascript
class PluginLoader {
  async loadPlugin(pluginPath) {
    // 1. 读取插件配置
    const config = await this.loadPluginConfig(pluginPath);
    
    // 2. 验证插件
    if (!this.validatePlugin(config)) {
      throw new Error('插件验证失败');
    }
    
    // 3. 检查权限
    if (!this.checkPermissions(config.permissions)) {
      throw new Error('权限不足');
    }
    
    // 4. 加载插件代码
    const pluginCode = await this.loadPluginCode(pluginPath);
    
    // 5. 创建沙箱环境
    const sandbox = this.sandbox.createSandbox(pluginCode);
    
    // 6. 执行插件
    const PluginClass = sandbox(this.api, {});
    const pluginInstance = new PluginClass(this.api);
    
    // 7. 初始化插件
    await pluginInstance.init();
    
    // 8. 注册插件
    this.plugins.set(config.id, {
      config,
      instance: pluginInstance,
      sandbox
    });
    
    return config.id;
  }
}
```

### 2. 钩子系统
```javascript
class HookSystem {
  constructor() {
    this.hooks = new Map();
    this.defineSystemHooks();
  }

  defineSystemHooks() {
    this.hooks.set('onPageLoad', []);
    this.hooks.set('onSearchExecute', []);
    this.hooks.set('onShortcutAdd', []);
    this.hooks.set('onThemeChange', []);
    this.hooks.set('onSettingsChange', []);
  }

  register(hookName, callback, pluginId) {
    if (!this.hooks.has(hookName)) {
      this.hooks.set(hookName, []);
    }
    
    this.hooks.get(hookName).push({
      callback,
      pluginId,
      priority: 0
    });
  }

  async execute(hookName, data = {}) {
    const callbacks = this.hooks.get(hookName) || [];
    
    // 按优先级排序
    callbacks.sort((a, b) => b.priority - a.priority);
    
    // 依次执行回调
    for (const { callback, pluginId } of callbacks) {
      try {
        await callback(data);
      } catch (error) {
        console.error(`Plugin ${pluginId} hook error:`, error);
      }
    }
  }
}
```

### 3. 权限系统
```javascript
class PermissionManager {
  constructor() {
    this.permissions = {
      'network.fetch': {
        description: '网络请求权限',
        risk: 'medium',
        handler: this.handleNetworkPermission.bind(this)
      },
      'storage.local': {
        description: '本地存储权限',
        risk: 'low',
        handler: this.handleStoragePermission.bind(this)
      },
      'ui.addWidget': {
        description: 'UI组件权限',
        risk: 'low',
        handler: this.handleUIPermission.bind(this)
      }
    };
  }

  async requestPermission(permission, pluginId) {
    const permissionInfo = this.permissions[permission];
    if (!permissionInfo) {
      throw new Error(`Unknown permission: ${permission}`);
    }

    // 高风险权限需要用户确认
    if (permissionInfo.risk === 'high') {
      const granted = await this.showPermissionDialog(permission, pluginId);
      if (!granted) {
        throw new Error(`Permission denied: ${permission}`);
      }
    }

    return permissionInfo.handler(pluginId);
  }

  async showPermissionDialog(permission, pluginId) {
    return new Promise((resolve) => {
      const dialog = document.createElement('div');
      dialog.innerHTML = `
        <div class="permission-dialog">
          <h3>权限请求</h3>
          <p>插件 "${pluginId}" 请求 "${permission}" 权限</p>
          <div class="dialog-actions">
            <button onclick="resolve(false)">拒绝</button>
            <button onclick="resolve(true)">允许</button>
          </div>
        </div>
      `;
      document.body.appendChild(dialog);
    });
  }
}
```

## 🎨 用户界面

### 插件管理界面
```javascript
class PluginManagerUI {
  constructor(pluginManager) {
    this.pluginManager = pluginManager;
  }

  renderPluginList() {
    return `
      <div class="plugin-manager">
        <div class="plugin-header">
          <h3>插件管理</h3>
          <button class="install-plugin-btn">安装插件</button>
        </div>
        
        <div class="plugin-list">
          ${this.renderInstalledPlugins()}
        </div>
        
        <div class="plugin-store">
          <h4>插件商店</h4>
          ${this.renderAvailablePlugins()}
        </div>
      </div>
    `;
  }

  renderPluginCard(plugin) {
    return `
      <div class="plugin-card" data-plugin-id="${plugin.id}">
        <div class="plugin-info">
          <h4>${plugin.name}</h4>
          <p>${plugin.description}</p>
          <span class="plugin-version">v${plugin.version}</span>
        </div>
        
        <div class="plugin-actions">
          <button class="plugin-toggle ${plugin.enabled ? 'enabled' : 'disabled'}">
            ${plugin.enabled ? '禁用' : '启用'}
          </button>
          <button class="plugin-settings">设置</button>
          <button class="plugin-remove">移除</button>
        </div>
      </div>
    `;
  }
}
```

## 📦 插件分发

### 插件商店结构
```javascript
class PluginStore {
  constructor() {
    this.storeUrl = 'https://plugins.moment-search.com';
    this.categories = [
      'widgets',      // 小组件
      'themes',       // 主题
      'tools',        // 工具
      'integrations'  // 集成
    ];
  }

  async fetchPlugins(category = 'all') {
    const response = await fetch(`${this.storeUrl}/api/plugins?category=${category}`);
    return response.json();
  }

  async installPlugin(pluginId) {
    // 1. 下载插件包
    const pluginPackage = await this.downloadPlugin(pluginId);
    
    // 2. 验证插件签名
    if (!this.verifyPluginSignature(pluginPackage)) {
      throw new Error('插件签名验证失败');
    }
    
    // 3. 解压并安装
    await this.extractAndInstall(pluginPackage);
    
    // 4. 加载插件
    return this.pluginManager.loadPlugin(pluginId);
  }
}
```

## 🔒 安全考虑

### 安全措施
1. **代码沙箱**: 限制插件访问全局对象
2. **权限系统**: 细粒度的权限控制
3. **代码签名**: 验证插件来源和完整性
4. **API限制**: 只暴露安全的API接口
5. **资源限制**: 限制插件的CPU和内存使用

### 风险评估
```javascript
class SecurityManager {
  assessPluginRisk(plugin) {
    let riskScore = 0;
    
    // 权限风险评估
    plugin.permissions.forEach(permission => {
      riskScore += this.getPermissionRisk(permission);
    });
    
    // 代码复杂度评估
    riskScore += this.analyzeCodeComplexity(plugin.code);
    
    // 网络请求评估
    if (plugin.permissions.includes('network.fetch')) {
      riskScore += this.analyzeNetworkUsage(plugin.code);
    }
    
    return {
      score: riskScore,
      level: this.getRiskLevel(riskScore),
      recommendations: this.getSecurityRecommendations(riskScore)
    };
  }
}
```

## 📈 性能优化

### 懒加载策略
```javascript
class PluginLazyLoader {
  constructor() {
    this.loadedPlugins = new Set();
    this.pluginQueue = [];
  }

  async loadPluginOnDemand(pluginId, trigger) {
    if (this.loadedPlugins.has(pluginId)) {
      return;
    }

    // 根据触发条件决定加载时机
    switch (trigger) {
      case 'page-load':
        await this.loadPlugin(pluginId);
        break;
      case 'user-interaction':
        this.queuePlugin(pluginId);
        break;
      case 'idle':
        requestIdleCallback(() => this.loadPlugin(pluginId));
        break;
    }
  }
}
```

## 🚀 实施路线图

### Phase 1: 基础框架 (2-3周)
- [ ] 插件管理器核心实现
- [ ] 基础API接口设计
- [ ] 简单的沙箱环境
- [ ] 插件加载/卸载功能

### Phase 2: 安全和权限 (2周)
- [ ] 权限系统实现
- [ ] 安全沙箱增强
- [ ] 代码签名验证
- [ ] 风险评估系统

### Phase 3: 用户界面 (1-2周)
- [ ] 插件管理界面
- [ ] 插件设置面板
- [ ] 安装向导
- [ ] 用户反馈系统

### Phase 4: 插件商店 (2-3周)
- [ ] 插件商店后端
- [ ] 插件分发系统
- [ ] 插件评级和评论
- [ ] 自动更新机制

## 📋 开发指南

### 插件开发模板
```bash
# 创建插件项目
mkdir my-plugin
cd my-plugin

# 基础文件结构
touch plugin.json
touch index.js
touch style.css
touch README.md

# 开发工具
npm init -y
npm install @moment-search/plugin-dev-tools
```

### 调试工具
```javascript
class PluginDebugger {
  constructor() {
    this.logs = [];
    this.performance = new Map();
  }

  log(pluginId, level, message) {
    const logEntry = {
      timestamp: Date.now(),
      pluginId,
      level,
      message
    };
    
    this.logs.push(logEntry);
    console.log(`[Plugin:${pluginId}] ${message}`);
  }

  startPerformanceTimer(pluginId, operation) {
    this.performance.set(`${pluginId}:${operation}`, performance.now());
  }

  endPerformanceTimer(pluginId, operation) {
    const startTime = this.performance.get(`${pluginId}:${operation}`);
    const duration = performance.now() - startTime;
    
    this.log(pluginId, 'performance', `${operation} took ${duration.toFixed(2)}ms`);
  }
}
```

## 🎯 示例插件

### 1. 天气小组件插件
```javascript
// weather-widget/plugin.json
{
  "id": "weather-widget",
  "name": "天气小组件",
  "version": "1.0.0",
  "description": "在新标签页显示实时天气信息",
  "author": "Moment Search Team",
  "main": "index.js",
  "permissions": ["network.fetch", "ui.addWidget", "storage.local"],
  "settings": {
    "city": { "type": "string", "default": "北京", "label": "城市" },
    "apiKey": { "type": "string", "default": "", "label": "API密钥" },
    "updateInterval": { "type": "number", "default": 30, "label": "更新间隔(分钟)" }
  }
}

// weather-widget/index.js
class WeatherWidget {
  constructor(api) {
    this.api = api;
    this.updateTimer = null;
  }

  async init() {
    const settings = await this.api.call('storage.get', 'weather-settings');
    this.settings = { ...this.getDefaultSettings(), ...settings };

    await this.createWidget();
    this.startAutoUpdate();

    // 注册设置变更监听
    this.api.registerHook('onSettingsChange', (data) => {
      if (data.pluginId === 'weather-widget') {
        this.updateSettings(data.settings);
      }
    });
  }

  async createWidget() {
    const weatherData = await this.fetchWeather();
    const widget = {
      id: 'weather-widget',
      position: 'top-right',
      html: this.generateHTML(weatherData),
      css: this.getCSS()
    };

    await this.api.call('ui.addWidget', widget);
  }

  async fetchWeather() {
    try {
      const url = `https://api.openweathermap.org/data/2.5/weather?q=${this.settings.city}&appid=${this.settings.apiKey}&units=metric&lang=zh_cn`;
      const response = await this.api.call('network.fetch', url);
      return await response.json();
    } catch (error) {
      this.api.call('ui.showNotification', {
        type: 'error',
        message: '获取天气信息失败'
      });
      return null;
    }
  }

  generateHTML(data) {
    if (!data) {
      return '<div class="weather-widget error">天气信息加载失败</div>';
    }

    return `
      <div class="weather-widget">
        <div class="weather-location">${data.name}</div>
        <div class="weather-main">
          <div class="weather-icon">${this.getWeatherIcon(data.weather[0].icon)}</div>
          <div class="weather-temp">${Math.round(data.main.temp)}°C</div>
        </div>
        <div class="weather-desc">${data.weather[0].description}</div>
        <div class="weather-details">
          <span>湿度: ${data.main.humidity}%</span>
          <span>风速: ${data.wind.speed}m/s</span>
        </div>
      </div>
    `;
  }

  getCSS() {
    return `
      .weather-widget {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        min-width: 200px;
      }

      .weather-location {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .weather-main {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
      }

      .weather-icon {
        font-size: 32px;
      }

      .weather-temp {
        font-size: 24px;
        font-weight: 600;
        color: #333;
      }

      .weather-desc {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        text-transform: capitalize;
      }

      .weather-details {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #999;
      }
    `;
  }

  startAutoUpdate() {
    this.updateTimer = setInterval(() => {
      this.updateWeather();
    }, this.settings.updateInterval * 60 * 1000);
  }

  async updateWeather() {
    const weatherData = await this.fetchWeather();
    if (weatherData) {
      await this.api.call('ui.updateWidget', 'weather-widget', {
        html: this.generateHTML(weatherData)
      });
    }
  }

  destroy() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
    }
    this.api.call('ui.removeWidget', 'weather-widget');
  }
}

window.PluginExport = WeatherWidget;
```

### 2. 搜索平台扩展插件
```javascript
// search-platforms/plugin.json
{
  "id": "search-platforms",
  "name": "搜索平台扩展",
  "version": "1.0.0",
  "description": "添加更多搜索平台支持",
  "author": "Community",
  "main": "index.js",
  "permissions": ["search.addPlatform"],
  "settings": {
    "enabledPlatforms": {
      "type": "multiselect",
      "options": ["stackoverflow", "reddit", "twitter", "instagram"],
      "default": ["stackoverflow", "reddit"],
      "label": "启用的平台"
    }
  }
}

// search-platforms/index.js
class SearchPlatformsExtension {
  constructor(api) {
    this.api = api;
    this.platforms = {
      'stackoverflow': {
        name: 'Stack Overflow',
        icon: '💻',
        url: 'https://stackoverflow.com/search?q=',
        aliases: ['so', 'stack']
      },
      'reddit': {
        name: 'Reddit',
        icon: '🔴',
        url: 'https://www.reddit.com/search/?q=',
        aliases: ['reddit', 'r']
      },
      'twitter': {
        name: 'Twitter',
        icon: '🐦',
        url: 'https://twitter.com/search?q=',
        aliases: ['twitter', 'tw']
      },
      'instagram': {
        name: 'Instagram',
        icon: '📷',
        url: 'https://www.instagram.com/explore/tags/',
        aliases: ['instagram', 'ig']
      }
    };
  }

  async init() {
    const settings = await this.api.call('storage.get', 'search-platforms-settings');
    this.settings = { ...this.getDefaultSettings(), ...settings };

    // 添加启用的搜索平台
    this.settings.enabledPlatforms.forEach(platformId => {
      const platform = this.platforms[platformId];
      if (platform) {
        this.api.call('search.addPlatform', {
          id: platformId,
          ...platform
        });
      }
    });
  }

  destroy() {
    // 移除添加的搜索平台
    this.settings.enabledPlatforms.forEach(platformId => {
      this.api.call('search.removePlatform', platformId);
    });
  }
}

window.PluginExport = SearchPlatformsExtension;
```

## 🔧 开发工具和调试

### 插件开发CLI工具
```bash
# 安装开发工具
npm install -g @moment-search/plugin-cli

# 创建新插件
moment-plugin create my-awesome-plugin

# 开发模式（热重载）
moment-plugin dev

# 构建插件
moment-plugin build

# 发布到插件商店
moment-plugin publish
```

### 开发者控制台
```javascript
class PluginDevConsole {
  constructor() {
    this.isDevMode = localStorage.getItem('plugin-dev-mode') === 'true';
    if (this.isDevMode) {
      this.initDevConsole();
    }
  }

  initDevConsole() {
    // 添加开发者控制台到页面
    const devConsole = document.createElement('div');
    devConsole.id = 'plugin-dev-console';
    devConsole.innerHTML = `
      <div class="dev-console-header">
        <h4>插件开发控制台</h4>
        <button onclick="this.parentElement.parentElement.style.display='none'">×</button>
      </div>
      <div class="dev-console-content">
        <div class="dev-console-tabs">
          <button class="tab-btn active" data-tab="logs">日志</button>
          <button class="tab-btn" data-tab="performance">性能</button>
          <button class="tab-btn" data-tab="plugins">插件</button>
        </div>
        <div class="dev-console-panels">
          <div class="panel active" id="logs-panel"></div>
          <div class="panel" id="performance-panel"></div>
          <div class="panel" id="plugins-panel"></div>
        </div>
      </div>
    `;

    document.body.appendChild(devConsole);
    this.bindDevConsoleEvents();
  }

  log(level, message, pluginId) {
    if (!this.isDevMode) return;

    const logsPanel = document.getElementById('logs-panel');
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${level}`;
    logEntry.innerHTML = `
      <span class="log-time">${new Date().toLocaleTimeString()}</span>
      <span class="log-plugin">[${pluginId}]</span>
      <span class="log-message">${message}</span>
    `;

    logsPanel.appendChild(logEntry);
    logsPanel.scrollTop = logsPanel.scrollHeight;
  }
}
```

## 📚 最佳实践

### 插件开发最佳实践
1. **性能优化**
   - 使用懒加载减少初始化时间
   - 避免频繁的DOM操作
   - 合理使用缓存机制

2. **用户体验**
   - 提供清晰的错误提示
   - 支持优雅降级
   - 遵循一致的设计规范

3. **安全考虑**
   - 验证所有用户输入
   - 使用HTTPS进行网络请求
   - 避免执行不可信的代码

4. **兼容性**
   - 测试不同浏览器的兼容性
   - 处理API变更的向后兼容
   - 提供版本迁移机制

### 代码示例：错误处理
```javascript
class RobustPlugin {
  constructor(api) {
    this.api = api;
    this.errorHandler = new PluginErrorHandler(api);
  }

  async safeExecute(operation, fallback = null) {
    try {
      return await operation();
    } catch (error) {
      this.errorHandler.handle(error);
      return fallback;
    }
  }

  async init() {
    await this.safeExecute(async () => {
      // 插件初始化逻辑
      await this.loadSettings();
      await this.createUI();
      this.bindEvents();
    }, () => {
      // 初始化失败的降级处理
      this.api.call('ui.showNotification', {
        type: 'warning',
        message: '插件初始化失败，使用基础功能'
      });
    });
  }
}

class PluginErrorHandler {
  constructor(api) {
    this.api = api;
    this.errorCounts = new Map();
  }

  handle(error) {
    const errorKey = error.message || 'unknown';
    const count = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, count + 1);

    // 错误频率过高时禁用插件
    if (count > 5) {
      this.api.call('plugin.disable', this.pluginId);
      this.api.call('ui.showNotification', {
        type: 'error',
        message: '插件错误过多，已自动禁用'
      });
    }

    // 记录错误日志
    console.error(`Plugin Error [${this.pluginId}]:`, error);
  }
}
```

---

**文档版本**: V1.0
**创建时间**: 2025-01-19
**更新时间**: 2025-01-19
**负责人**: Moment Search Team
