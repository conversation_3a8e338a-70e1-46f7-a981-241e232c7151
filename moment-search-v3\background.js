// Moment Search V3 - 后台服务脚本
(function() {
    'use strict';

    // 扩展安装和更新处理
    chrome.runtime.onInstalled.addListener((details) => {
        console.log('🎉 Moment Search V3 安装/更新事件:', details.reason);
        
        switch (details.reason) {
            case 'install':
                console.log('🆕 首次安装 Moment Search V3');
                // 首次安装时可以打开欢迎页面
                // chrome.tabs.create({ url: chrome.runtime.getURL('index.html') });
                break;
                
            case 'update':
                console.log('🔄 Moment Search V3 更新到版本:', chrome.runtime.getManifest().version);
                // 更新时的处理逻辑
                handleExtensionUpdate(details.previousVersion);
                break;
                
            default:
                console.log('🔧 其他安装事件:', details.reason);
        }
    });

    // 处理扩展更新
    function handleExtensionUpdate(previousVersion) {
        // 可以在这里处理版本升级逻辑
        console.log(`从版本 ${previousVersion} 更新到 ${chrome.runtime.getManifest().version}`);
        
        // 例如：清理旧版本的数据、迁移设置等
        // 这里可以根据需要添加具体的升级逻辑
    }

    // 处理来自内容脚本或popup的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('📨 收到消息:', message.type, '来自:', sender.tab?.url || 'extension');
        
        const messageHandlers = {
            // 获取扩展信息
            'getExtensionInfo': () => {
                return {
                    version: chrome.runtime.getManifest().version,
                    name: chrome.runtime.getManifest().name
                };
            },
            
            // 打开新标签页
            'openNewTab': async () => {
                try {
                    await chrome.tabs.create({ 
                        url: chrome.runtime.getURL('index.html'),
                        active: true 
                    });
                    return { success: true };
                } catch (error) {
                    console.error('打开新标签页失败:', error);
                    return { success: false, error: error.message };
                }
            },
            
            // 执行搜索（如果需要在后台处理）
            'executeSearch': async () => {
                const { platform, query } = message.data || {};
                if (platform?.url && query) {
                    try {
                        await chrome.tabs.create({ 
                            url: platform.url + encodeURIComponent(query),
                            active: true 
                        });
                        return { success: true };
                    } catch (error) {
                        console.error('执行搜索失败:', error);
                        return { success: false, error: error.message };
                    }
                }
                return { success: false, error: 'Missing platform or query' };
            },
            
            // 获取存储数据
            'getStorageData': async () => {
                const { keys } = message.data || {};
                try {
                    const result = await chrome.storage.local.get(keys);
                    return { success: true, data: result };
                } catch (error) {
                    console.error('获取存储数据失败:', error);
                    return { success: false, error: error.message };
                }
            },
            
            // 设置存储数据
            'setStorageData': async () => {
                const { data } = message.data || {};
                try {
                    await chrome.storage.local.set(data);
                    return { success: true };
                } catch (error) {
                    console.error('设置存储数据失败:', error);
                    return { success: false, error: error.message };
                }
            }
        };
        
        const handler = messageHandlers[message.type];
        if (handler) {
            // 异步处理消息
            handler().then(sendResponse).catch(error => {
                console.error('❌ 消息处理失败:', error);
                sendResponse({ success: false, error: error.message });
            });
            return true; // 保持消息通道开放
        }
        
        console.log('❓ 未知消息类型:', message.type);
        sendResponse({ success: false, error: 'Unknown message type' });
    });

    // 监听标签页更新事件
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
        // 当标签页完成加载时
        if (changeInfo.status === 'complete' && tab.url) {
            // 可以在这里添加一些标签页相关的处理逻辑
            // 例如：统计使用情况、检查特定网站等
        }
    });

    // 监听标签页激活事件
    chrome.tabs.onActivated.addListener((activeInfo) => {
        // 当用户切换到不同标签页时
        // 可以在这里添加相关处理逻辑
    });

    // 扩展启动时的初始化
    function initializeExtension() {
        console.log('🚀 Moment Search V3 后台脚本已启动');
        
        // 设置扩展的卸载URL（可选）
        try {
            chrome.runtime.setUninstallURL('https://github.com/moment-search/moment-search');
        } catch (error) {
            console.warn('设置卸载URL失败:', error);
        }
        
        // 其他初始化逻辑
        setupPerformanceMonitoring();
    }

    // 性能监控设置
    function setupPerformanceMonitoring() {
        // 监控扩展性能
        const startTime = performance.now();
        
        // 定期检查内存使用情况
        setInterval(() => {
            if (performance.memory) {
                const memoryInfo = {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                };
                
                // 如果内存使用过高，记录警告
                if (memoryInfo.used > 50) {
                    console.warn('⚠️ 内存使用较高:', memoryInfo);
                }
            }
        }, 60000); // 每分钟检查一次
        
        console.log('📊 性能监控已启动，启动时间:', Math.round(performance.now() - startTime), 'ms');
    }

    // 错误处理
    self.addEventListener('error', (event) => {
        console.error('🚨 后台脚本错误:', event.error);
    });

    self.addEventListener('unhandledrejection', (event) => {
        console.error('🚨 未处理的Promise拒绝:', event.reason);
    });

    // 启动扩展
    initializeExtension();

})();
