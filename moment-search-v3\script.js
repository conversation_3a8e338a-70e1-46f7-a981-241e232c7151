// Moment Search V3 - 主脚本文件
(function() {
    'use strict';

    // 注意：搜索平台配置现在由SearchPlatformConfig类管理



    // 默认快捷方式配置 - 简化为2个预设图标，使用API获取真实图标
    const DEFAULT_SHORTCUTS = [
        // 预设图标，src为空，将通过API获取真实图标
        { id: 1, name: 'Bing', url: 'https://www.bing.com', icon: '🌐', order: 1, category: 'search', searchMode: 'direct', pageMode: 'normal', src: '' },
        { id: 2, name: 'Chrome', url: 'https://www.google.com/chrome', icon: '🌍', order: 2, category: 'browser', searchMode: 'direct', pageMode: 'normal', src: '' }
    ];

    // 移除了未使用的SHORTCUT_CATEGORIES常量

    // 全局事件管理器 - 避免重复的 document 事件监听器
    const GlobalEventManager = {
        clickHandlers: new Set(),

        init() {
            document.addEventListener('click', (e) => {
                this.clickHandlers.forEach(handler => {
                    try {
                        handler(e);
                    } catch (error) {
                        console.error('全局点击事件处理器错误:', error);
                    }
                });
            });
        },

        addClickHandler(handler) {
            this.clickHandlers.add(handler);
        },

        removeClickHandler(handler) {
            this.clickHandlers.delete(handler);
        }
    };

    // DOM管理器 - 统一管理DOM元素缓存
    class DOMManager {
        constructor() {
            this.cache = new Map();
        }

        // 获取DOM元素（带缓存）
        get(id) {
            if (!this.cache.has(id)) {
                const element = document.getElementById(id);
                if (element) {
                    this.cache.set(id, element);
                }
            }
            return this.cache.get(id) || null;
        }

        // 清除缓存
        clearCache() {
            this.cache.clear();
        }

        // 批量获取元素
        getMultiple(ids) {
            const result = {};
            ids.forEach(id => {
                result[id] = this.get(id);
            });
            return result;
        }
    }

    // 图片预加载工具类
    class ImagePreloader {
        static preloadImage(src, timeout = 1000) {
            return new Promise((resolve, reject) => {
                if (!src) {
                    reject(new Error('图片源为空'));
                    return;
                }

                const img = new Image();
                let resolved = false;

                img.onload = () => {
                    if (!resolved) {
                        resolved = true;
                        // 对于已缓存的图标，这里会立即触发
                        resolve(img);
                    }
                };

                img.onerror = () => {
                    if (!resolved) {
                        resolved = true;
                        reject(new Error(`图片加载失败: ${src}`));
                    }
                };

                // 设置较短的超时时间，已缓存的图标应该立即加载
                const timeoutId = setTimeout(() => {
                    if (!resolved) {
                        resolved = true;
                        reject(new Error('图片加载超时'));
                    }
                }, timeout);

                // 在本地文件环境下不设置crossOrigin，避免CORS问题
                img.src = src;

                // 如果图片已经在缓存中，onload可能会同步触发
                if (img.complete && img.naturalWidth > 0) {
                    if (!resolved) {
                        resolved = true;
                        clearTimeout(timeoutId);
                        resolve(img);
                    }
                }
            });
        }

        static async preloadImages(srcArray, timeout = 1000) {
            if (!Array.isArray(srcArray) || srcArray.length === 0) {
                return [];
            }

            // 并发预加载，但限制并发数量避免过多请求
            const concurrencyLimit = 5;
            const results = [];

            for (let i = 0; i < srcArray.length; i += concurrencyLimit) {
                const batch = srcArray.slice(i, i + concurrencyLimit);
                const batchPromises = batch.map(src =>
                    this.preloadImage(src, timeout).catch(error => {
                        // 对于已缓存的图标，加载会很快，失败通常意味着网络问题
                        console.warn(`预加载图片失败: ${src}`, error);
                        return null;
                    })
                );

                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }

            return results;
        }


    }

    // 工具函数
    const Utils = {
        // 防抖函数
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // 生成唯一ID
        generateId() {
            return Date.now() + Math.random().toString(36).substr(2, 9);
        },

        // 验证URL
        isValidUrl(string) {
            try {
                new URL(string.startsWith('http') ? string : 'https://' + string);
                return true;
            } catch (_) {
                return false;
            }
        },

        // 时间格式化功能已移至 TimeManager 类中，避免重复定义
    };

    // 图标获取管理器
    class FaviconManager {
        constructor() {
            // 使用IconCacheManager进行持久化缓存
            this.iconCacheManager = new IconCacheManager();
            this.fallbackIcon = '🌐'; // 默认图标
            this.googleFaviconAPI = 'https://www.google.com/s2/favicons';
            this.faviconImAPI = 'https://favicon.im'; // 高质量favicon服务
        }

        /**
         * 获取网站图标
         * @param {string} url - 网站URL
         * @param {number} size - 图标大小，默认32px
         * @returns {Promise<string>} 图标URL
         */
        async getFavicon(url, size = 32) {
            try {
                // 标准化URL
                const normalizedUrl = this.normalizeUrl(url);

                // 优先从持久化缓存获取
                const cached = this.iconCacheManager.getCachedIcon(normalizedUrl, size);
                if (cached) {
                    console.log(`从缓存获取图标: ${normalizedUrl}`);
                    return cached.url;
                }

                console.log(`缓存未命中，获取新图标: ${normalizedUrl}`);

                // 优先使用高质量favicon.im服务
                const faviconImUrl = await this.getFaviconImIcon(normalizedUrl, size);
                if (faviconImUrl) {
                    // 缓存到持久化存储
                    this.iconCacheManager.cacheIcon(normalizedUrl, faviconImUrl, size, {
                        source: 'favicon.im',
                        verified: true
                    });
                    return faviconImUrl;
                }

                // 备用1：使用Chrome Extension Favicon API
                const chromeIconUrl = await this.getChromeFavicon(normalizedUrl, size);
                if (chromeIconUrl) {
                    // 缓存到持久化存储
                    this.iconCacheManager.cacheIcon(normalizedUrl, chromeIconUrl, size, {
                        source: 'chrome-extension',
                        verified: true
                    });
                    return chromeIconUrl;
                }

                // 备用2：使用Google Favicon API
                const googleIconUrl = this.getGoogleFavicon(normalizedUrl, size);
                // 缓存到持久化存储
                this.iconCacheManager.cacheIcon(normalizedUrl, googleIconUrl, size, {
                    source: 'google-favicon',
                    verified: false // Google API不需要验证，但可能质量较低
                });
                return googleIconUrl;

            } catch (error) {
                console.warn('获取图标失败:', error);
                return this.getDefaultIcon();
            }
        }

        /**
         * 使用favicon.im高质量服务获取图标
         * @param {string} url - 网站URL
         * @param {number} size - 图标大小
         * @returns {Promise<string|null>} 图标URL或null
         */
        async getFaviconImIcon(url, size) {
            try {
                const domain = this.extractDomain(url);
                if (!domain) return null;

                // 根据尺寸选择合适的参数
                const larger = size > 32 ? '?larger=true' : '';
                const faviconUrl = `${this.faviconImAPI}/${domain}${larger}`;

                // 测试图标是否可用
                const isValid = await this.testImageUrl(faviconUrl);
                return isValid ? faviconUrl : null;

            } catch (error) {
                console.warn('Favicon.im服务获取失败:', error);
                return null;
            }
        }

        /**
         * 使用Chrome Extension Favicon API获取图标
         * @param {string} url - 网站URL
         * @param {number} size - 图标大小
         * @returns {Promise<string|null>} 图标URL或null
         */
        async getChromeFavicon(url, size) {
            try {
                if (!chrome?.runtime?.getURL) {
                    return null;
                }

                const faviconUrl = new URL(chrome.runtime.getURL("/_favicon/"));
                faviconUrl.searchParams.set("pageUrl", url);
                faviconUrl.searchParams.set("size", size.toString());

                // 测试图标是否可用
                const isValid = await this.testImageUrl(faviconUrl.toString());
                return isValid ? faviconUrl.toString() : null;

            } catch (error) {
                console.warn('Chrome Favicon API 调用失败:', error);
                return null;
            }
        }

        /**
         * 使用Google Favicon API获取图标
         * @param {string} url - 网站URL
         * @param {number} size - 图标大小
         * @returns {string} 图标URL
         */
        getGoogleFavicon(url, size) {
            try {
                const domain = this.extractDomain(url);
                return `${this.googleFaviconAPI}?domain=${encodeURIComponent(domain)}&sz=${size}`;
            } catch (error) {
                console.warn('Google Favicon API 调用失败:', error);
                return this.getDefaultIcon();
            }
        }

        /**
         * 测试图片URL是否有效
         * @param {string} imageUrl - 图片URL
         * @returns {Promise<boolean>} 是否有效
         */
        testImageUrl(imageUrl) {
            return new Promise((resolve) => {
                const img = new Image();
                const timeout = setTimeout(() => {
                    resolve(false);
                }, 3000); // 3秒超时

                img.onload = () => {
                    clearTimeout(timeout);
                    resolve(true);
                };

                img.onerror = () => {
                    clearTimeout(timeout);
                    resolve(false);
                };

                img.src = imageUrl;
            });
        }

        /**
         * 标准化URL
         * @param {string} url - 原始URL
         * @returns {string} 标准化后的URL
         */
        normalizeUrl(url) {
            if (!url) return '';

            // 如果不是完整URL，添加https://
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }

            return url;
        }

        /**
         * 提取域名
         * @param {string} url - URL
         * @returns {string} 域名
         */
        extractDomain(url) {
            try {
                const urlObj = new URL(this.normalizeUrl(url));
                return urlObj.hostname;
            } catch (error) {
                console.warn('提取域名失败:', error);
                return url;
            }
        }

        /**
         * 获取默认图标
         * @returns {string} 默认图标
         */
        getDefaultIcon() {
            return this.fallbackIcon;
        }

        /**
         * 清除缓存
         */
        clearCache() {
            this.iconCacheManager.clearCache();
            console.log('图标缓存已清除');
        }



        /**
         * 获取缓存统计信息
         * @returns {Object} 缓存统计
         */
        getCacheStats() {
            return this.iconCacheManager.getCacheStats();
        }

        /**
         * 获取图标并自动缓存，同时更新快捷方式数据
         * @param {string} url 网站URL
         * @param {number} size 图标大小
         * @param {string} shortcutId 快捷方式ID（可选）
         * @returns {Promise<string|null>} 图标URL
         */
        async getIconAndUpdateShortcut(url, size = 32, shortcutId = null) {
            // 先检查快捷方式是否已有图标URL
            if (shortcutId) {
                const shortcut = this.findShortcutById(shortcutId);
                if (shortcut && shortcut.faviconUrl) {
                    console.log(`使用快捷方式已保存的图标: ${url}`);
                    return shortcut.faviconUrl;
                }
            }

            // 检查缓存
            const cached = this.iconCacheManager.getCachedIcon(url, size);
            if (cached) {
                console.log(`从缓存获取图标: ${url}`);
                // 如果有快捷方式ID，更新其faviconUrl
                if (shortcutId) {
                    this.updateShortcutFaviconUrl(shortcutId, cached.url);
                }
                return cached.url;
            }

            console.log(`缓存未命中，获取新图标: ${url}`);

            try {
                const iconUrl = await this.getFavicon(url, size);
                if (iconUrl && iconUrl !== this.getDefaultIcon()) {
                    // 缓存图标
                    this.iconCacheManager.cacheIcon(url, iconUrl, size, {
                        source: 'favicon-manager',
                        verified: true
                    });

                    // 如果有快捷方式ID，更新其faviconUrl
                    if (shortcutId) {
                        this.updateShortcutFaviconUrl(shortcutId, iconUrl);
                    }

                    return iconUrl;
                }
            } catch (error) {
                console.warn(`获取图标失败: ${url}`, error);
            }

            return null;
        }

        /**
         * 根据ID查找快捷方式
         * @param {string} shortcutId 快捷方式ID
         * @returns {Object|null} 快捷方式对象
         */
        findShortcutById(shortcutId) {
            const shortcutManager = window.app?.shortcutManager;
            if (!shortcutManager) return null;

            return shortcutManager.shortcuts.find(s => s.id.toString() === shortcutId.toString());
        }

        /**
         * 更新快捷方式的图标URL
         * @param {string} shortcutId 快捷方式ID
         * @param {string} iconUrl 图标URL
         */
        updateShortcutFaviconUrl(shortcutId, iconUrl) {
            const shortcutManager = window.app?.shortcutManager;
            if (!shortcutManager) return;

            const shortcut = shortcutManager.shortcuts.find(s => s.id.toString() === shortcutId.toString());
            if (shortcut) {
                shortcut.faviconUrl = iconUrl;
                shortcut.faviconCacheTime = Date.now();

                // 保存到localStorage
                shortcutManager.saveShortcuts();

                console.log(`已更新快捷方式 ${shortcut.name} 的图标URL`);
            }
        }
    }

    // 存储工具
    const Storage = {
        get(key, defaultValue = null) {
            try {
                const value = localStorage.getItem(key);
                return value ? JSON.parse(value) : defaultValue;
            } catch (error) {
                console.error('Storage get error:', error);
                return defaultValue;
            }
        },

        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Storage set error:', error);
                return false;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('Storage remove error:', error);
                return false;
            }
        }
    };

    // URLParser - 智能URL解析引擎
    class URLParser {
        constructor() {
            // 通用搜索参数 - 覆盖90%的搜索网站
            this.universalParams = [
                'q', 'query', 'search', 'keyword', 'wd', 'search_query',
                's', 'text', 'term', 'searchterm', 'find', 'lookup',
                'k', 'key', 'words', 'string', 'input', 'kw'
            ];

            // 域名特定参数 - 针对主流网站优化
            this.domainParams = {
                'google.com': ['q'],
                'baidu.com': ['wd'],
                'zhihu.com': ['q'],
                'bilibili.com': ['keyword'],
                'taobao.com': ['q'],
                'github.com': ['q'],
                'youtube.com': ['search_query'],
                'stackoverflow.com': ['q'],
                'reddit.com': ['q'],
                'amazon.com': ['k'],
                'jd.com': ['keyword'],
                'tmall.com': ['q'],
                'xiaohongshu.com': ['keyword'],
                'douban.com': ['q'],
                'weibo.com': ['q']
            };

            // 网站名称映射 - 统一管理
            this.siteNameMap = {
                'google.com': 'Google',
                'baidu.com': '百度',
                'zhihu.com': '知乎',
                'bilibili.com': 'B站',
                'taobao.com': '淘宝',
                'github.com': 'GitHub',
                'youtube.com': 'YouTube',
                'stackoverflow.com': 'Stack Overflow',
                'reddit.com': 'Reddit',
                'amazon.com': 'Amazon',
                'jd.com': '京东',
                'tmall.com': '天猫',
                'xiaohongshu.com': '小红书',
                'douban.com': '豆瓣',
                'weibo.com': '微博',
                'twitter.com': 'Twitter'
            };
        }

        // 核心解析方法 - 自动提取搜索关键词
        async parseURL(url, testKeyword = null) {
            try {
                // 1. URL验证和标准化
                const urlObj = this.validateAndParseURL(url);
                if (!urlObj.success) return urlObj;

                const { parsedURL, domain } = urlObj;

                // 2. 自动参数解析 (优先级最高)
                const paramResult = this.autoParseFromParams(parsedURL, domain);
                if (paramResult.success) {
                    return this.buildResult(paramResult, domain, parsedURL);
                }

                // 3. 如果提供了测试关键词，使用传统方法
                if (testKeyword) {
                    const legacyParamResult = this.parseFromParams(parsedURL, testKeyword, domain);
                    if (legacyParamResult.success) {
                        return this.buildResult(legacyParamResult, domain, parsedURL);
                    }

                    const pathResult = this.parseFromPath(parsedURL, testKeyword);
                    if (pathResult.success) {
                        return this.buildResult(pathResult, domain, parsedURL);
                    }
                }

                // 4. 解析失败
                return {
                    success: false,
                    error: '无法识别搜索参数',
                    suggestion: '请确保URL是有效的搜索结果页面，例如：https://github.com/search?q=javascript'
                };
            } catch (error) {
                return {
                    success: false,
                    error: 'URL解析异常',
                    details: error.message
                };
            }
        }

        // URL验证和标准化
        validateAndParseURL(url) {
            try {
                // 自动添加协议
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    url = 'https://' + url;
                }

                const parsedURL = new URL(url);
                const domain = this.extractDomainFromURL(parsedURL.hostname);

                // 基础验证
                if (!domain || domain.length < 3) {
                    return {
                        success: false,
                        error: '域名格式无效',
                        suggestion: '请输入完整的网站域名'
                    };
                }

                return { success: true, parsedURL, domain };
            } catch (error) {
                return {
                    success: false,
                    error: 'URL格式无效',
                    suggestion: '请输入完整的URL地址，例如：https://example.com/search?q=关键词'
                };
            }
        }

        // 自动从URL参数解析 - 不需要测试关键词
        autoParseFromParams(urlObj, domain) {
            const params = new URLSearchParams(urlObj.search);
            const priorityParams = this.getParamPriority(domain);

            for (const param of priorityParams) {
                const value = params.get(param);
                if (value && this.isValidSearchValue(value)) {
                    const template = this.generateParamTemplate(urlObj, param, params);
                    return {
                        success: true,
                        template,
                        searchParam: param,
                        method: 'parameter',
                        confidence: this.calculateConfidence(param, domain),
                        extractedKeyword: this.decodeSearchValue(value)
                    };
                }
            }

            return { success: false };
        }

        // 从URL参数解析 (传统方法，需要测试关键词)
        parseFromParams(urlObj, testKeyword, domain) {
            const params = new URLSearchParams(urlObj.search);
            const priorityParams = this.getParamPriority(domain);

            for (const param of priorityParams) {
                const value = params.get(param);
                if (value && this.containsKeyword(value, testKeyword)) {
                    const template = this.generateParamTemplate(urlObj, param, params);
                    return {
                        success: true,
                        template,
                        searchParam: param,
                        method: 'parameter',
                        confidence: this.calculateConfidence(param, domain)
                    };
                }
            }

            return { success: false };
        }

        // 从URL路径解析
        parseFromPath(urlObj, testKeyword) {
            const path = urlObj.pathname;

            if (this.containsKeyword(path, testKeyword)) {
                const template = urlObj.href.replace(
                    new RegExp(this.escapeRegex(testKeyword), 'gi'),
                    '{query}'
                );

                return {
                    success: true,
                    template,
                    method: 'path',
                    confidence: 0.7 // 路径解析置信度较低
                };
            }

            return { success: false };
        }

        // 生成搜索模板
        generateParamTemplate(urlObj, targetParam, params) {
            const newParams = new URLSearchParams(params);
            newParams.set(targetParam, '{query}');
            const queryString = newParams.toString().replace('%7Bquery%7D', '{query}');
            return `${urlObj.origin}${urlObj.pathname}?${queryString}`;
        }

        // 检查关键词匹配
        containsKeyword(value, keyword) {
            if (!value || !keyword) return false;

            try {
                const decodedValue = decodeURIComponent(value);
                return decodedValue.toLowerCase().includes(keyword.toLowerCase());
            } catch (error) {
                return value.toLowerCase().includes(keyword.toLowerCase());
            }
        }

        // 获取参数优先级
        getParamPriority(domain) {
            const domainSpecific = this.domainParams[domain] || [];
            return [...domainSpecific, ...this.universalParams];
        }

        // 计算解析置信度
        calculateConfidence(param, domain) {
            const domainSpecific = this.domainParams[domain] || [];
            if (domainSpecific.includes(param)) return 0.95;

            const commonParams = ['q', 'query', 'search', 'keyword'];
            if (commonParams.includes(param)) return 0.9;

            return 0.8;
        }

        // 构建最终结果
        buildResult(parseResult, domain, urlObj) {
            return {
                success: true,
                template: parseResult.template,
                name: this.extractSiteName(domain),
                domain: domain,
                icon: '🔍', // 使用默认搜索图标
                method: parseResult.method,
                searchParam: parseResult.searchParam,
                confidence: parseResult.confidence,
                originalUrl: urlObj.href
            };
        }

        // 提取网站名称
        extractSiteName(domain) {
            return this.siteNameMap[domain] || this.capitalizeFirst(domain.split('.')[0]);
        }

        // 从hostname提取域名（统一方法）
        extractDomainFromURL(hostname) {
            return hostname.replace(/^www\./, '');
        }

        // 验证是否为有效的搜索值
        isValidSearchValue(value) {
            if (!value || value.length < 1) return false;

            // 解码URL编码的值
            const decodedValue = this.decodeSearchValue(value);

            // 检查是否包含有意义的内容
            // 排除纯数字、单个字符、特殊符号等
            if (decodedValue.length < 2) return false;
            if (/^\d+$/.test(decodedValue)) return false; // 纯数字
            if (/^[^\w\u4e00-\u9fa5]+$/.test(decodedValue)) return false; // 只有特殊符号

            return true;
        }

        // 解码搜索值
        decodeSearchValue(value) {
            try {
                return decodeURIComponent(value);
            } catch (error) {
                return value;
            }
        }

        // 首字母大写
        capitalizeFirst(str) {
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // 转义正则表达式特殊字符
        escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
    }

    // PlatformManager - 平台管理器
    class PlatformManager {
        constructor() {
            this.platforms = new Map();
            this.urlParser = new URLParser();

            // 常量定义
            this.MAX_CUSTOM_PLATFORMS = 30;
            this.MAX_ALIAS_COUNT = 10;
            this.MAX_ALIAS_LENGTH = 20;
            this.MAX_PLATFORM_NAME_LENGTH = 50;

            this.init();
        }

        async init() {
            this.loadBuiltinPlatforms();
            await this.loadCustomPlatforms();
        }

        // 加载内置平台
        loadBuiltinPlatforms() {
            // 使用SearchPlatformConfig获取内置平台
            const searchPlatformConfig = new window.SearchPlatformConfig();
            searchPlatformConfig.platforms.forEach(platform => {
                const platformData = {
                    id: platform.id,
                    name: platform.name,
                    icon: platform.icon,
                    url: platform.urls.normal,
                    searchUrl: platform.urls.search,
                    aliases: platform.aliases || [],
                    isCustom: false,
                    isBuiltin: true,
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                };
                this.platforms.set(platform.id, platformData);
            });
        }

        // 加载自定义平台
        async loadCustomPlatforms() {
            try {
                const customPlatforms = Storage.get('customPlatforms_v3') || [];
                customPlatforms.forEach(platform => {
                    this.platforms.set(platform.id, platform);
                });
            } catch (error) {
                console.error('加载自定义平台失败:', error);
            }
        }

        // 从URL添加平台 (主要功能)
        async addPlatformFromURL(url, options = {}) {
            try {
                // 检查数量限制
                this.checkPlatformLimit();

                // 解析URL (自动解析，不需要测试关键词)
                const parseResult = await this.urlParser.parseURL(url);

                if (!parseResult.success) {
                    throw new Error(parseResult.error);
                }

                // 检查重复
                if (this.isDuplicatePlatform(parseResult.domain)) {
                    throw new Error(`域名 ${parseResult.domain} 已存在`);
                }

                // 构建平台数据
                const platformData = {
                    id: window.MomentSearch.Utils.generateId(),
                    name: options.name || parseResult.name,
                    url: parseResult.template,
                    domain: parseResult.domain,
                    icon: parseResult.icon,
                    aliases: this.parseAliases(options.aliases || ''),
                    isCustom: true,
                    isBuiltin: false,
                    method: parseResult.method,
                    searchParam: parseResult.searchParam,
                    confidence: parseResult.confidence,
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                };

                // 验证数据
                const validation = this.validatePlatformData(platformData);
                if (!validation.valid) {
                    throw new Error(validation.error);
                }

                // 添加到平台列表
                this.platforms.set(platformData.id, platformData);

                // 保存到存储
                await this.savePlatforms();

                // 更新别名映射
                this.updateAliasMap();

                return platformData;
            } catch (error) {
                console.error('从URL添加平台失败:', error);
                throw error;
            }
        }

        // 检查平台数量限制
        checkPlatformLimit() {
            const customCount = Array.from(this.platforms.values())
                .filter(p => p.isCustom).length;

            if (customCount >= this.MAX_CUSTOM_PLATFORMS) {
                throw new Error(`最多只能添加${this.MAX_CUSTOM_PLATFORMS}个自定义平台`);
            }
        }

        // 检查重复平台
        isDuplicatePlatform(domain) {
            return Array.from(this.platforms.values())
                .some(p => p.domain === domain);
        }

        // 验证平台数据
        validatePlatformData(data) {
            if (!data.name || data.name.trim().length === 0) {
                return { valid: false, error: '平台名称不能为空' };
            }

            if (data.name.length > this.MAX_PLATFORM_NAME_LENGTH) {
                return { valid: false, error: `平台名称不能超过${this.MAX_PLATFORM_NAME_LENGTH}个字符` };
            }

            if (!data.url || !data.url.includes('{query}')) {
                return { valid: false, error: 'URL必须包含{query}占位符' };
            }

            try {
                // 验证URL格式
                const testUrl = data.url.replace('{query}', 'test');
                new URL(testUrl);
            } catch (error) {
                return { valid: false, error: 'URL格式无效' };
            }

            if (data.aliases && data.aliases.length > this.MAX_ALIAS_COUNT) {
                return { valid: false, error: `别名数量不能超过${this.MAX_ALIAS_COUNT}个` };
            }

            return { valid: true };
        }

        // 解析别名
        parseAliases(aliasString) {
            if (!aliasString) return [];

            return aliasString
                .split(/[,，\s]+/)
                .map(alias => alias ? alias.trim().toLowerCase() : '')
                .filter(alias => alias && alias.length > 0 && alias.length <= this.MAX_ALIAS_LENGTH)
                .slice(0, this.MAX_ALIAS_COUNT);
        }

        // 更新别名映射
        updateAliasMap() {
            // 清空现有的自定义平台映射
            const customAliases = [];
            ALIAS_MAP.forEach((platform, alias) => {
                if (platform.isCustom) {
                    customAliases.push(alias);
                }
            });
            customAliases.forEach(alias => ALIAS_MAP.delete(alias));

            // 重新构建自定义平台映射
            this.platforms.forEach(platform => {
                if (platform.aliases && Array.isArray(platform.aliases)) {
                    platform.aliases.forEach(alias => {
                        ALIAS_MAP.set(alias.toLowerCase(), platform);
                    });
                }
            });
        }

        // 删除平台（修复版：添加反向同步逻辑）
        async deletePlatform(id) {
            try {
                const platform = this.platforms.get(id);
                if (!platform) {
                    throw new Error('平台不存在');
                }

                if (platform.isBuiltin) {
                    throw new Error('不能删除内置平台');
                }

                // 反向同步：从主页删除对应的快捷方式（复用SearchDirectManager的方法）
                await this.callSearchDirectManagerSync(platform);

                // 从平台列表移除
                this.platforms.delete(id);

                // 保存到存储
                await this.savePlatforms();

                // 更新别名映射
                this.updateAliasMap();

                return true;
            } catch (error) {
                console.error('删除平台失败:', error);
                throw error;
            }
        }

        // 调用SettingsManager的反向同步方法（避免代码重复）
        async callSearchDirectManagerSync(platform) {
            try {
                const settingsManager = window.app?.settingsManager;
                if (settingsManager && settingsManager.syncDeletePlatformFromPageManager) {
                    console.log(`🔄 反向同步：从主页删除对应的快捷方式: ${platform.name}`);
                    await settingsManager.syncDeletePlatformFromPageManager(platform);
                } else {
                    console.warn('SettingsManager或syncDeletePlatformFromPageManager方法不存在，无法反向同步删除');
                }
            } catch (error) {
                console.error('❌ 反向同步删除主页快捷方式失败:', error);
            }
        }

        // 保存自定义平台
        async savePlatforms() {
            try {
                const customPlatforms = Array.from(this.platforms.values())
                    .filter(p => p.isCustom);
                Storage.set('customPlatforms_v3', customPlatforms);
            } catch (error) {
                console.error('保存平台失败:', error);
                throw error;
            }
        }

        // 获取所有平台（简化版，移除未使用的过滤选项）
        getAllPlatforms() {
            const platforms = Array.from(this.platforms.values());

            return platforms.sort((a, b) => {
                if (a.isBuiltin && !b.isBuiltin) return -1;
                if (!a.isBuiltin && b.isBuiltin) return 1;
                return a.name.localeCompare(b.name);
            });
        }

        // 获取平台总数（简化版，只返回总数）
        getPlatformCount() {
            return this.platforms.size;
        }

        // 提取域名（统一方法，避免重复）
        extractDomain(url) {
            try {
                const urlObj = new URL(url.replace('{query}', 'test'));
                return urlObj.hostname.replace(/^www\./, '');
            } catch (error) {
                return null;
            }
        }
    }

    // 时间管理器
    class TimeManager {
        // 位置计算常量
        static POSITION_CONSTANTS = {
            MIN_GAP: 20,           // 最小间距20px
            DEFAULT_TIME_TOP: 40,  // CSS默认位置，与CSS保持一致
            SEARCH_TOP: 220,       // 搜索容器固定位置
            MIN_TIME_TOP: 20       // 时间区域最小top值，避免超出页面顶部
        };

        constructor() {
            this.timeElement = document.getElementById('currentTime');
            this.dateElement = document.getElementById('currentDate');
            this.timeDisplay = document.getElementById('timeDisplay');

            // 初始化位置调整状态，CSS已设置正确的默认位置
            this._positionAdjusted = true;

            this.init();
        }

        init() {
            this.updateTime();
            setInterval(() => this.updateTime(), 1000);

            // 应用时间显示设置
            this.applySettings();
        }

        updateTime() {
            const now = new Date();

            // 获取时间设置
            const settings = Storage.get('settings_v3', {});
            const timeSettings = settings.time || {};

            // 更新时间 - 只在内容实际改变时更新DOM
            if (this.timeElement && timeSettings.showTime !== false) {
                const timeStr = this.formatTime(now, timeSettings);
                if (this.timeElement.textContent !== timeStr) {
                    this.timeElement.textContent = timeStr;
                }
            }

            // 更新日期（当有任何日期信息需要显示时）- 只在内容实际改变时更新DOM
            if (this.dateElement && this.hasDateInfo(timeSettings)) {
                const dateStr = this.formatDate(now, timeSettings);
                if (this.dateElement.textContent !== dateStr) {
                    this.dateElement.textContent = dateStr;
                }
            }
        }

        formatTime(date, settings) {
            const format24h = settings.format24h !== false;
            const showSeconds = settings.showSeconds || false;

            let hours = date.getHours();
            const minutes = date.getMinutes();
            const seconds = date.getSeconds();

            if (!format24h) {
                // 12小时制转换，但不显示AM/PM后缀
                hours = hours % 12;
                hours = hours ? hours : 12; // 0点显示为12点
            }

            const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}${showSeconds ? ':' + seconds.toString().padStart(2, '0') : ''}`;
            return timeStr;
        }

        // 检查是否有任何日期信息需要显示
        hasDateInfo(settings) {
            const showDate = settings.showDate !== false;
            const showWeekday = settings.showWeekday !== false;
            return showDate || showWeekday;
        }

        formatDate(date, settings) {
            const showDate = settings.showDate !== false; // 控制月日显示
            const showWeekday = settings.showWeekday !== false; // 控制星期显示

            let dateParts = [];

            // 月日部分（受"月日显示"控制）
            if (showDate) {
                const dateOptions = {
                    month: 'long',
                    day: 'numeric'
                };
                const dateStr = date.toLocaleDateString('zh-CN', dateOptions);
                dateParts.push(dateStr);
            }

            // 星期部分（受"星期显示"控制）
            if (showWeekday) {
                const weekdayOptions = {
                    weekday: 'long'
                };
                const weekdayStr = date.toLocaleDateString('zh-CN', weekdayOptions);
                dateParts.push(weekdayStr);
            }

            return dateParts.join('');
        }

        applySettings() {
            const settings = Storage.get('settings_v3', {});
            const timeSettings = settings.time || {};

            // 修正控制逻辑：
            // showTime - 控制整个时间展示区域（包括时间、日期、星期）
            // showDate - 控制月日显示
            // 时间显示控制
            const showTimeArea = timeSettings.showTime !== false; // 控制整个时间区域
            const showTimeOnly = true; // 时间部分始终显示（当时间区域显示时）
            const fontSizePercent = Math.max(30, timeSettings.fontSize || 50); // 百分比值，最小30%
            const color = timeSettings.color || '#ffffff';
            const fontFamily = timeSettings.fontFamily || 'system-ui';
            const fontWeight = timeSettings.fontWeight || 400;

            // 将百分比转换为实际像素值
            // 基础大小：时间48px，日期16px，50%对应这些基础值
            const baseTimeFontSize = 48;
            const baseDateFontSize = 16;
            const actualTimeFontSize = Math.round((fontSizePercent / 50) * baseTimeFontSize);
            const actualDateFontSize = Math.round((fontSizePercent / 50) * baseDateFontSize);

            // 控制整个时间显示区域
            if (this.timeDisplay) {
                this.timeDisplay.style.display = showTimeArea ? 'block' : 'none';
                this.timeDisplay.style.color = color;
            }

            // 控制时间部分（当时间区域显示时，时间部分始终显示）
            if (this.timeElement) {
                this.timeElement.style.display = (showTimeArea && showTimeOnly) ? 'block' : 'none';
                this.timeElement.style.fontSize = `${actualTimeFontSize}px`;
                this.timeElement.style.fontFamily = this.getFontFamily(fontFamily);
                this.timeElement.style.fontWeight = fontWeight;
            }

            // 控制日期部分（当时间区域显示且有任何日期信息需要显示时显示）
            if (this.dateElement) {
                const hasDateInfo = this.hasDateInfo(timeSettings);
                this.dateElement.style.display = (showTimeArea && hasDateInfo) ? 'block' : 'none';
                this.dateElement.style.fontSize = `${actualDateFontSize}px`;
                this.dateElement.style.fontFamily = this.getFontFamily(fontFamily);
                this.dateElement.style.fontWeight = fontWeight;
            }

            // 重置位置调整缓存，因为字体大小可能已改变
            this.resetPositionCache();

            // 动态调整时间显示区域位置以避免重叠
            this.adjustTimeDisplayPosition();
        }

        resetPositionCache() {
            this._positionAdjusted = false;
            this._cachedTimeHeight = null;
            this._fontSizeChanged = true; // 标记字体大小已改变
        }

        getFontFamily(fontValue) {
            const fontMap = {
                'arial': '"Arial", "Helvetica", sans-serif',
                'monospace': '"Consolas", "Monaco", "Courier New", monospace',
                'yahei': '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif',
                'times': '"Times New Roman", "Times", serif'
            };
            return fontMap[fontValue] || fontMap['arial'];
        }



        adjustTimeDisplayPosition() {
            // 在页面加载状态下不进行位置调整，避免视觉跳跃
            if (document.body.classList.contains('app-loading')) {
                return;
            }

            // 只在字体大小实际改变时才调整位置，避免不必要的调整
            if (!this._fontSizeChanged) {
                return;
            }

            // 立即执行位置计算，避免requestAnimationFrame导致的延迟
            const timeDisplay = document.querySelector('.time-display');
            const searchContainer = document.querySelector('.search-container');

            if (timeDisplay && searchContainer) {
                // 重新测量时间区域高度
                const timeRect = timeDisplay.getBoundingClientRect();
                this._cachedTimeHeight = timeRect.height;
                this._fontSizeChanged = false;

                const { MIN_GAP, DEFAULT_TIME_TOP, SEARCH_TOP, MIN_TIME_TOP } = TimeManager.POSITION_CONSTANTS;

                // 计算理想的时间区域top位置：搜索栏位置 - 时间高度 - 最小间距
                const idealTimeTop = SEARCH_TOP - this._cachedTimeHeight - MIN_GAP;

                // 确定最终的时间区域位置：默认位置和理想位置的较小值，但不小于最小值
                const newTimeTop = Math.max(MIN_TIME_TOP, Math.min(DEFAULT_TIME_TOP, idealTimeTop));

                // 只有当计算出的位置与CSS默认位置不同时才调整
                if (newTimeTop !== DEFAULT_TIME_TOP) {
                    timeDisplay.style.top = `${newTimeTop}px`;
                    console.log(`时间显示区域位置调整: ${DEFAULT_TIME_TOP}px → ${newTimeTop}px (时间高度: ${this._cachedTimeHeight}px)`);
                } else {
                    console.log(`时间显示区域位置保持默认: ${DEFAULT_TIME_TOP}px (时间高度: ${this._cachedTimeHeight}px)`);
                }

                this._positionAdjusted = true;
            }
        }
    }

    // 快捷键工具类
    class ShortcutUtils {
        // 检查快捷键是否被按下（支持两种模式：严格模式和兼容模式）
        static isShortcutPressed(event, shortcutKey, strictMode = false) {
            if (!shortcutKey) return false;

            if (strictMode && shortcutKey.includes('+')) {
                // 严格模式：用于模式切换快捷键（必须有修饰键）
                const parts = shortcutKey.split('+');
                const modifiers = parts.slice(0, -1);
                const key = parts[parts.length - 1];

                // 检查修饰键
                const hasCtrl = modifiers.includes('Ctrl') ? (event.ctrlKey || event.metaKey) : !event.ctrlKey && !event.metaKey;
                const hasAlt = modifiers.includes('Alt') ? event.altKey : !event.altKey;
                const hasShift = modifiers.includes('Shift') ? event.shiftKey : !event.shiftKey;

                if (!hasCtrl || !hasAlt || !hasShift) return false;

                // 检查主键
                const normalizedKey = this.normalizeEventKey(event.key);
                return normalizedKey === key.toUpperCase();
            } else {
                // 兼容模式：用于平台切换快捷键（可以是单键或组合键）
                const parts = shortcutKey.split('+').map(part => part.trim());
                const key = parts[parts.length - 1].toUpperCase();
                const modifiers = parts.slice(0, -1).map(mod => mod.toLowerCase());

                // 检查修饰键
                for (const modifier of modifiers) {
                    switch (modifier) {
                        case 'ctrl':
                            if (!event.ctrlKey) return false;
                            break;
                        case 'alt':
                            if (!event.altKey) return false;
                            break;
                        case 'shift':
                            if (!event.shiftKey) return false;
                            break;
                        default:
                            return false;
                    }
                }

                // 检查主键
                const eventKey = this.normalizeEventKey(event.key);
                return eventKey === key;
            }
        }

        // 标准化事件按键名称
        static normalizeEventKey(key) {
            const keyMap = {
                ' ': 'SPACE',
                'ArrowUp': 'UP',
                'ArrowDown': 'DOWN',
                'ArrowLeft': 'LEFT',
                'ArrowRight': 'RIGHT',
                'Escape': 'ESC'
            };

            return keyMap[key] || key.toUpperCase();
        }
    }

    // 搜索模式管理器
    class SearchModeManager {
        constructor() {
            this.currentMode = 'normal'; // 'normal' | 'quick'
            this.modes = {
                normal: {
                    name: '常规搜索',
                    icon: '🔍',
                    description: '选择平台后搜索',
                    searchMethod: 'platform-select',
                    defaultPlatforms: ['google', 'bing', 'baidu']
                },
                quick: {
                    name: '快捷搜索',
                    icon: '⚡',
                    description: '使用别名直达搜索',
                    searchMethod: 'alias-match',
                    defaultPlatforms: ['google', 'bing', 'baidu']
                }
            };

            this.init();
        }

        init() {
            this.loadSavedMode();
            this.bindShortcuts();
            this.updateSearchPlaceholder();
        }

        // 加载保存的模式（默认为常规搜索）
        loadSavedMode() {
            this.currentMode = 'normal'; // 始终默认为常规搜索模式
        }

        // 保存当前模式（优化：减少重复的设置读取）
        saveMode() {
            this.updateSetting('search.currentMode', this.currentMode);
        }

        // 统一的设置更新方法
        updateSetting(path, value) {
            const settings = Storage.get('settings_v3', {});
            const keys = path.split('.');
            let current = settings;

            // 创建嵌套对象路径
            for (let i = 0; i < keys.length - 1; i++) {
                if (!current[keys[i]]) current[keys[i]] = {};
                current = current[keys[i]];
            }

            current[keys[keys.length - 1]] = value;
            Storage.set('settings_v3', settings);
        }

        // 更新搜索框占位符
        updateSearchPlaceholder() {
            // 使用统一的DOM管理器
            if (!this.domManager) {
                this.domManager = new DOMManager();
            }

            const searchInput = this.domManager.get('searchInput');
            const platformName = this.domManager.get('platformName');

            if (searchInput && platformName) {
                if (this.currentMode === 'normal') {
                    searchInput.placeholder = `在 ${platformName.textContent} 中搜索...`;
                } else {
                    searchInput.placeholder = '输入别名和关键词，如 "g 搜索内容"...';
                }
            }
        }

        // 绑定平台选择器事件
        bindPlatformSelector() {
            const { platformGrid } = this.domElements;
            if (!platformGrid) return;

            platformGrid.addEventListener('click', (e) => {
                const platformItem = e.target.closest('.platform-item');
                if (!platformItem) return;

                const platformId = platformItem.dataset.platform;

                // 更新选中状态
                platformGrid.querySelectorAll('.platform-item').forEach(item => {
                    item.classList.remove('selected');
                });
                platformItem.classList.add('selected');

                // 通知搜索管理器
                if (window.app?.searchManager) {
                    window.app.searchManager.setSelectedPlatform(platformId);
                }
            });
        }

        // 绑定快捷键
        bindShortcuts() {
            document.addEventListener('keydown', (e) => {
                const settings = Storage.get('settings_v3', {});
                const shortcutKey = settings.shortcuts?.modeSwitch || 'Ctrl+Q';

                if (ShortcutUtils.isShortcutPressed(e, shortcutKey, true)) {
                    e.preventDefault();
                    this.toggleMode();
                }
            });
        }



        // 更新快捷键（由设置页面调用）
        updateShortcutKey(newShortcut) {
            const settings = Storage.get('settings_v3', {});
            if (!settings.shortcuts) settings.shortcuts = {};
            settings.shortcuts.modeSwitch = newShortcut;
            Storage.set('settings_v3', settings);

            console.log(`快捷键已更新为: ${newShortcut}`);
        }

        // 切换模式
        switchMode(mode) {
            if (this.modes[mode] && this.currentMode !== mode) {
                this.currentMode = mode;
                this.saveMode();
                this.updateSearchPlaceholder();

                // 优化：先切换页面，再异步更新搜索相关设置，避免双重渲染
                this.switchToModePages(mode);

                // 使用Promise.resolve()确保在下一个微任务中执行，避免与页面切换冲突
                Promise.resolve().then(() => {
                    this.updateSearchBehavior();
                    this.updatePlatformList();
                });
            }
        }

        // 切换模式（在当前两种模式间切换）
        toggleMode() {
            const newMode = this.currentMode === 'normal' ? 'quick' : 'normal';
            this.switchMode(newMode);
        }

        // 切换到对应模式的页面
        switchToModePages(mode) {
            if (window.app && window.app.pageManager) {
                const pageManager = window.app.pageManager;
                const modePages = pageManager.getPagesByMode(mode);

                if (modePages.length > 0) {
                    // 切换到该模式的第一个页面
                    pageManager.switchToPage(modePages[0].id);
                }
            }
        }

        // 更新搜索行为
        updateSearchBehavior() {
            // 通知搜索管理器模式已改变
            if (window.app?.searchManager) {
                window.app.searchManager.setSearchMode(this.currentMode);
            }
        }

        // 更新平台列表
        updatePlatformList() {
            // 通知搜索管理器重新初始化平台列表
            if (window.app?.searchManager) {
                window.app.searchManager.initializePlatformList();
            }
        }

        // 模式改变回调
        onModeChange(mode) {
            console.log(`搜索模式切换到: ${this.modes[mode].name}`);

            // 触发自定义事件
            const event = new CustomEvent('searchModeChanged', {
                detail: { mode, modeInfo: this.modes[mode] }
            });
            document.dispatchEvent(event);
        }





        // 获取当前模式
        getCurrentMode() {
            return this.currentMode;
        }

        // 获取模式信息
        getModeInfo(mode = this.currentMode) {
            return this.modes[mode];
        }
    }

    // 搜索管理器
    class SearchManager {
        constructor() {
            // 使用SearchPlatformConfig管理平台配置
            this.platformConfig = new window.SearchPlatformConfig();
            this.platforms = this.platformConfig.platforms; // 存储平台列表
            this.currentPlatform = 'all';

            // 初始化平台管理器
            this.platformManager = new PlatformManager();

            // 搜索模式相关
            this.searchMode = 'normal'; // 'normal' | 'quick'
            this.selectedPlatform = 'google'; // 常规模式下选中的平台

            // 弹窗状态管理
            this.isAddPlatformModalOpen = false; // 跟踪全部快捷搜索平台弹窗状态
            this.hideDropdownTimer = null; // hover延迟隐藏计时器
            this.eventsbound = false; // 防止重复绑定事件标志

            // DOM元素缓存
            this.domElements = {
                searchInput: document.getElementById('searchInput'),
                platformSelector: document.getElementById('platformSelector'),
                platformFavicon: document.getElementById('platformFavicon'),
                platformEmoji: document.getElementById('platformEmoji'),
                platformDropdown: document.getElementById('platformDropdown'),
                platformList: document.getElementById('platformList'),
                platformSearchInput: document.getElementById('platformSearchInput')
            };

            // 初始化图标管理器
            this.faviconManager = new FaviconManager();

            // 初始化平台配置（复用实例，避免重复创建）
            this.platformConfig = new SearchPlatformConfig();

            // 当前选中的平台
            this.currentSelectedPlatform = null;

            this.init();
        }

        init() {
            this.bindEvents();
            this.bindPlatformSwitchShortcut();
            this.updatePlatformIndicator(this.getCurrentPlatform());
        }

        bindEvents() {
            const { searchInput, platformSelector, platformDropdown, platformSearchInput } = this.domElements;
            if (!searchInput || !platformSelector) return;

            // 防止重复绑定事件
            if (this.eventsbound) return;
            this.eventsbound = true;

            // 回车键搜索
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.executeSearch();
                }
            });

            // 平台选择器hover触发（优化：改为悬停触发）
            platformSelector.addEventListener('mouseenter', () => {
                this.showPlatformDropdown();
            });

            // 平台选择器和下拉框区域的鼠标离开事件
            platformSelector.addEventListener('mouseleave', () => {
                // 延迟隐藏，给用户时间移动到下拉框
                this.hideDropdownTimer = setTimeout(() => {
                    this.hidePlatformDropdown();
                }, 200);
            });

            // 下拉框鼠标进入时取消隐藏
            if (platformDropdown) {
                platformDropdown.addEventListener('mouseenter', () => {
                    if (this.hideDropdownTimer) {
                        clearTimeout(this.hideDropdownTimer);
                        this.hideDropdownTimer = null;
                    }
                });

                platformDropdown.addEventListener('mouseleave', () => {
                    this.hidePlatformDropdown();
                });
            }

            // 保留点击事件作为备用触发方式
            platformSelector.addEventListener('click', (e) => {
                e.stopPropagation();
                this.togglePlatformDropdown();
            });

            // 平台搜索输入
            if (platformSearchInput) {
                platformSearchInput.addEventListener('input', (e) => {
                    this.filterPlatforms(e.target.value);
                });
            }

            // 点击其他地方关闭下拉框
            document.addEventListener('click', (e) => {
                // 如果全部快捷搜索平台弹窗打开，不处理点击外部隐藏逻辑
                if (this.isAddPlatformModalOpen) {
                    return;
                }

                // 正常的点击外部隐藏逻辑
                if (!platformDropdown.contains(e.target) && !platformSelector.contains(e.target)) {
                    this.hidePlatformDropdown();
                }
            });

            // 输入时智能匹配 - 使用防抖优化
            const debouncedMatch = Utils.debounce((value) => {
                this.handleSmartMatch(value);
            }, 300);

            searchInput.addEventListener('input', (e) => {
                const value = e.target.value;
                debouncedMatch(value);
            });

            // 初始化平台列表
            this.initializePlatformList();

            // 绑定多选功能事件
            this.bindMultiSelectEvents();
        }

        // 初始化平台列表
        async initializePlatformList() {
            // 从localStorage加载用户的平台选择
            const userPlatforms = this.loadUserPlatformSelection();

            // 将用户选择的平台ID转换为完整的平台对象
            const platforms = await this.convertUserPlatformsToFullObjects(userPlatforms);

            await this.renderPlatformList(platforms);

            // 设置默认选中的平台
            this.selectPlatform(platforms[0]);
        }

        // 将用户选择的平台ID转换为完整的平台对象
        async convertUserPlatformsToFullObjects(userPlatforms) {
            const fullPlatforms = [];

            for (const userPlatform of userPlatforms) {
                // 使用统一的平台获取方法
                const platform = this.getPlatformInfo(userPlatform.id);

                if (platform) {
                    fullPlatforms.push({
                        id: platform.id,
                        name: platform.name,
                        icon: platform.icon,
                        url: platform.urls?.normal || platform.url,
                        searchUrl: platform.urls?.search || platform.searchUrl,
                        faviconUrl: platform.faviconUrl || `https://favicon.im/${new URL(platform.urls?.normal || platform.url).hostname}`
                    });
                } else {
                    console.warn(`平台 ${userPlatform.id} 既不在预设配置中，也不在用户新增平台中，跳过`);
                }
            }

            // 如果没有有效的平台，返回默认平台
            if (fullPlatforms.length === 0) {
                console.log('没有有效的用户平台，使用默认平台');
                const defaultPlatforms = this.getDefaultPlatforms();
                return this.convertUserPlatformsToFullObjects(defaultPlatforms);
            }

            return fullPlatforms;
        }

        // 统一的平台获取方法：支持预设平台和用户新增平台
        getPlatformInfo(platformId) {
            // 首先尝试从SearchPlatformConfig获取平台信息（预设平台）
            let platform = this.platformConfig.getPlatform(platformId);

            // 如果不是预设平台，尝试从用户新增平台获取
            if (!platform) {
                platform = this.getUserAddedPlatform(platformId);
            }

            return platform;
        }

        // 获取用户新增的平台信息
        getUserAddedPlatform(platformId) {
            try {
                const pageManager = window.app?.pageManager;
                if (!pageManager) {
                    console.log('PageManager不可用，无法获取用户新增平台');
                    return null;
                }

                // 遍历所有快捷搜索页面
                const quickSearchPages = pageManager.pages.filter(page => page.mode === 'quick');

                for (const page of quickSearchPages) {
                    if (page.shortcuts && page.shortcuts.length > 0) {
                        // 查找匹配的快捷方式
                        const shortcut = page.shortcuts.find(s => s.id === platformId);

                        if (shortcut && shortcut.searchMode === 'search' && shortcut.searchUrl) {
                            // 转换为平台格式
                            const userPlatform = {
                                id: shortcut.id,
                                name: shortcut.name,
                                icon: shortcut.icon || '🔍',
                                urls: {
                                    normal: shortcut.url,
                                    search: shortcut.searchUrl
                                },
                                faviconUrl: shortcut.faviconUrl,
                                isFromShortcut: true,
                                description: `来自快捷搜索页面: ${page.name}`
                            };

                            console.log(`找到用户新增平台: ${platformId} (${shortcut.name})`);
                            return userPlatform;
                        }
                    }
                }

                console.log(`未找到用户新增平台: ${platformId}`);
                return null;
            } catch (error) {
                console.error('获取用户新增平台失败:', error);
                return null;
            }
        }

        // 获取当前模式的平台列表（修复版：包含PlatformManager中的自定义平台）
        getModePlatforms() {
            // 优先从DOM获取当前显示的平台（实时状态）
            const platformItems = document.querySelectorAll('.platform-item[data-platform-id]:not([data-action="add-platform"])');

            if (platformItems.length > 0) {
                // 从DOM中获取当前显示的平台
                const platforms = Array.from(platformItems).map(item => {
                    const platformId = item.dataset.platformId;
                    const platformName = item.dataset.platformName;
                    const isUserAdded = item.dataset.isUserAdded === 'true';

                    // 使用统一的平台获取方法
                    let platform = this.getPlatformInfo(platformId);

                    // 如果仍然找不到，尝试从DOM数据构建基本平台信息
                    if (!platform) {
                        const platformUrl = item.dataset.platformUrl;
                        const platformSearchUrl = item.dataset.platformSearchUrl;

                        if (platformUrl && platformSearchUrl) {
                            platform = {
                                id: platformId,
                                name: platformName,
                                icon: '🔍',
                                urls: {
                                    normal: platformUrl,
                                    search: platformSearchUrl
                                },
                                isFromShortcut: isUserAdded
                            };
                        }
                    }

                    if (platform) {
                        return {
                            id: platform.id,
                            name: platform.name,
                            icon: platform.icon,
                            url: platform.urls?.normal || platform.url,
                            searchUrl: platform.urls?.search || platform.searchUrl,
                            faviconUrl: platform.faviconUrl || `https://favicon.im/${new URL(platform.urls?.normal || platform.url).hostname}`
                        };
                    }

                    return null;
                }).filter(platform => platform !== null);

                console.log(`从平台下拉框获取到 ${platforms.length} 个用户选择的平台`);
                return platforms;
            }

            // 新增：从PlatformManager获取所有平台（包括自定义平台）
            try {
                const platformManager = window.app?.searchManager?.platformManager;
                if (platformManager) {
                    const allPlatforms = platformManager.getAllPlatforms();
                    if (allPlatforms && allPlatforms.length > 0) {
                        const formattedPlatforms = allPlatforms.map(platform => ({
                            id: platform.id,
                            name: platform.name,
                            icon: platform.icon || '🔍',
                            url: platform.url,
                            searchUrl: platform.searchUrl || platform.url,
                            faviconUrl: platform.faviconUrl || `https://favicon.im/${this.extractDomain(platform.url)}`
                        }));

                        console.log(`从PlatformManager获取到 ${formattedPlatforms.length} 个平台（包含自定义平台）`);
                        return formattedPlatforms;
                    }
                }
            } catch (error) {
                console.warn('从PlatformManager获取平台失败:', error);
            }

            // 后备方案1：从localStorage获取用户保存的平台选择
            try {
                const userPlatforms = Storage.get('userSelectedPlatforms_v3');
                if (userPlatforms && Array.isArray(userPlatforms) && userPlatforms.length > 0) {
                    console.log(`从localStorage获取到 ${userPlatforms.length} 个用户保存的平台`);

                    // 转换为完整的平台对象
                    const fullPlatforms = userPlatforms.map(userPlatform => {
                        const platform = this.platformConfig.getPlatform(userPlatform.id);
                        if (platform) {
                            return {
                                id: platform.id,
                                name: platform.name,
                                icon: platform.icon,
                                url: platform.urls?.normal || platform.url,
                                searchUrl: platform.urls?.search || platform.searchUrl,
                                faviconUrl: platform.faviconUrl || `https://favicon.im/${new URL(platform.urls?.normal || platform.url).hostname}`
                            };
                        }
                        return null;
                    }).filter(platform => platform !== null);

                    if (fullPlatforms.length > 0) {
                        return fullPlatforms;
                    }
                }
            } catch (error) {
                console.warn('从localStorage加载平台失败:', error);
            }

            // 后备方案2：使用默认平台配置
            console.log('使用默认平台配置');
            const defaultPlatforms = this.getDefaultPlatforms();

            return defaultPlatforms.map(defaultPlatform => {
                const platform = this.platformConfig.getPlatform(defaultPlatform.id);
                if (platform) {
                    return {
                        id: platform.id,
                        name: platform.name,
                        icon: platform.icon,
                        url: platform.urls?.normal || platform.url,
                        searchUrl: platform.urls?.search || platform.searchUrl,
                        faviconUrl: platform.faviconUrl || `https://favicon.im/${new URL(platform.urls?.normal || platform.url).hostname}`
                    };
                }
                return null;
            }).filter(platform => platform !== null);
        }

        // 渲染平台列表
        async renderPlatformList(platforms) {
            const { platformList } = this.domElements;
            if (!platformList) return;

            const platformItems = await Promise.all(platforms.map(async (platform) => {
                // 使用公共方法获取图标HTML
                const iconHtml = await this.getPlatformIconHtml(platform);

                return `
                    <div class="platform-item" data-platform-id="${platform.id}" data-platform-name="${platform.name}" data-search-url="${platform.searchUrl || platform.url}">
                        <div class="platform-item-checkbox" style="display: none;">
                            <input type="checkbox" class="platform-checkbox" data-platform-id="${platform.id}">
                        </div>
                        <div class="platform-icon-container">
                            ${iconHtml}
                            ${!platform.isFromShortcut ? `<div class="platform-item-delete" data-action="delete" title="删除此平台">×</div>` : ''}
                        </div>
                        <div class="platform-item-name">${platform.name}</div>
                    </div>
                `;
            }));

            // 添加"添加"按钮
            const addButton = `
                <div class="platform-item platform-add-item" data-action="add-platform">
                    <div class="platform-item-icon">
                        <span>+</span>
                    </div>
                    <div class="platform-item-name">添加</div>
                </div>
            `;

            platformList.innerHTML = platformItems.join('') + addButton;

            // 绑定点击事件
            platformList.addEventListener('click', (e) => {
                // 检查是否点击了删除按钮
                if (e.target.classList.contains('platform-item-delete')) {
                    e.stopPropagation(); // 阻止事件冒泡
                    const platformItem = e.target.closest('.platform-item');
                    if (platformItem) {
                        const platformId = platformItem.dataset.platformId;
                        this.deletePlatformWithAnimation(platformItem, platformId);
                    }
                    return;
                }

                // 检查是否点击了添加按钮
                const platformItem = e.target.closest('.platform-item');
                if (platformItem && platformItem.dataset.action === 'add-platform') {
                    e.stopPropagation();
                    this.showAddPlatformModal();
                    return;
                }

                // 处理平台选择
                if (platformItem) {
                    // 检查是否在多选模式下
                    const isMultiSelectMode = platformItem.classList.contains('multi-select-mode');

                    if (isMultiSelectMode) {
                        // 多选模式：切换选中状态和复选框
                        const checkbox = platformItem.querySelector('.platform-checkbox');
                        if (checkbox) {
                            checkbox.checked = !checkbox.checked;

                            // 更新视觉反馈
                            if (checkbox.checked) {
                                platformItem.classList.add('selected');
                            } else {
                                platformItem.classList.remove('selected');
                            }

                            this.updateSelectedCount();
                        }
                    } else {
                        // 单选模式：选择平台并关闭下拉框
                        const platformId = platformItem.dataset.platformId;
                        const platform = platforms.find(p => p.id === platformId);
                        if (platform) {
                            this.selectPlatform(platform);
                            this.hidePlatformDropdown();
                        }
                    }
                }
            });
        }

        // 选择平台
        selectPlatform(platform) {
            this.currentSelectedPlatform = platform;
            this.selectedPlatform = platform.id;

            // 更新UI显示（现在是同步的，使用预加载的缓存）
            this.updatePlatformDisplay(platform);

            // 更新搜索框占位符
            const { searchInput } = this.domElements;
            if (searchInput) {
                searchInput.placeholder = `在 ${platform.name} 中搜索...`;
            }
        }

        // 更新平台显示 - 优化版本，使用预加载的缓存图标
        updatePlatformDisplay(platform) {
            const { platformFavicon, platformEmoji } = this.domElements;

            if (!platformFavicon || !platformEmoji) {
                console.warn('平台图标元素未找到');
                return;
            }

            if (platform.id === 'all') {
                // 全平台搜索直接显示emoji
                this.setPlatformIcon(platformFavicon, platformEmoji, {
                    icon: platform.icon,
                    name: platform.name
                });
            } else {
                // 使用与其他地方一致的URL获取逻辑
                const platformUrl = platform.urls?.normal || platform.url || `https://${platform.name.toLowerCase()}.com`;

                // 优先从缓存获取图标（已在页面初始化时预加载）
                const cached = this.faviconManager.iconCacheManager.getCachedIcon(platformUrl);

                if (cached) {
                    // 使用缓存的图标，立即显示
                    console.log(`从缓存获取平台图标: ${platform.name}`);
                    this.setPlatformIcon(platformFavicon, platformEmoji, {
                        faviconUrl: cached.url,
                        icon: platform.icon,
                        name: platform.name
                    });
                } else {
                    // 缓存未命中，显示emoji并异步获取图标
                    this.setPlatformIcon(platformFavicon, platformEmoji, {
                        icon: platform.icon,
                        name: platform.name
                    });

                    // 异步获取图标（备用方案）
                    (async () => {
                        try {
                            const faviconUrl = await this.faviconManager.getFavicon(platformUrl);
                            console.log(`获取新平台图标: ${platform.name}`);

                            if (faviconUrl && faviconUrl !== this.faviconManager.getDefaultIcon()) {
                                this.setPlatformIcon(platformFavicon, platformEmoji, {
                                    faviconUrl: faviconUrl,
                                    icon: platform.icon,
                                    name: platform.name
                                });
                            }
                        } catch (error) {
                            console.warn(`获取平台图标失败: ${platform.name}`, error);
                        }
                    })();
                }
            }
        }

        // 切换平台下拉框显示
        togglePlatformDropdown() {
            const { platformDropdown } = this.domElements;
            if (!platformDropdown) return;

            const isVisible = platformDropdown.style.display !== 'none';
            if (isVisible) {
                this.hidePlatformDropdown();
            } else {
                this.showPlatformDropdown();
            }
        }

        // 显示平台下拉框
        showPlatformDropdown() {
            const { platformDropdown, platformSearchInput } = this.domElements;
            if (!platformDropdown) return;

            platformDropdown.style.display = 'block';

            // 聚焦搜索框
            if (platformSearchInput) {
                setTimeout(() => platformSearchInput.focus(), 100);
            }
        }

        // 隐藏平台下拉框
        hidePlatformDropdown() {
            const { platformDropdown, platformSearchInput } = this.domElements;
            if (!platformDropdown) return;

            platformDropdown.style.display = 'none';

            // 清空搜索框
            if (platformSearchInput) {
                platformSearchInput.value = '';
                this.filterPlatforms('');
            }
        }

        // 过滤平台
        filterPlatforms(query) {
            const { platformList } = this.domElements;
            if (!platformList) return;

            const items = platformList.querySelectorAll('.platform-item');
            const lowerQuery = query.toLowerCase();

            items.forEach(item => {
                const platformName = item.dataset.platformName;
                // 跳过没有platformName的项目（如添加按钮）
                if (!platformName) {
                    item.style.display = 'flex'; // 始终显示添加按钮
                    return;
                }

                const name = platformName.toLowerCase();
                const isMatch = name.includes(lowerQuery);
                item.style.display = isMatch ? 'flex' : 'none';
            });
        }

        // ========== 平台切换功能 ==========

        // 绑定平台切换快捷键
        bindPlatformSwitchShortcut() {
            document.addEventListener('keydown', (e) => {
                const settings = Storage.get('settings_v3', {});
                const shortcutKey = settings.shortcuts?.platformSwitch || 'Tab';

                // 只在搜索输入框获得焦点时生效
                const { searchInput } = this.domElements;
                if (!searchInput || document.activeElement !== searchInput) {
                    return;
                }

                if (ShortcutUtils.isShortcutPressed(e, shortcutKey)) {
                    e.preventDefault();
                    console.log(`平台切换快捷键触发: ${shortcutKey}`);
                    this.switchToNextPlatform();
                }
            });
        }



        // 切换到下一个平台
        switchToNextPlatform() {
            // 动态获取当前平台下拉框的实时平台列表
            const currentPlatforms = this.getModePlatforms();

            if (!currentPlatforms || currentPlatforms.length === 0) {
                console.warn('没有可用的平台进行切换');
                return;
            }

            // 获取当前平台在列表中的索引
            const currentIndex = currentPlatforms.findIndex(p => p.id === this.selectedPlatform);

            // 计算下一个平台的索引（循环切换）
            const nextIndex = (currentIndex + 1) % currentPlatforms.length;
            const nextPlatform = currentPlatforms[nextIndex];

            if (nextPlatform) {
                console.log(`平台切换: ${this.selectedPlatform} → ${nextPlatform.id} (${nextPlatform.name})`);
                this.selectPlatform(nextPlatform);
            }
        }

        // 更新平台切换快捷键（由设置页面调用）
        updatePlatformSwitchShortcut(newShortcut) {
            const settings = Storage.get('settings_v3', {});
            if (!settings.shortcuts) settings.shortcuts = {};
            settings.shortcuts.platformSwitch = newShortcut;
            Storage.set('settings_v3', settings);

            console.log(`平台切换快捷键已更新为: ${newShortcut}`);
        }

        // ========== 搜索执行功能 ==========

        executeSearch() {
            const { searchInput } = this.domElements;
            const query = searchInput.value.trim();
            if (!query) return;

            // 检查是否在多选模式下
            const selectedCheckboxes = document.querySelectorAll('.platform-checkbox:checked');
            if (selectedCheckboxes.length > 0) {
                // 多选模式：执行多平台搜索
                this.executeMultiPlatformSearchWithQuery(query);
            } else {
                // 单选模式：执行正常搜索
                if (this.searchMode === 'normal') {
                    this.executeNormalSearch(query);
                } else {
                    this.executeQuickSearch(query);
                }
            }

            // 清空搜索框
            searchInput.value = '';

            // 保持用户选择的搜索引擎不变，不进行重置
        }

        // 使用指定查询词执行多平台搜索
        executeMultiPlatformSearchWithQuery(searchQuery) {
            const selectedCheckboxes = document.querySelectorAll('.platform-checkbox:checked');

            if (selectedCheckboxes.length === 0) {
                console.warn('请至少选择一个搜索平台');
                return;
            }

            // 获取当前平台列表数据
            const platforms = this.getModePlatforms();

            // 为每个选中的平台打开新标签页
            selectedCheckboxes.forEach(checkbox => {
                const platformId = checkbox.dataset.platformId;
                const platform = platforms.find(p => p.id === platformId);

                if (platform && platform.searchUrl) {
                    let finalUrl;
                    if (platform.searchUrl.includes('{query}')) {
                        finalUrl = platform.searchUrl.replace('{query}', encodeURIComponent(searchQuery));
                    } else if (platform.searchUrl.includes('{keyword}')) {
                        finalUrl = platform.searchUrl.replace('{keyword}', encodeURIComponent(searchQuery));
                    } else if (platform.searchUrl.includes('?')) {
                        finalUrl = platform.searchUrl + encodeURIComponent(searchQuery);
                    } else {
                        finalUrl = platform.searchUrl + '?q=' + encodeURIComponent(searchQuery);
                    }

                    // 打开新标签页
                    window.open(finalUrl, '_blank');
                    console.log(`在 ${platform.name} 中搜索: ${searchQuery} -> ${finalUrl}`);
                } else {
                    console.warn(`平台 ${platformId} 没有搜索URL配置`);
                }
            });

            // 显示成功提示
            console.log(`✅ 已在 ${selectedCheckboxes.length} 个平台中搜索: ${searchQuery}`);

            // 隐藏下拉框
            this.hidePlatformDropdown();

            // 自动退出多选模式，恢复到正常状态
            this.exitMultiSelectMode();
        }

        handleSmartMatch(query) {
            const words = query.toLowerCase().split(' ');
            let matchedPlatform = null;

            for (const word of words) {
                if (this.aliasMap.has(word)) {
                    matchedPlatform = this.aliasMap.get(word);
                    this.currentPlatform = matchedPlatform.id;
                    break;
                }
            }

            // 只有在明确匹配到平台关键词时才切换平台
            if (matchedPlatform) {
                this.updatePlatformIndicator(matchedPlatform);
            }
            // 移除自动重置逻辑，保持用户选择的平台不变
        }

        getCurrentPlatform() {
            // 使用PlatformManager统一查找平台
            if (this.platformManager && this.platformManager.platforms) {
                const platform = this.platformManager.platforms.get(this.currentPlatform);
                if (platform) {
                    return platform;
                }
            }

            // 默认返回全平台搜索（从this.platforms获取）
            return this.platforms[0];
        }

        executeSinglePlatformSearch(query, platform) {
            // 根据搜索模式决定使用哪个URL
            let searchUrl;

            // 优先使用searchUrl进行搜索，如果没有则使用url
            if (platform.searchUrl) {
                // 使用专门的搜索URL模板，支持占位符替换
                if (platform.searchUrl.includes('{query}') || platform.searchUrl.includes('{keyword}')) {
                    searchUrl = platform.searchUrl
                        .replace('{query}', encodeURIComponent(query))
                        .replace('{keyword}', encodeURIComponent(query));
                } else {
                    // 如果没有占位符，直接拼接（兼容旧格式）
                    searchUrl = platform.searchUrl + encodeURIComponent(query);
                }
            } else if (platform.url && (platform.url.includes('{query}') || platform.url.includes('{keyword}'))) {
                // 兼容旧格式：URL中包含占位符
                searchUrl = platform.url
                    .replace('{query}', encodeURIComponent(query))
                    .replace('{keyword}', encodeURIComponent(query));
            } else {
                // 如果都没有，直接打开网站首页（这种情况应该很少见）
                searchUrl = platform.url;
            }

            window.open(searchUrl, '_blank');
        }



        async updatePlatformIndicator(platform) {
            // 使用新的selectPlatform方法
            await this.selectPlatform(platform);
        }

        // 设置搜索模式
        setSearchMode(mode) {
            this.searchMode = mode;
        }

        // 设置选中的平台
        setSelectedPlatform(platformName, platformUrl, options = {}) {
            // 根据平台名称或URL查找对应的平台ID
            const platform = this.platforms.find(p =>
                p.name === platformName ||
                p.url === platformUrl ||
                p.searchUrl === platformUrl
            );

            if (platform) {
                this.selectedPlatform = platform.id;

                // 如果是快捷搜索模式，保存搜索配置
                if (options.searchMode === 'search') {
                    this.selectedPlatformConfig = {
                        ...platform,
                        searchUrl: options.searchUrl || platform.searchUrl,
                        displayUrl: options.displayUrl || platform.url
                    };
                }
            } else {
                // 如果找不到匹配的平台，创建一个临时平台
                this.selectedPlatform = 'custom';
                this.customPlatform = {
                    id: 'custom',
                    name: platformName,
                    url: options.displayUrl || platformUrl,
                    searchUrl: options.searchUrl || platformUrl
                };

                // 保存临时平台配置
                if (options.searchMode === 'search') {
                    this.selectedPlatformConfig = this.customPlatform;
                }
            }
        }

        // 常规搜索
        executeNormalSearch(query) {
            const platform = this.getPlatformById(this.selectedPlatform);
            this.executeSinglePlatformSearch(query, platform);
        }

        // 快捷搜索
        executeQuickSearch(query) {
            // 如果有选中的平台配置（快捷搜索模式下选择了平台）
            if (this.selectedPlatformConfig) {
                this.executeSinglePlatformSearch(query, this.selectedPlatformConfig);
                return;
            }

            // 否则使用别名匹配逻辑
            const platform = this.matchPlatform(query);

            if (platform) {
                const actualQuery = query.substring(query.indexOf(' ') + 1);
                this.executeSinglePlatformSearch(actualQuery, platform);
            } else {
                // 如果没有匹配到平台，使用默认平台（Google）搜索
                const defaultPlatform = this.getPlatformById('google');
                this.executeSinglePlatformSearch(query, defaultPlatform);
            }
        }

        // 匹配平台（统一的平台匹配逻辑）
        matchPlatform(query) {
            const words = query.toLowerCase().split(' ');
            for (const word of words) {
                if (this.aliasMap.has(word)) {
                    return this.aliasMap.get(word);
                }
            }
            return null;
        }

        // 根据ID获取平台（统一的平台获取逻辑）
        getPlatformById(platformId) {
            if (platformId === 'custom' && this.customPlatform) {
                return this.customPlatform;
            }

            // 使用统一的平台获取方法，支持预设平台和用户新增平台
            const platform = this.getPlatformInfo(platformId);
            if (platform) {
                // 转换为搜索执行需要的格式
                return {
                    id: platform.id,
                    name: platform.name,
                    icon: platform.icon,
                    url: platform.urls?.normal || platform.url,
                    searchUrl: platform.urls?.search || platform.searchUrl,
                    faviconUrl: platform.faviconUrl
                };
            }

            // 如果仍然找不到，尝试从旧的数据源查找（向后兼容）
            if (this.platformManager && this.platformManager.platforms) {
                const customPlatform = this.platformManager.platforms.get(platformId);
                if (customPlatform) {
                    return customPlatform;
                }
            }

            // 然后从SearchPlatformConfig查找
            const searchPlatformConfig = new window.SearchPlatformConfig();
            const builtinPlatform = searchPlatformConfig.getPlatform(platformId);
            if (builtinPlatform) {
                return builtinPlatform;
            }

            // 默认返回Google搜索
            console.warn(`平台 ${platformId} 未找到，使用Google作为默认平台`);
            return this.getPlatformInfo('google') || searchPlatformConfig.getPlatform('google');
        }

        // 删除平台
        deletePlatform() {
            // 这里只是从下拉列表中移除，实际的删除逻辑由PlatformManager处理
            // 重新渲染平台列表以反映删除
            this.initializePlatformList();
        }

        // 带动画的删除平台
        deletePlatformWithAnimation(platformItem, platformId) {
            // 添加删除动画类
            platformItem.classList.add('deleting');

            // 等待动画完成后移除元素
            setTimeout(() => {
                platformItem.remove();
                console.log(`删除平台: ${platformId}`);

                // 立即更新全部快捷搜索平台弹窗的状态（如果打开的话）
                this.updatePlatformModalStatus(platformId, false);

                // 使用防抖机制延迟执行刷新和保存操作，避免快速连续删除时的冲突
                this.debouncedRefreshAndSave();
            }, 300); // 与CSS动画时间一致
        }

        // 显示添加平台弹窗
        showAddPlatformModal() {
            // 设置全部快捷搜索平台弹窗状态为打开
            this.isAddPlatformModalOpen = true;

            console.log('打开全部快捷搜索平台弹窗，弹窗列表保持展开');

            // 显示新的模态弹窗
            this.showPlatformSelectionModal();
        }

        // 显示平台选择模态弹窗
        async showPlatformSelectionModal() {
            const modal = document.getElementById('addPlatformModal');
            const grid = document.getElementById('addPlatformGrid');

            if (!modal || !grid) return;

            // 生成所有可用平台选项（异步获取真实图标）
            await this.renderPlatformOptions(grid);

            // 显示模态弹窗
            modal.style.display = 'flex';

            // 绑定事件
            this.bindPlatformModalEvents();
        }

        // 渲染平台选项
        async renderPlatformOptions(grid) {
            // 1. 获取预设平台（SearchPlatformConfig.js中的数据）
            const presetPlatforms = this.platformConfig.platforms;

            // 2. 获取快捷搜索页面的搜索网站
            const pageManager = window.app?.pageManager;
            let shortcutPlatforms = [];

            if (pageManager) {
                const quickSearchPages = pageManager.pages.filter(page => page.mode === 'quick');
                quickSearchPages.forEach(page => {
                    if (page.shortcuts && page.shortcuts.length > 0) {
                        const searchShortcuts = page.shortcuts.filter(shortcut =>
                            shortcut.searchMode === 'search' && shortcut.searchUrl
                        );

                        // 转换为平台格式
                        const convertedShortcuts = searchShortcuts.map(shortcut => ({
                            id: shortcut.id,
                            name: shortcut.name,
                            icon: shortcut.icon || '🔍',
                            urls: {
                                normal: shortcut.url,
                                search: shortcut.searchUrl
                            },
                            faviconUrl: shortcut.faviconUrl,
                            isFromShortcut: true,
                            description: `来自快捷搜索页面`
                        }));

                        shortcutPlatforms.push(...convertedShortcuts);
                    }
                });
            }

            // 3. 合并所有平台，去重（优先保留预设平台）
            const allPlatforms = [...presetPlatforms];
            shortcutPlatforms.forEach(shortcutPlatform => {
                // 检查是否与预设平台重复（通过名称或URL判断）
                const isDuplicate = presetPlatforms.some(preset =>
                    preset.name === shortcutPlatform.name ||
                    preset.urls?.normal === shortcutPlatform.urls?.normal
                );

                if (!isDuplicate) {
                    allPlatforms.push(shortcutPlatform);
                }
            });

            // 获取当前弹窗中已显示的平台ID列表
            const currentPlatformIds = this.getCurrentPlatformIds();

            console.log('预设平台数据:', presetPlatforms);
            console.log('快捷搜索平台数据:', shortcutPlatforms);
            console.log('当前已显示平台:', currentPlatformIds);

            // 异步获取每个平台的图标，使用公共方法
            const optionsHtml = await Promise.all(allPlatforms.map(async platform => {
                const isUsed = currentPlatformIds.includes(platform.id);
                const statusClass = isUsed ? 'used' : 'unused';

                // 使用公共方法获取图标HTML
                const iconHtml = await this.getPlatformIconHtml(platform);

                return `
                    <div class="add-platform-option ${statusClass}"
                         data-platform-id="${platform.id}">
                        <div class="add-platform-icon-container">
                            ${iconHtml}
                        </div>
                        <div class="add-platform-option-name">${platform.name}</div>
                    </div>
                `;
            }));

            grid.innerHTML = optionsHtml.join('');
        }

        // 获取当前弹窗中已显示的平台ID列表
        getCurrentPlatformIds() {
            const platformItems = document.querySelectorAll('.platform-item[data-platform-id]');
            return Array.from(platformItems).map(item => item.dataset.platformId);
        }

        // 更新全部快捷搜索平台弹窗的状态
        updatePlatformModalStatus(platformId, isUsed) {
            const modal = document.getElementById('addPlatformModal');
            if (modal && modal.style.display !== 'none') {
                const option = modal.querySelector(`.add-platform-option[data-platform-id="${platformId}"]`);
                if (option) {
                    option.classList.remove('used', 'unused');
                    option.classList.add(isUsed ? 'used' : 'unused');
                    console.log(`更新模态弹窗状态: ${platformId} → ${isUsed ? 'used' : 'unused'}`);
                }
            }
        }

        // 公共方法：异步获取平台图标HTML
        async getPlatformIconHtml(platform) {
            let iconHtml = '';

            // 如果是'all'平台，直接返回emoji图标
            if (platform.id === 'all') {
                return `<span>${platform.icon}</span>`;
            }

            try {
                const platformUrl = platform.urls?.normal || platform.url || `https://${platform.name.toLowerCase()}.com`;

                // 先检查缓存
                const cached = this.faviconManager.iconCacheManager.getCachedIcon(platformUrl);
                let faviconUrl = null;

                if (cached) {
                    faviconUrl = cached.url;
                } else {
                    faviconUrl = await this.faviconManager.getFavicon(platformUrl);
                }

                if (faviconUrl && faviconUrl !== this.faviconManager.getDefaultIcon()) {
                    iconHtml = `<img src="${faviconUrl}" alt="${platform.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                               <span style="display: none;">${platform.icon}</span>`;
                } else {
                    iconHtml = `<span>${platform.icon}</span>`;
                }
            } catch (error) {
                iconHtml = `<span>${platform.icon}</span>`;
            }

            return iconHtml;
        }

        // 从弹窗列表中删除平台
        removePlatformFromDropdown(platformId) {
            const platformItem = document.querySelector(`.platform-item[data-platform-id="${platformId}"]`);
            if (platformItem) {
                // 立即删除，确保状态同步
                platformItem.remove();
                console.log(`从弹窗列表中删除平台: ${platformId}`);

                // 立即更新全部快捷搜索平台弹窗的状态（如果打开的话）
                this.updatePlatformModalStatus(platformId, false);

                // 使用防抖机制延迟执行刷新和保存操作，避免快速连续删除时的冲突
                this.debouncedRefreshAndSave();
            }
        }

        // 添加平台到弹窗列表
        async addPlatformToDropdown(platformId) {
            // 使用统一的平台获取方法
            const platform = this.getPlatformInfo(platformId);

            // 如果找不到平台信息，无法添加
            if (!platform) {
                console.warn(`平台 ${platformId} 既不在预设配置中，也不在用户新增平台中，无法添加`);
                return;
            }

            // 检查是否已经存在，避免重复添加
            const existingItem = document.querySelector(`.platform-item[data-platform-id="${platformId}"]`);
            if (existingItem) {
                console.log(`平台 ${platformId} 已存在，跳过添加`);
                return;
            }

            // 使用公共方法获取图标HTML
            const iconHtml = await this.getPlatformIconHtml(platform);

            // 创建新的平台项HTML，支持用户新增平台
            const platformItemHtml = `
                <div class="platform-item"
                     data-platform-id="${platform.id}"
                     data-platform-name="${platform.name}"
                     data-platform-url="${platform.urls?.normal || platform.url || ''}"
                     data-platform-search-url="${platform.urls?.search || platform.searchUrl || ''}"
                     data-is-user-added="${platform.isFromShortcut ? 'true' : 'false'}">
                    <div class="platform-icon-container">
                        ${iconHtml}
                        <div class="platform-item-delete" title="删除此平台" data-action="delete">×</div>
                    </div>
                    <div class="platform-item-name">${platform.name}</div>
                    ${platform.isFromShortcut ? '<div class="platform-source-indicator">用户新增</div>' : ''}
                </div>
            `;

            // 找到添加按钮，在其前面插入新平台项
            const addItem = document.querySelector('.platform-add-item');
            if (addItem) {
                addItem.insertAdjacentHTML('beforebegin', platformItemHtml);
                console.log(`添加真实平台到弹窗列表: ${platformId} (${platform.name})`);

                // 立即更新全部快捷搜索平台弹窗的状态（如果打开的话）
                this.updatePlatformModalStatus(platformId, true);

                // 使用防抖机制延迟执行刷新和保存操作，避免快速连续添加时的冲突
                this.debouncedRefreshAndSave();
            }
        }

        // 绑定模态弹窗事件
        bindPlatformModalEvents() {
            const modal = document.getElementById('addPlatformModal');
            const overlay = modal.querySelector('.add-platform-overlay');
            const closeBtn = document.getElementById('addPlatformClose');
            const grid = document.getElementById('addPlatformGrid');

            // 标记已绑定事件，避免重复绑定
            if (modal.dataset.eventsBound === 'true') {
                return;
            }
            modal.dataset.eventsBound = 'true';

            // 关闭弹窗事件 - 始终保持弹窗列表展开状态
            const closeModal = () => {
                modal.style.display = 'none';

                // 从弹窗列表的"添加"按钮打开的全部快捷搜索平台弹窗，关闭时始终保持弹窗列表展开
                // 使用setTimeout确保在全局点击事件处理之后执行
                setTimeout(() => {
                    this.showPlatformDropdown();
                    console.log('关闭全部快捷搜索平台弹窗，保持弹窗列表展开状态');
                }, 10); // 10ms延迟，确保在全局点击事件之后执行

                // 延迟重置状态，确保全局点击事件能正确检测到弹窗状态
                setTimeout(() => {
                    this.isAddPlatformModalOpen = false;
                }, 50); // 50ms延迟重置状态
            };

            overlay.addEventListener('click', closeModal);
            closeBtn.addEventListener('click', closeModal);

            // 平台选择事件 - 立即生效，无需确认按钮
            grid.addEventListener('click', (e) => {
                const option = e.target.closest('.add-platform-option');
                if (option) {
                    const platformId = option.dataset.platformId;
                    const isUsed = option.classList.contains('used');

                    console.log(`点击平台: ${platformId}, 当前状态: ${isUsed ? 'used' : 'unused'}`);

                    if (isUsed) {
                        // 绿色外框平台 → 从弹窗列表中删除，变为红色
                        this.removePlatformFromDropdown(platformId);
                        option.classList.remove('used');
                        option.classList.add('unused');
                        console.log(`${platformId} 状态变更: used → unused`);
                    } else {
                        // 红色外框平台 → 添加到弹窗列表中，变为绿色
                        this.addPlatformToDropdown(platformId);
                        option.classList.remove('unused');
                        option.classList.add('used');
                        console.log(`${platformId} 状态变更: unused → used`);
                    }
                }
            });

            // 移除确认按钮事件处理，操作立即生效
        }











        // 保存搜索历史
        saveSearchHistory(query) {
            try {
                if (!query || query.length < 2) return;

                const history = Storage.get('searchHistory_v3', []);

                // 移除重复项
                const filteredHistory = history.filter(item => item !== query);

                // 添加到开头
                filteredHistory.unshift(query);

                // 限制历史记录数量
                const maxHistory = 50;
                if (filteredHistory.length > maxHistory) {
                    filteredHistory.splice(maxHistory);
                }

                Storage.set('searchHistory_v3', filteredHistory);
            } catch (error) {
                console.warn('保存搜索历史失败:', error);
            }
        }

        // 使用指定查询词执行搜索
        executeSearchWithQuery(query) {
            if (!query) return;

            // 保存搜索历史
            this.saveSearchHistory(query);

            // 执行搜索
            if (this.searchMode === 'normal') {
                this.executeNormalSearch(query);
            } else {
                this.executeQuickSearch(query);
            }

            // 清空输入框
            const { searchInput } = this.domElements;
            if (searchInput) {
                searchInput.value = '';
            }

            // 保持用户选择的搜索引擎不变，不进行重置
        }

        // 统一的平台图标设置方法
        setPlatformIcon(faviconElement, emojiElement, iconData) {
            const { faviconUrl, icon, name } = iconData;

            // 优先使用网站图标
            if (faviconUrl && faviconUrl !== this.faviconManager?.getDefaultIcon()) {
                faviconElement.src = faviconUrl;
                faviconElement.alt = name || '';
                faviconElement.style.display = 'inline';
                emojiElement.style.display = 'none';
            } else if (icon && typeof icon === 'string' && icon.length <= 2) {
                // 使用emoji图标
                emojiElement.textContent = icon;
                emojiElement.style.display = 'inline';
                faviconElement.style.display = 'none';
            } else {
                // 使用默认搜索图标
                emojiElement.textContent = '🔍';
                emojiElement.style.display = 'inline';
                faviconElement.style.display = 'none';
            }
        }

        // 绑定多选功能事件
        bindMultiSelectEvents() {
            const multiSelectToggle = document.getElementById('multiSelectToggle');

            if (multiSelectToggle) {
                multiSelectToggle.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleMultiSelectMode();
                });
            }
        }

        // 切换多选模式
        toggleMultiSelectMode() {
            const platformItems = document.querySelectorAll('.platform-item:not([data-action="add-platform"])');
            const multiSelectToggle = document.getElementById('multiSelectToggle');
            const multiSelectIcon = multiSelectToggle?.querySelector('.multi-select-icon');

            const isMultiSelectMode = platformItems[0]?.classList.contains('multi-select-mode');

            if (isMultiSelectMode) {
                // 退出多选模式
                platformItems.forEach(item => {
                    item.classList.remove('multi-select-mode', 'selected');
                    const checkbox = item.querySelector('.platform-checkbox');
                    if (checkbox) {
                        checkbox.checked = false;
                        checkbox.closest('.platform-item-checkbox').style.display = 'none';
                    }
                });
                if (multiSelectIcon) multiSelectIcon.textContent = '☐';
                this.updateSelectedCount();
            } else {
                // 进入多选模式
                platformItems.forEach(item => {
                    item.classList.add('multi-select-mode');
                    const checkbox = item.querySelector('.platform-checkbox');
                    if (checkbox) {
                        checkbox.closest('.platform-item-checkbox').style.display = 'block';
                    }
                });
                if (multiSelectIcon) multiSelectIcon.textContent = '☑';

                // 绑定复选框变化事件
                this.bindCheckboxEvents();
            }
        }

        // 绑定复选框事件
        bindCheckboxEvents() {
            const checkboxes = document.querySelectorAll('.platform-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    this.updateSelectedCount();
                });
            });
        }

        // 更新选中数量显示
        updateSelectedCount() {
            const selectedCheckboxes = document.querySelectorAll('.platform-checkbox:checked');

            // 更新搜索框前面的图标显示
            this.updateSearchIconForMultiSelect(selectedCheckboxes.length);
        }

        // 更新搜索图标为多选模式
        updateSearchIconForMultiSelect(selectedCount) {
            const { platformFavicon, platformEmoji } = this.domElements;

            if (!platformFavicon || !platformEmoji) return;

            if (selectedCount > 0) {
                // 多选模式：显示数字
                platformEmoji.textContent = selectedCount.toString();
                platformEmoji.style.display = 'inline';
                platformFavicon.style.display = 'none';

                // 添加多选模式标记
                platformEmoji.classList.add('multi-select-mode');
                console.log(`搜索图标已切换为多选模式，显示数字: ${selectedCount}`);
            } else {
                // 恢复单选模式
                platformEmoji.classList.remove('multi-select-mode');

                // 恢复当前选中平台的图标
                const currentPlatform = this.getCurrentSelectedPlatform();
                if (currentPlatform) {
                    this.setPlatformIcon(platformFavicon, platformEmoji, {
                        faviconUrl: currentPlatform.faviconUrl,
                        icon: currentPlatform.icon,
                        name: currentPlatform.name
                    });
                }
                console.log('搜索图标已恢复为单选模式');
            }
        }

        // 获取当前选中的平台
        getCurrentSelectedPlatform() {
            const platforms = this.getModePlatforms();
            return platforms.length > 0 ? platforms[0] : null;
        }

        // 执行多平台搜索
        executeMultiPlatformSearch() {
            const selectedCheckboxes = document.querySelectorAll('.platform-checkbox:checked');
            const searchInput = document.getElementById('searchInput');
            const searchQuery = searchInput?.value.trim();

            if (!searchQuery) {
                console.warn('请输入搜索关键词');
                return;
            }

            if (selectedCheckboxes.length === 0) {
                console.warn('请至少选择一个搜索平台');
                return;
            }

            // 获取当前平台列表数据
            const platforms = this.getModePlatforms();

            // 为每个选中的平台打开新标签页
            selectedCheckboxes.forEach(checkbox => {
                const platformId = checkbox.dataset.platformId;
                const platform = platforms.find(p => p.id === platformId);

                if (platform && platform.searchUrl) {
                    let finalUrl;
                    if (platform.searchUrl.includes('{query}')) {
                        finalUrl = platform.searchUrl.replace('{query}', encodeURIComponent(searchQuery));
                    } else if (platform.searchUrl.includes('{keyword}')) {
                        finalUrl = platform.searchUrl.replace('{keyword}', encodeURIComponent(searchQuery));
                    } else if (platform.searchUrl.includes('?')) {
                        finalUrl = platform.searchUrl + encodeURIComponent(searchQuery);
                    } else {
                        finalUrl = platform.searchUrl + '?q=' + encodeURIComponent(searchQuery);
                    }

                    // 打开新标签页
                    window.open(finalUrl, '_blank');
                    console.log(`在 ${platform.name} 中搜索: ${searchQuery} -> ${finalUrl}`);
                } else {
                    console.warn(`平台 ${platformId} 没有搜索URL配置`);
                }
            });

            // 显示成功提示
            console.log(`✅ 已在 ${selectedCheckboxes.length} 个平台中搜索: ${searchQuery}`);

            // 隐藏下拉框
            this.hidePlatformDropdown();
        }

        // 退出多选模式，恢复到正常状态
        exitMultiSelectMode() {
            const platformItems = document.querySelectorAll('.platform-item:not([data-action="add-platform"])');
            const multiSelectToggle = document.getElementById('multiSelectToggle');
            const multiSelectIcon = multiSelectToggle?.querySelector('.multi-select-icon');

            // 退出多选模式
            platformItems.forEach(item => {
                item.classList.remove('multi-select-mode', 'selected');
                const checkbox = item.querySelector('.platform-checkbox');
                if (checkbox) {
                    checkbox.checked = false;
                    checkbox.closest('.platform-item-checkbox').style.display = 'none';
                }
            });

            // 重置多选按钮状态
            if (multiSelectIcon) multiSelectIcon.textContent = '☐';

            // 更新计数器（会触发搜索图标恢复）
            this.updateSelectedCount();

            console.log('已退出多选模式，恢复到正常搜索状态');
        }

        // 防抖机制：延迟执行刷新和保存操作，避免快速连续操作时的冲突
        debouncedRefreshAndSave() {
            // 清除之前的定时器
            if (this.refreshAndSaveTimer) {
                clearTimeout(this.refreshAndSaveTimer);
            }

            // 设置新的定时器，延迟300ms执行
            this.refreshAndSaveTimer = setTimeout(() => {
                try {
                    // 刷新平台下拉框列表，实现数据同步
                    this.refreshPlatformDropdownList();

                    // 保存用户的平台选择
                    this.saveUserPlatformSelection();

                    console.log('防抖机制：平台数据已同步保存');
                } catch (error) {
                    console.error('防抖机制执行失败:', error);
                }
            }, 300);
        }

        // 刷新平台下拉框列表，实现数据同步
        refreshPlatformDropdownList() {
            // 检查平台下拉框是否打开
            const platformDropdown = document.getElementById('platformDropdown');
            if (!platformDropdown || platformDropdown.style.display === 'none') {
                console.log('平台下拉框未打开，跳过刷新');
                return;
            }

            // 重新获取平台列表并渲染
            const platforms = this.getModePlatforms();
            this.renderPlatformList(platforms);
            console.log('平台下拉框列表已刷新，实现数据同步');
        }

        // 保存用户的平台选择到localStorage
        saveUserPlatformSelection() {
            try {
                const platformItems = document.querySelectorAll('.platform-item[data-platform-id]:not([data-action="add-platform"])');
                const userPlatforms = Array.from(platformItems).map(item => ({
                    id: item.dataset.platformId,
                    name: item.dataset.platformName || item.querySelector('.platform-item-name')?.textContent
                }));

                Storage.set('userSelectedPlatforms_v3', userPlatforms);
                console.log(`已保存用户平台选择: ${userPlatforms.length} 个平台`, userPlatforms);
                return true;
            } catch (error) {
                console.error('保存用户平台选择失败:', error);
                return false;
            }
        }

        // 从localStorage加载用户的平台选择
        loadUserPlatformSelection() {
            try {
                const userPlatforms = Storage.get('userSelectedPlatforms_v3');
                if (!userPlatforms || !Array.isArray(userPlatforms)) {
                    console.log('未找到用户平台选择，使用默认配置');
                    return this.getDefaultPlatforms();
                }

                console.log(`已加载用户平台选择: ${userPlatforms.length} 个平台`, userPlatforms);
                return userPlatforms;
            } catch (error) {
                console.error('加载用户平台选择失败:', error);
                return this.getDefaultPlatforms();
            }
        }

        // 获取默认平台配置
        getDefaultPlatforms() {
            return [
                { id: 'google', name: 'Google' },
                { id: 'bing', name: 'Bing' },
                { id: 'baidu', name: '百度' }
            ];
        }



    }

    // 主应用类
    class NewTabApp {
        constructor() {
            this.configManager = null;
            this.timeManager = null;
            this.searchManager = null;
            this.searchModeManager = null;
            this.shortcutManager = null;
            this.settingsManager = null;
            this.backgroundManager = null;
            this.dataStorageManager = null;
            this.shortcutHelpManager = null;

            this.init();
        }

        async init() {
            try {
                // 添加加载状态
                document.body.classList.add('app-loading');

                // 初始化全局事件管理器
                GlobalEventManager.init();

                // 检查数据迁移
                await this.checkDataMigration();

                // 同步初始化所有模块，确保数据先加载
                await this.initAllModules();

                // 所有模块初始化完成后，移除加载状态
                document.body.classList.remove('app-loading');
                document.body.classList.add('app-ready');

                console.log('✅ Moment Search V3 启动完成');

            } catch (error) {
                console.error('❌ 初始化失败:', error);
                document.body.classList.remove('app-loading');
                this.showError('启动失败，请刷新页面重试');
            }
        }

        async initAllModules() {
            console.log('🔄 按依赖顺序初始化模块...');

            try {
                // 0. 首先初始化配置管理器，加载静态配置文件
                this.configManager = new ConfigManager();
                await this.configManager.initialize();

                // 0.5. 初始化数据存储管理器
                this.dataStorageManager = new DataStorageManager();

                // 1. 初始化设置管理器，确保所有配置可用
                this.settingsManager = new SettingsManager();
                await this.settingsManager.waitForInit();

                // 2. 初始化背景管理器，等待背景加载完成
                this.backgroundManager = new BackgroundManager();
                await this.backgroundManager.init();

                // 3. 初始化页面管理器（必须在快捷方式管理器之前）
                this.pageManager = new PageManager();
                this.pageManager.init();

                // 4. 初始化快捷方式管理器，等待图标预加载完成
                this.shortcutManager = new ShortcutManager();
                await this.shortcutManager.init();

                // 5. 最后初始化UI相关模块
                this.timeManager = new TimeManager();
                this.searchManager = new SearchManager();
                this.searchModeManager = new SearchModeManager();

                // 6. 初始化快捷键帮助管理器
                this.shortcutHelpManager = new ShortcutHelpManager();

                console.log('✅ 所有模块初始化完成');
            } catch (error) {
                console.error('❌ 模块初始化失败:', error);
                throw error;
            }
        }

        async checkDataMigration() {
            // 检查是否需要从V2迁移数据
            const v2History = localStorage.getItem('momentSearchHistory');
            const v3Migrated = localStorage.getItem('v3_migrated');
            
            if (v2History && !v3Migrated) {
                await this.migrateFromV2();
                localStorage.setItem('v3_migrated', 'true');
            }
        }

        async migrateFromV2() {
            try {
                // 迁移主题设置
                const v2Theme = localStorage.getItem('momentSearchTheme');
                if (v2Theme) {
                    const settings = Storage.get('settings_v3', {});
                    settings.appearance = settings.appearance || {};
                    settings.appearance.theme = v2Theme;
                    Storage.set('settings_v3', settings);
                }


            } catch (error) {
                console.error('❌ 数据迁移失败:', error);
            }
        }

        showError(message) {
            document.body.innerHTML = `
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    color: white;
                    text-align: center;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                ">
                    <div>
                        <h2>⚠️ ${message}</h2>
                        <p style="margin-top: 16px; opacity: 0.8;">
                            如果问题持续存在，请尝试清除浏览器缓存
                        </p>
                    </div>
                </div>
            `;
        }
    }

    // 启动应用
    document.addEventListener('DOMContentLoaded', () => {
        window.app = new NewTabApp();
    });

    // 快捷方式管理器
    class ShortcutManager {
        constructor() {
            this.shortcuts = [];
            this.maxShortcuts = 60; // 增加到60个，支持更多自定义快捷方式
            this.container = document.getElementById('shortcutsGrid');
            this.currentEditingId = null;

            // 批量操作模式标志（用于避免多次重新渲染）
            this.batchMode = false;

            // 初始化图标管理器
            this.faviconManager = new FaviconManager();

            // 初始化搜索平台配置
            this.searchPlatformConfig = new SearchPlatformConfig();

            // 初始化桌面级拖拽管理器
            this.dragDropManager = null;

            // 初始化DOM管理器（避免重复实例化）
            this.domManager = new DOMManager();

            // 缓存常用的DOM元素
            this.modalElements = {
                modal: document.getElementById('modalOverlay'),
                title: document.getElementById('modalTitle'),
                nameInput: document.getElementById('shortcutName'),
                urlInput: document.getElementById('shortcutUrl'),
                iconPreview: document.getElementById('iconPreview'),
                iconStatus: document.getElementById('iconStatus'),
                previewFavicon: document.getElementById('previewFavicon'),
                previewEmoji: document.getElementById('previewEmoji')
            };

            // 不在构造函数中自动调用init，由主应用控制初始化时机
        }

        async init() {
            this.loadShortcuts();
            this.enhanceShortcutsWithSearchUrls();

            // 预加载所有图标，确保在渲染前完成
            await this.preloadAllIcons();

            // 渲染快捷方式（包含智能预加载）
            await this.render();
            // 初始化拖拽管理器
            this.initDragDropManager();
            console.log('✅ 快捷方式管理器初始化完成');
        }

        // 预加载所有快捷方式图标
        async preloadAllIcons() {
            console.log('🔄 开始检查快捷方式图标...');

            // 统计图标情况（检查src和faviconUrl字段）
            const hasIconShortcuts = this.shortcuts.filter(shortcut => shortcut.src || shortcut.faviconUrl);
            const needFetchIcons = this.shortcuts.filter(shortcut => !shortcut.src && !shortcut.faviconUrl && shortcut.url);

            console.log(`📊 图标状态: ${hasIconShortcuts.length}个已有图标, ${needFetchIcons.length}个需获取`);

            // 只为没有图标的快捷方式获取图标
            let newIconsCount = 0;
            for (const shortcut of needFetchIcons) {
                try {
                    console.log(`🔍 获取图标: ${shortcut.name}`);
                    const iconUrl = await this.faviconManager.getFavicon(shortcut.url);
                    if (iconUrl && iconUrl !== this.faviconManager.getDefaultIcon()) {
                        // 直接保存到src字段（参考itabdate.md格式）
                        shortcut.src = iconUrl;
                        newIconsCount++;
                        console.log(`✅ 已保存图标: ${shortcut.name} -> ${iconUrl}`);
                    }
                } catch (error) {
                    console.warn(`获取图标失败: ${shortcut.name}`, error);
                }
            }

            // 保存更新后的快捷方式数据（如果有新图标）
            if (newIconsCount > 0) {
                this.saveShortcuts();
                console.log(`✅ 新获取了 ${newIconsCount} 个图标`);
                return { hasNewIcons: true, newIconsCount };
            } else {
                console.log('✅ 所有图标均已存在，无需重新获取');
                return { hasNewIcons: false, newIconsCount: 0 };
            }
        }

        initDragDropManager() {
            // 确保DesktopDragManager类已加载
            if (typeof DesktopDragManager !== 'undefined') {
                this.dragDropManager = new DesktopDragManager(this);
                this.dragDropManager.init();
                console.log('桌面级拖拽管理器初始化成功');
            } else {
                console.warn('DesktopDragManager类未找到，拖拽功能不可用');
            }
        }

        reinitDragDropManager() {
            // 清理旧的拖拽管理器
            if (this.dragDropManager) {
                this.dragDropManager.cleanup();
                this.dragDropManager = null;
            }

            // 重新初始化拖拽管理器
            this.initDragDropManager();
        }

        // 为快捷方式添加搜索URL配置
        enhanceShortcutsWithSearchUrls() {
            this.shortcuts.forEach(shortcut => {
                // 如果已有搜索URL配置，跳过
                if (shortcut.searchUrl) return;

                // 根据平台配置添加搜索URL
                const platformConfig = this.searchPlatformConfig.platforms.find(p =>
                    p.name === shortcut.name ||
                    p.urls.normal.includes(this.extractDomain(shortcut.url))
                );

                if (platformConfig) {
                    shortcut.searchUrl = platformConfig.urls.search;
                    shortcut.searchMode = 'search';
                    shortcut.pageMode = 'quick';
                } else {
                    // 默认为直接跳转模式
                    shortcut.searchMode = 'direct';
                    shortcut.pageMode = 'normal';
                }
            });
        }

        // 提取域名
        extractDomain(url) {
            try {
                return new URL(url).hostname.replace('www.', '');
            } catch {
                return url;
            }
        }

        loadShortcuts() {
            // 优先从页面管理器加载当前页面的快捷方式
            const pageManager = window.app?.pageManager;
            if (pageManager && pageManager.pages && pageManager.pages.length > 0) {
                const currentPage = pageManager.pages[pageManager.currentPageIndex];
                if (currentPage && currentPage.shortcuts && currentPage.shortcuts.length > 0) {
                    console.log(`📋 从页面管理器加载快捷方式（页面: ${currentPage.name}）...`);
                    this.shortcuts = [...currentPage.shortcuts];
                    console.log(`✅ 已加载 ${this.shortcuts.length} 个快捷方式`);
                    return;
                }
            }

            // 备用方案1：从配置文件加载快捷方式（已处理base64图标）
            const configManager = window.app?.configManager;
            if (configManager) {
                console.log('📋 从配置文件加载快捷方式（含base64图标）...');
                this.shortcuts = configManager.getShortcuts();
                console.log(`✅ 已加载 ${this.shortcuts.length} 个快捷方式，图标已处理为base64格式`);
                return;
            }

            // 备用方案2：从localStorage加载
            const saved = Storage.get('shortcuts_v3');
            if (saved && Array.isArray(saved)) {
                console.log('📋 从localStorage加载快捷方式...');
                this.shortcuts = saved;

                // 数据迁移：将faviconUrl迁移到src字段
                let needSave = false;
                this.shortcuts.forEach(shortcut => {
                    if (shortcut.faviconUrl && !shortcut.src) {
                        shortcut.src = shortcut.faviconUrl;
                        needSave = true;
                        console.log(`迁移图标数据: ${shortcut.name} -> ${shortcut.src}`);
                    }
                });

                if (needSave) {
                    this.saveShortcuts();
                    console.log('✅ 图标数据迁移完成');
                }
            } else {
                // 最后备用方案：使用硬编码的默认快捷方式
                console.log('📋 使用默认快捷方式...');
                this.shortcuts = [...DEFAULT_SHORTCUTS];
                this.saveShortcuts();
            }
        }

        saveShortcuts() {
            // 如果有页面管理器，保存到当前页面
            if (window.app?.pageManager) {
                window.app.pageManager.saveCurrentPageShortcuts();
            } else {
                // 兼容模式，保存到本地存储
                Storage.set('shortcuts_v3', this.shortcuts);
            }
        }

        async render() {
            if (!this.container) return;

            // 按order排序
            const sortedShortcuts = [...this.shortcuts].sort((a, b) => a.order - b.order);

            // 优化：检查是否需要预加载（只有在有新图标时才预加载，且不在拖拽状态下）
            const isDragging = this.dragDropManager?.isDragging || false;
            const isPageSwitching = this.isPageSwitching || false; // 新增：页面切换状态检查
            const needsPreload = !isDragging && !isPageSwitching && this.checkIfNeedsPreload(sortedShortcuts);

            // 创建快捷方式HTML（先渲染，后预加载）
            const shortcutsHTML = sortedShortcuts
                .map(shortcut => this.createShortcutHTML(shortcut))
                .join('');

            // 添加"添加图标"按钮
            const addButtonHTML = this.createAddButtonHTML();

            // 一次性更新DOM，避免重复渲染
            this.container.innerHTML = shortcutsHTML + addButtonHTML;

            // 异步预加载图标，不阻塞渲染
            if (needsPreload) {
                this.preloadIconsToCache(sortedShortcuts).catch(error => {
                    console.warn('图标预加载失败:', error);
                });
            }

            // 绑定事件（只绑定新创建的元素）
            this.bindShortcutEvents();

            // 更新拖拽管理器的网格信息（避免完全重新初始化）
            if (this.dragDropManager) {
                this.dragDropManager.calculateGridLayout();
            }

            console.log('✅ 快捷方式渲染完成');
        }

        // 检查是否需要预加载（检查主页面和文件夹内是否有HTTP图标）
        checkIfNeedsPreload(shortcuts) {
            // 获取当前页面的图标URL列表
            const currentIconUrls = this.extractAllIconUrls(shortcuts);

            // 缓存提取的URL，避免在preloadIconsToCache中重复计算
            this.cachedIconUrls = currentIconUrls;

            // 检查是否与上次预加载的URL列表相同
            if (this.lastPreloadedUrls &&
                this.arraysEqual(currentIconUrls, this.lastPreloadedUrls)) {
                this.cachedIconUrls = null; // 清除缓存，因为不需要预加载
                return false; // 相同的图标列表，无需重新预加载
            }

            // 检查是否有HTTP图标需要预加载
            return currentIconUrls.length > 0;
        }

        // 提取所有图标URL（优化：单次遍历处理所有类型）
        extractAllIconUrls(shortcuts) {
            const urls = [];

            shortcuts.forEach(shortcut => {
                if (shortcut.type === 'folder' && shortcut.children) {
                    // 文件夹内图标
                    shortcut.children.forEach(child => {
                        const iconUrl = child.src || child.faviconUrl;
                        if (iconUrl && iconUrl.startsWith('http')) {
                            urls.push(iconUrl);
                        }
                    });
                } else if (shortcut.type !== 'folder') {
                    // 主页面图标
                    const iconUrl = shortcut.src || shortcut.faviconUrl;
                    if (iconUrl && iconUrl.startsWith('http')) {
                        urls.push(iconUrl);
                    }
                }
            });

            return [...new Set(urls)]; // 去重
        }

        // 数组比较工具方法（添加边界检查）
        arraysEqual(arr1, arr2) {
            if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
            if (arr1.length !== arr2.length) return false;
            return arr1.every((val, index) => val === arr2[index]);
        }

        // 预加载图标到浏览器缓存（包括文件夹内的图标，但排除平台图标）
        async preloadIconsToCache(shortcuts) {
            // 复用checkIfNeedsPreload中已提取的URL，避免重复计算
            const currentIconUrls = this.cachedIconUrls || this.extractAllIconUrls(shortcuts);

            if (currentIconUrls.length > 0) {
                console.log(`🔄 预加载 ${currentIconUrls.length} 个HTTP图标到浏览器缓存...`);
                try {
                    await ImagePreloader.preloadImages(currentIconUrls, 1000);
                    console.log(`✅ 图标预加载完成`);

                    // 记录已预加载的URL列表
                    this.lastPreloadedUrls = [...currentIconUrls];
                    // 清除缓存的URL，避免内存泄漏
                    this.cachedIconUrls = null;
                } catch (error) {
                    console.warn('预加载图标到浏览器缓存失败:', error);
                }
            }
        }



        createShortcutHTML(shortcut) {
            // 检查是否是文件夹类型
            if (shortcut.type === 'folder') {
                return this.createFolderHTML(shortcut);
            }

            // 直接使用src字段保存的图标URL（参考itabdate.md格式）
            const iconSrc = shortcut.src || shortcut.faviconUrl || '';
            const showIcon = iconSrc && iconSrc.trim() !== '';

            return `
                <div class="shortcut-item" data-id="${shortcut.id}" data-url="${shortcut.url}" data-name="${shortcut.name}" data-type="shortcut">
                    <div class="shortcut-icon" data-shortcut-id="${shortcut.id}">
                        <img class="shortcut-favicon"
                             src="${showIcon ? iconSrc : ''}"
                             alt="${shortcut.name}"
                             style="display: ${showIcon ? 'inline' : 'none'};"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span class="shortcut-emoji" style="display: ${showIcon ? 'none' : 'inline'}">${shortcut.icon}</span>
                    </div>
                    <div class="shortcut-name">${shortcut.name}</div>
                </div>
            `;
        }

        // 创建文件夹HTML
        createFolderHTML(folder) {
            const isExpanded = folder.isExpanded || false;

            // 检查图标是否是SVG data URL
            const isSVGIcon = folder.icon && folder.icon.startsWith('data:image/svg+xml');

            return `
                <div class="shortcut-item folder-item" data-id="${folder.id}" data-name="${folder.name}" data-type="folder">
                    <div class="shortcut-icon folder-icon" data-shortcut-id="${folder.id}">
                        ${isSVGIcon ?
                            `<img class="folder-preview-icon" src="${folder.icon}" alt="${folder.name}" />` :
                            `<span class="folder-emoji">${folder.icon}</span>`
                        }
                    </div>
                    <div class="shortcut-name">${folder.name}</div>
                    ${isExpanded ? this.createFolderContentHTML(folder) : ''}
                </div>
            `;
        }

        // 创建文件夹内容HTML（展开时显示）
        createFolderContentHTML(folder) {
            if (!folder.children || folder.children.length === 0) return '';

            const childrenHTML = folder.children
                .map(child => `
                    <div class="folder-child-item" data-id="${child.id}" data-url="${child.url}">
                        <div class="child-icon">
                            <img src="${child.faviconUrl || ''}" alt="${child.name}"
                                 style="display: ${child.faviconUrl ? 'inline' : 'none'};"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                            <span class="child-emoji" style="display: ${child.faviconUrl ? 'none' : 'inline'}">${child.icon}</span>
                        </div>
                        <div class="child-name">${child.name}</div>
                    </div>
                `)
                .join('');

            return `
                <div class="folder-content">
                    ${childrenHTML}
                </div>
            `;
        }

        // 创建添加按钮HTML
        createAddButtonHTML() {
            if (this.shortcuts.length >= this.maxShortcuts) {
                return ''; // 达到最大数量时不显示添加按钮
            }

            return `
                <div class="shortcut-add-button" id="shortcutAddButton" tabindex="0">
                    <div class="shortcut-icon add-icon">
                        <span class="add-icon-plus">+</span>
                    </div>
                    <div class="shortcut-name">添加图标</div>
                </div>
            `;
        }



        // 更新快捷方式图标显示
        updateShortcutIcon(shortcutId, faviconUrl) {
            const iconContainer = this.container?.querySelector(`[data-shortcut-id="${shortcutId}"]`);
            if (!iconContainer) return;

            const faviconImg = iconContainer.querySelector('.shortcut-favicon');
            const emojiSpan = iconContainer.querySelector('.shortcut-emoji');

            if (faviconImg && emojiSpan) {
                faviconImg.src = faviconUrl;
                faviconImg.style.display = 'inline';
                emojiSpan.style.display = 'none';
            }
        }

        bindShortcutEvents() {
            if (!this.container) return;

            // 绑定右键菜单
            this.bindContextMenuEvents();

            // 绑定模态框事件
            this.bindModalEvents();

            const shortcutItems = this.container.querySelectorAll('.shortcut-item');
            shortcutItems.forEach(item => {
                // 点击事件 - 根据搜索模式决定行为
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.handleShortcutClick(item);
                });

                // 右键菜单
                item.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    this.showContextMenu(e, item.dataset.id);
                });

                // 双击编辑
                item.addEventListener('dblclick', (e) => {
                    e.preventDefault();
                    this.editShortcut(item.dataset.id);
                });

                // 键盘事件支持
                item.addEventListener('keydown', (e) => {
                    this.handleShortcutKeydown(e, item);
                });

                // 设置可聚焦
                item.setAttribute('tabindex', '0');
            });

            // 绑定容器级别的键盘事件
            this.bindKeyboardEvents();

            // 绑定添加按钮事件
            this.bindAddButtonEvents();
        }

        // 绑定添加按钮事件
        bindAddButtonEvents() {
            const addButton = document.getElementById('shortcutAddButton');
            if (!addButton) return;

            // 点击事件
            addButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.showAddModal();
            });

            // 键盘事件
            addButton.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.showAddModal();
                }
            });

            // 悬停效果
            addButton.addEventListener('mouseenter', () => {
                addButton.classList.add('hover');
            });

            addButton.addEventListener('mouseleave', () => {
                addButton.classList.remove('hover');
            });
        }

        // 处理快捷方式键盘事件
        handleShortcutKeydown(e, item) {
            const id = item.dataset.id;

            switch(e.key) {
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    this.handleShortcutClick(item);
                    break;
                case 'Delete':
                case 'Backspace':
                    e.preventDefault();
                    this.deleteShortcut(id);
                    break;
                case 'F2':
                    e.preventDefault();
                    this.editShortcut(id);
                    break;
                case 'Escape':
                    item.blur();
                    break;
            }
        }

        // 绑定容器级别的键盘事件
        bindKeyboardEvents() {
            if (!this.container) return;

            this.container.addEventListener('keydown', (e) => {
                // Ctrl+A 全选
                if (e.ctrlKey && e.key === 'a') {
                    e.preventDefault();
                    this.selectAllShortcuts();
                }

                // Ctrl+N 添加新快捷方式
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    this.showAddModal();
                }

                // Delete 删除选中的快捷方式
                if (e.key === 'Delete') {
                    const focused = document.activeElement;
                    if (focused && focused.classList.contains('shortcut-item')) {
                        e.preventDefault();
                        this.deleteShortcut(focused.dataset.id);
                    }
                }
            });
        }

        // 全选快捷方式
        selectAllShortcuts() {
            const items = this.container.querySelectorAll('.shortcut-item');
            items.forEach(item => {
                item.classList.add('selected');
            });
        }

        // 处理快捷方式点击
        handleShortcutClick(item) {
            const shortcutId = item.dataset.id;
            const shortcut = this.shortcuts.find(s => s.id == shortcutId);

            if (!shortcut) {
                console.warn('未找到快捷方式数据:', shortcutId);
                return;
            }

            // 如果是文件夹，打开文件夹模态窗口
            if (shortcut.type === 'folder') {
                this.openFolder(shortcutId);
                return;
            }

            // 获取当前页面的模式
            const currentPageMode = window.app?.pageManager?.pages[window.app?.pageManager?.currentPageIndex]?.mode || 'normal';

            // 根据当前页面模式和快捷方式配置决定行为
            if (currentPageMode === 'quick' && shortcut.searchMode === 'search') {
                // 快捷搜索模式：选择平台进行搜索
                this.selectPlatformForQuickSearch(shortcut, item);
            } else {
                // 常规模式或直接跳转：打开URL
                this.openShortcut(shortcut.url);
            }
        }

        /**
         * 打开文件夹
         * @param {string} folderId 文件夹ID
         */
        openFolder(folderId) {
            if (this.dragDropManager && this.dragDropManager.folderManager) {
                this.dragDropManager.folderManager.openFolderModal(folderId);
            } else {
                console.warn('文件夹管理器未初始化');
            }
        }

        // 选择平台进行搜索
        selectPlatformForSearch(platformName, platformUrl, item) {
            const elements = this.domManager.getMultiple([
                'platformIndicator', 'platformIcon', 'platformName', 'searchInput'
            ]);

            const { platformIcon, platformName: platformNameElement, searchInput } = elements;

            if (platformIcon && platformNameElement && searchInput) {
                // 获取平台图标
                const iconElement = item.querySelector('.shortcut-icon');
                const icon = iconElement ? iconElement.textContent.trim() : '🔍';

                // 更新平台指示器
                platformIcon.textContent = icon;
                platformNameElement.textContent = platformName;
                searchInput.placeholder = `在 ${platformName} 中搜索...`;

                // 保存当前选择的平台
                if (window.app?.searchManager) {
                    window.app.searchManager.setSelectedPlatform(platformName, platformUrl);
                }
            }
        }

        // 选择平台进行快捷搜索
        selectPlatformForQuickSearch(shortcut, item) {
            try {
                // 直接设置搜索管理器的配置
                if (window.app?.searchManager) {
                    // 直接设置selectedPlatformConfig，跳过复杂的setSelectedPlatform逻辑
                    window.app.searchManager.selectedPlatformConfig = {
                        id: shortcut.id,
                        name: shortcut.name,
                        url: shortcut.url,
                        searchUrl: shortcut.searchUrl,
                        icon: shortcut.icon
                    };
                }

                // 更新搜索框placeholder
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.placeholder = `在 ${shortcut.name} 中搜索...`;
                    searchInput.focus();
                }

                // 更新搜索框前面的平台图标
                this.updateSearchPlatformIcon(shortcut);

                // 添加选中状态
                this.container.querySelectorAll('.shortcut-item').forEach(i => {
                    i.classList.remove('selected');
                });
                item.classList.add('selected');

            } catch (error) {
                console.error('❌ selectPlatformForQuickSearch 错误:', error);
            }
        }

        // 更新搜索框前面的平台图标
        updateSearchPlatformIcon(shortcut) {
            try {
                // 直接获取DOM元素，确保能找到
                const platformFavicon = document.getElementById('platformFavicon');
                const platformEmoji = document.getElementById('platformEmoji');

                if (!platformFavicon || !platformEmoji) {
                    console.warn('平台图标元素未找到');
                    return;
                }

                // 使用SearchManager的统一图标更新逻辑
                if (window.app?.searchManager?.setPlatformIcon) {
                    window.app.searchManager.setPlatformIcon(platformFavicon, platformEmoji, {
                        faviconUrl: shortcut.faviconUrl,
                        icon: shortcut.icon,
                        name: shortcut.name
                    });
                } else {
                    // 后备方案：直接设置图标
                    if (shortcut.faviconUrl && shortcut.faviconUrl !== this.faviconManager?.getDefaultIcon()) {
                        platformFavicon.src = shortcut.faviconUrl;
                        platformFavicon.alt = shortcut.name || '';
                        platformFavicon.style.display = 'inline';
                        platformEmoji.style.display = 'none';
                    } else if (shortcut.icon && typeof shortcut.icon === 'string' && shortcut.icon.length <= 2) {
                        platformEmoji.textContent = shortcut.icon;
                        platformEmoji.style.display = 'inline';
                        platformFavicon.style.display = 'none';
                    } else {
                        platformEmoji.textContent = '🔍';
                        platformEmoji.style.display = 'inline';
                        platformFavicon.style.display = 'none';
                    }
                }

            } catch (error) {
                console.error('❌ updateSearchPlatformIcon 错误:', error);
            }
        }



        bindModalEvents() {
            // 防止重复绑定事件监听器
            if (this._modalEventsBound) {
                return;
            }

            const modal = document.getElementById('modalOverlay');
            const closeModal = document.getElementById('closeModal');
            const cancelBtn = document.getElementById('cancelBtn');
            const confirmBtn = document.getElementById('confirmBtn');
            const fetchIconBtn = document.getElementById('fetchIconBtn');
            const urlInput = document.getElementById('shortcutUrl');

            if (closeModal) {
                closeModal.addEventListener('click', () => this.hideModal());
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => this.hideModal());
            }

            if (confirmBtn) {
                confirmBtn.addEventListener('click', () => this.handleModalConfirm());
            }

            if (fetchIconBtn) {
                fetchIconBtn.addEventListener('click', () => this.handleFetchIcon());
            }

            // 标记事件已绑定
            this._modalEventsBound = true;

            // URL输入自动处理
            if (urlInput) {
                // 使用防抖优化
                const debouncedAutoFetch = Utils.debounce((url) => {
                    this.handleAutoFetchIcon(url);
                }, 800);

                urlInput.addEventListener('input', (e) => {
                    const url = e.target.value.trim();

                    // 清除错误提示
                    this.hideModalError();

                    if (url) {
                        debouncedAutoFetch(url);
                    }
                });
            }

            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.hideModal();
                    }
                });
            }
        }

        // 自动获取图标和名称
        async handleAutoFetchIcon(url) {
            const nameInput = document.getElementById('shortcutName');
            const urlInput = document.getElementById('shortcutUrl');
            const iconStatus = document.getElementById('iconStatus');

            if (!url) return;

            // 更新状态
            if (iconStatus) iconStatus.textContent = '正在获取图标...';

            try {
                // 标准化URL
                const normalizedUrl = url.startsWith('http') ? url : 'https://' + url;

                // 检查当前页面模式，如果是搜索直达模式，尝试解析URL
                const currentPageMode = window.app?.pageManager?.pages[window.app?.pageManager?.currentPageIndex]?.mode || 'normal';
                let finalUrl = normalizedUrl;
                let urlParseSuccess = false;

                if (currentPageMode === 'quick') {
                    try {
                        const platformManager = window.app?.searchManager?.platformManager;
                        if (platformManager && platformManager.urlParser) {
                            const parseResult = await platformManager.urlParser.parseURL(normalizedUrl);
                            if (parseResult.success && parseResult.template) {
                                finalUrl = parseResult.template;
                                urlParseSuccess = true;

                                // 更新URL输入框显示解析后的模板
                                if (urlInput) {
                                    urlInput.value = finalUrl;
                                }

                                console.log(`搜索直达模式：URL解析成功，模板: ${finalUrl}`);
                            }
                        }
                    } catch (error) {
                        console.warn('URL解析失败:', error);
                    }
                }

                // 获取图标（使用原始URL获取图标，而不是模板URL）
                const faviconUrl = await this.faviconManager.getFavicon(normalizedUrl);

                if (faviconUrl && faviconUrl !== this.faviconManager.getDefaultIcon()) {
                    // 测试图标是否可用
                    const isValid = await this.faviconManager.testImageUrl(faviconUrl);
                    if (isValid) {
                        this.updateIconPreview(faviconUrl, 'favicon');

                        // 根据是否解析成功显示不同的状态信息
                        if (iconStatus) {
                            if (urlParseSuccess) {
                                iconStatus.textContent = '图标获取成功，URL已解析为搜索模板';
                            } else {
                                iconStatus.textContent = '图标获取成功';
                            }
                        }

                        // 自动填充网站名称
                        if (nameInput && !nameInput.value.trim()) {
                            const siteName = this.extractSiteName(normalizedUrl);
                            if (siteName) {
                                nameInput.value = siteName;
                                nameInput.placeholder = '请输入名称';
                            }
                        }
                        return;
                    }
                }

                // 获取失败，使用默认图标
                this.updateIconPreview('🌐', 'emoji');
                if (iconStatus) {
                    if (urlParseSuccess) {
                        iconStatus.textContent = '无法获取图标，使用默认图标，URL已解析为搜索模板';
                    } else {
                        iconStatus.textContent = '无法获取图标，使用默认图标';
                    }
                }

                // 仍然尝试填充网站名称
                if (nameInput && !nameInput.value.trim()) {
                    const siteName = this.extractSiteName(normalizedUrl);
                    if (siteName) {
                        nameInput.value = siteName;
                        nameInput.placeholder = '请输入名称';
                    }
                }

            } catch (error) {
                console.warn('自动获取图标失败:', error);
                this.updateIconPreview('🌐', 'emoji');
                if (iconStatus) iconStatus.textContent = '获取图标失败，使用默认图标';
            }
        }

        // 处理获取图标按钮点击
        async handleFetchIcon() {
            const urlInput = document.getElementById('shortcutUrl');
            const fetchIconBtn = document.getElementById('fetchIconBtn');

            const url = urlInput?.value.trim();
            if (!url) {
                alert('请先输入网址');
                return;
            }

            // 禁用按钮，显示加载状态
            if (fetchIconBtn) {
                fetchIconBtn.disabled = true;
                fetchIconBtn.textContent = '获取中...';
            }

            try {
                await this.handleAutoFetchIcon(url);
            } catch (error) {
                console.error('手动获取图标失败:', error);
                alert('获取图标失败，请检查网址是否正确');
            } finally {
                // 恢复按钮状态
                if (fetchIconBtn) {
                    fetchIconBtn.disabled = false;
                    fetchIconBtn.textContent = '获取图标';
                }
            }
        }

        // 从URL提取网站名称
        extractSiteName(url) {
            try {
                const urlObj = new URL(url);
                const hostname = urlObj.hostname.replace(/^www\./, '');

                // 常见网站名称映射
                const siteNameMap = {
                    'github.com': 'GitHub',
                    'stackoverflow.com': 'Stack Overflow',
                    'youtube.com': 'YouTube',
                    'google.com': 'Google',
                    'baidu.com': '百度',
                    'zhihu.com': '知乎',
                    'weibo.com': '微博',
                    'bilibili.com': 'B站',
                    'taobao.com': '淘宝',
                    'jd.com': '京东',
                    'tmall.com': '天猫',
                    'douban.com': '豆瓣',
                    'csdn.net': 'CSDN',
                    'juejin.cn': '掘金',
                    'xiaohongshu.com': '小红书',
                    'iqiyi.com': '爱奇艺',
                    'qq.com': 'QQ',
                    'wechat.com': '微信',
                    'alipay.com': '支付宝'
                };

                if (siteNameMap[hostname]) {
                    return siteNameMap[hostname];
                }

                // 提取主域名并首字母大写
                const parts = hostname.split('.');
                const mainDomain = parts[0];
                return mainDomain.charAt(0).toUpperCase() + mainDomain.slice(1);
            } catch (error) {
                return '';
            }
        }

        // 处理图标输入框变化
        handleIconInputChange(value) {
            if (value.trim()) {
                this.updateIconPreview(value.trim(), 'emoji');
            } else {
                this.updateIconPreview('🌐', 'emoji');
            }
        }

        // 更新图标预览
        updateIconPreview(iconData, type) {
            const { previewFavicon, previewEmoji } = this.modalElements;

            if (type === 'favicon' && previewFavicon && previewEmoji) {
                previewFavicon.src = iconData;
                previewFavicon.style.display = 'inline';
                previewEmoji.style.display = 'none';
            } else if (type === 'emoji' && previewFavicon && previewEmoji) {
                previewFavicon.style.display = 'none';
                previewEmoji.textContent = iconData;
                previewEmoji.style.display = 'inline';
            }
        }

        // 从URL提取网站名称
        extractSiteName(url) {
            try {
                const urlObj = new URL(url);
                const hostname = urlObj.hostname;

                // 移除www前缀
                const domain = hostname.replace(/^www\./, '');

                // 特殊域名映射
                const domainMap = {
                    'web.okjike.com': '即刻',
                    'okjike.com': '即刻',
                    'github.com': 'GitHub',
                    'stackoverflow.com': 'Stack Overflow',
                    'zhihu.com': '知乎',
                    'bilibili.com': '哔哩哔哩',
                    'weibo.com': '微博',
                    'douban.com': '豆瓣',
                    'jianshu.com': '简书',
                    'csdn.net': 'CSDN',
                    'juejin.cn': '掘金',
                    'segmentfault.com': 'SegmentFault'
                };

                // 检查特殊映射
                if (domainMap[domain]) {
                    return domainMap[domain];
                }

                // 提取主域名（移除顶级域名）
                const parts = domain.split('.');
                if (parts.length >= 2) {
                    // 对于子域名，优先使用主域名而不是子域名
                    // 例如：web.okjike.com -> okjike，而不是web
                    const mainDomain = parts.length >= 3 ? parts[parts.length - 2] : parts[0];
                    // 首字母大写
                    return mainDomain.charAt(0).toUpperCase() + mainDomain.slice(1);
                }

                return domain;
            } catch (error) {
                return '';
            }
        }

        bindContextMenuEvents() {
            const editItem = document.getElementById('editShortcut');
            const deleteItem = document.getElementById('deleteShortcut');

            if (editItem) {
                editItem.addEventListener('click', () => {
                    this.editShortcut(this.contextMenuTargetId);
                    this.hideContextMenu();
                });
            }

            if (deleteItem) {
                deleteItem.addEventListener('click', () => {
                    this.deleteShortcut(this.contextMenuTargetId);
                    this.hideContextMenu();
                });
            }

            // 使用全局事件管理器避免重复监听器
            this.globalClickHandler = () => {
                this.hideContextMenu();
            };
            GlobalEventManager.addClickHandler(this.globalClickHandler);
        }

        openShortcut(url) {
            if (Utils.isValidUrl(url)) {
                window.open(url, '_blank');
            } else {
                console.error('无效的URL:', url);
            }
        }

        showContextMenu(event, shortcutId) {
            const contextMenu = document.getElementById('contextMenu');
            if (!contextMenu) return;

            this.contextMenuTargetId = shortcutId;

            contextMenu.style.display = 'block';
            contextMenu.style.left = event.pageX + 'px';
            contextMenu.style.top = event.pageY + 'px';
        }

        hideContextMenu() {
            const contextMenu = document.getElementById('contextMenu');
            if (contextMenu) {
                contextMenu.style.display = 'none';
            }
        }

        showModal(title = '添加图标', shortcut = null) {
            const { modal, title: modalTitle, nameInput, urlInput, iconStatus } = this.modalElements;

            if (!modal) return;

            // 清除之前的错误提示
            this.hideModalError();

            if (modalTitle) modalTitle.textContent = title;

            if (shortcut) {
                if (nameInput) nameInput.value = shortcut.name;
                if (urlInput) urlInput.value = shortcut.url;
                this.currentEditingId = shortcut.id;

                // 初始化图标预览 - 优先显示真实的favicon
                if (shortcut.faviconUrl && shortcut.faviconUrl !== this.faviconManager.getDefaultIcon()) {
                    this.updateIconPreview(shortcut.faviconUrl, 'favicon');
                    if (iconStatus) iconStatus.textContent = '已设置图标（真实网站图标）';
                } else {
                    this.updateIconPreview(shortcut.icon, 'emoji');
                    if (iconStatus) iconStatus.textContent = '已设置图标（表情符号）';
                }
            } else {
                if (nameInput) {
                    nameInput.value = '';
                    nameInput.placeholder = '自动解析中...';
                }
                if (urlInput) urlInput.value = '';
                this.currentEditingId = null;

                // 初始化图标预览为默认图标
                this.updateIconPreview('🌐', 'emoji');
                if (iconStatus) iconStatus.textContent = '请输入URL自动获取图标';
            }

            modal.style.display = 'flex';

            // 聚焦到URL输入框
            setTimeout(() => {
                if (urlInput) urlInput.focus();
            }, 100);
        }

        hideModal() {
            const { modal } = this.modalElements;
            if (modal) {
                modal.style.display = 'none';
            }
            this.currentEditingId = null;
            // 隐藏错误提示
            this.hideModalError();
        }

        // 获取错误提示元素（避免重复查询）
        getModalErrorElements() {
            return {
                errorElement: document.getElementById('modalErrorMessage'),
                errorText: document.getElementById('modalErrorText')
            };
        }

        // 显示弹窗内错误提示
        showModalError(message) {
            const { errorElement, errorText } = this.getModalErrorElements();

            if (errorElement && errorText) {
                errorText.textContent = message;
                errorElement.style.display = 'flex';

                // 滚动到错误提示位置
                errorElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
        }

        // 隐藏弹窗内错误提示
        hideModalError() {
            const { errorElement } = this.getModalErrorElements();
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        }

        // 验证快捷搜索模式下的URL格式
        validateQuickSearchUrl(url) {
            // 检查URL是否包含搜索参数占位符或搜索关键词参数
            const searchPatterns = [
                '{query}', '{keyword}', '{searchTerms}', // 常见占位符
                'q=', 'query=', 'search=', 'keyword=', 'wd=', 's=', // 常见搜索参数
                '/search?', '/s?', '?q=', '?search=', '?keyword=' // 搜索路径模式
            ];

            const lowerUrl = url.toLowerCase();
            const hasSearchPattern = searchPatterns.some(pattern => lowerUrl.includes(pattern.toLowerCase()));

            if (!hasSearchPattern) {
                return {
                    valid: false,
                    error: '该URL不是快捷搜索平台，缺少搜索关键词参数，添加失败'
                };
            }

            return { valid: true };
        }

        handleModalConfirm() {
            // 防止重复提交
            if (this._isSubmitting) {
                return;
            }
            this._isSubmitting = true;

            const nameInput = document.getElementById('shortcutName');
            const urlInput = document.getElementById('shortcutUrl');
            const previewFavicon = document.getElementById('previewFavicon');
            const previewEmoji = document.getElementById('previewEmoji');

            const name = nameInput?.value.trim();
            const url = urlInput?.value.trim();

            // 获取当前显示的图标
            let icon = '🌐'; // 默认图标
            if (previewFavicon && previewFavicon.style.display !== 'none' && previewFavicon.src) {
                icon = previewFavicon.src;
            } else if (previewEmoji && previewEmoji.style.display !== 'none') {
                icon = previewEmoji.textContent;
            }

            if (!name || !url) {
                this.showModalError('请填写名称和URL');
                this._isSubmitting = false;
                return;
            }

            if (!Utils.isValidUrl(url)) {
                this.showModalError('请输入有效的URL');
                this._isSubmitting = false;
                return;
            }

            // 快捷搜索模式下的URL格式验证
            const currentPageMode = window.app?.pageManager?.pages[window.app?.pageManager?.currentPageIndex]?.mode || 'normal';
            if (currentPageMode === 'quick' && !this.currentEditingId) {
                // 只在添加模式下验证，编辑模式允许修改
                const validationResult = this.validateQuickSearchUrl(url);
                if (!validationResult.valid) {
                    this.showModalError(validationResult.error);
                    this._isSubmitting = false;
                    return;
                }
            }

            try {
                if (this.currentEditingId) {
                    // 编辑模式
                    this.updateShortcut(this.currentEditingId, { name, url, icon });
                } else {
                    // 添加模式
                    this.addShortcut(name, url, icon);
                }

                this.hideModal();
            } finally {
                // 重置提交状态
                this._isSubmitting = false;
            }
        }

        async addShortcut(name, url, icon, category = 'productivity') {
            if (this.shortcuts.length >= this.maxShortcuts) {
                alert(`最多只能添加 ${this.maxShortcuts} 个快捷方式`);
                return false;
            }

            // 获取当前页面模式，确保只添加到当前页面
            const currentPageMode = window.app?.pageManager?.pages[window.app?.pageManager?.currentPageIndex]?.mode || 'normal';

            // 标准化URL
            const normalizedUrl = url.startsWith('http') ? url : 'https://' + url;

            const newShortcut = {
                id: Utils.generateId(),
                name,
                url: normalizedUrl,
                icon,
                order: this.shortcuts.length + 1,
                category,
                src: '', // 使用src字段保存图标URL（参考itabdate.md格式）
                // 添加页面模式和搜索模式标识
                pageMode: currentPageMode,
                searchMode: currentPageMode === 'quick' ? 'search' : 'direct'
            };

            // 如果是搜索直达模式，尝试解析URL生成搜索模板
            if (currentPageMode === 'quick') {
                try {
                    const platformManager = window.app?.searchManager?.platformManager;
                    if (platformManager && platformManager.urlParser) {
                        const parseResult = await platformManager.urlParser.parseURL(normalizedUrl);
                        if (parseResult.success && parseResult.template) {
                            // 使用解析出的搜索模板作为searchUrl
                            newShortcut.searchUrl = parseResult.template;
                            console.log(`搜索直达模式：为 ${name} 生成搜索模板: ${parseResult.template}`);
                        } else {
                            console.log(`搜索直达模式：无法为 ${name} 生成搜索模板，将使用直接跳转模式`);
                            newShortcut.searchMode = 'direct';
                        }
                    }
                } catch (error) {
                    console.warn('URL解析失败，使用直接跳转模式:', error);
                    newShortcut.searchMode = 'direct';
                }
            }

            // 只添加到当前页面的shortcuts数组
            this.shortcuts.push(newShortcut);

            // 立即保存到当前页面，避免跨页面重复
            this.saveShortcuts();
            await this.render();

            // 异步获取并缓存图标
            try {
                const faviconUrl = await this.faviconManager.getFavicon(newShortcut.url);
                if (faviconUrl && faviconUrl !== this.faviconManager.getDefaultIcon()) {
                    newShortcut.faviconUrl = faviconUrl;
                    this.updateShortcutIcon(newShortcut.id, faviconUrl);
                    // 只有在图标真正更新时才保存，避免重复保存
                    this.saveShortcuts();
                }
            } catch (error) {
                console.warn('获取新图标失败:', error);
            }

            console.log(`已添加到${currentPageMode === 'quick' ? '快捷搜索' : '常规搜索'}页面: ${name}`);
            return true;
        }

        updateShortcut(id, data) {
            const index = this.shortcuts.findIndex(s => s.id == id);
            if (index !== -1) {
                this.shortcuts[index] = {
                    ...this.shortcuts[index],
                    ...data,
                    url: data.url && !data.url.startsWith('http') ? 'https://' + data.url : data.url
                };
                this.saveShortcuts();
                this.render();
            }
        }

        deleteShortcut(id) {
            // 获取要删除的快捷方式信息
            const shortcut = this.shortcuts.find(s => s.id == id);
            const shortcutName = shortcut ? shortcut.name : '未知';

            // 直接删除，无需确认对话框
            this.shortcuts = this.shortcuts.filter(s => s.id != id);

            // 立即保存到当前页面
            this.saveShortcuts();
            this.render();

            // 删除成功，无需提示（保持界面简洁）
            console.log(`已删除快捷方式: ${shortcutName}`);
        }

        editShortcut(id) {
            const shortcut = this.shortcuts.find(s => s.id == id);
            if (shortcut) {
                this.showModal('编辑图标', shortcut);
            }
        }

        // 公共方法供设置面板调用
        showAddModal() {
            this.showModal('添加快捷方式');
        }

        resetToDefault() {
            if (confirm('确定要重置为默认快捷方式吗？这将删除所有自定义快捷方式。')) {
                this.shortcuts = [...DEFAULT_SHORTCUTS];
                this.saveShortcuts();
                this.render();
            }
        }

        // 拖拽排序相关方法（已移除无用的reorderShortcuts方法）

        getShortcutById(id) {
            return this.shortcuts.find(s => s.id == id);
        }

        /**
         * 添加文件夹到指定位置
         * @param {Object} folder 文件夹对象
         * @param {number} position 插入位置
         */
        addFolderAtPosition(folder, position) {
            // 调整其他图标的order值
            this.shortcuts.forEach(shortcut => {
                if (shortcut.order >= position) {
                    shortcut.order++;
                }
            });

            // 设置文件夹的order值
            folder.order = position;

            // 添加文件夹到列表
            this.shortcuts.push(folder);

            // 保存数据
            this.saveShortcuts();

            // 在批量模式下跳过渲染，避免多次重新渲染
            if (!this.batchMode) {
                this.render();
            }
        }

        /**
         * 移除快捷方式（支持文件夹）- 增强版：包含双向同步
         * @param {string} id 快捷方式或文件夹ID
         * @param {Object} options 选项参数
         * @param {boolean} options.skipPlatformSync 是否跳过平台同步（避免循环调用）
         */
        removeShortcut(id, options = {}) {
            const index = this.shortcuts.findIndex(s => s.id == id);
            if (index !== -1) {
                const shortcut = this.shortcuts[index];

                // 双向同步：如果是搜索模式的快捷方式，同时从PlatformManager中删除
                // 除非明确指定跳过同步（避免反向同步时的循环调用）
                if (shortcut.searchMode === 'search' && !options.skipPlatformSync) {
                    this.syncDeletePlatformFromPlatformManager(shortcut);
                }

                this.shortcuts.splice(index, 1);

                // 重新计算order值
                this.shortcuts.forEach((shortcut, idx) => {
                    shortcut.order = idx + 1;
                });

                this.saveShortcuts();

                // 在批量模式下跳过渲染，避免多次重新渲染
                if (!this.batchMode) {
                    this.render();
                }
                return true;
            }
            return false;
        }

        // 同步删除PlatformManager中对应的平台（优化版：复用匹配逻辑）
        async syncDeletePlatformFromPlatformManager(shortcut) {
            try {
                const platformManager = window.app?.searchManager?.platformManager;
                if (!platformManager) {
                    console.warn('PlatformManager不存在，无法同步删除');
                    return;
                }

                console.log(`🔄 尝试同步删除PlatformManager中的平台: ${shortcut.name}`);

                // 复用统一的匹配逻辑
                const matchingPlatform = this.findMatchingPlatform(
                    Array.from(platformManager.platforms.values()),
                    shortcut
                );

                if (matchingPlatform && !matchingPlatform.isBuiltin) {
                    // 删除对应的平台（不显示确认对话框，因为用户已经在主页确认删除）
                    // 直接删除，跳过反向同步避免循环调用
                    await this.deletePlatformDirectly(platformManager, matchingPlatform.id);
                    console.log(`✅ 已同步删除PlatformManager中的平台: ${shortcut.name}`);

                    // 刷新设置页面的平台列表UI
                    this.refreshSettingsPlatformListUI();
                } else if (matchingPlatform && matchingPlatform.isBuiltin) {
                    console.log(`⚠️ 跳过删除内置平台: ${shortcut.name}`);
                } else {
                    console.log(`⚠️ 未找到对应的PlatformManager平台: ${shortcut.name}`);
                }
            } catch (error) {
                console.error('❌ 同步删除PlatformManager平台失败:', error);
            }
        }

        // 统一的平台/快捷方式匹配逻辑
        findMatchingPlatform(platforms, target) {
            return platforms.find(p =>
                p.name === target.name ||
                p.url === target.url ||
                (p.searchUrl && p.searchUrl === target.searchUrl) ||
                (target.searchUrl && target.searchUrl === p.url)
            );
        }

        // 直接删除平台（跳过反向同步，避免循环调用）
        async deletePlatformDirectly(platformManager, platformId) {
            try {
                const platform = platformManager.platforms.get(platformId);
                if (!platform) {
                    throw new Error('平台不存在');
                }

                if (platform.isBuiltin) {
                    throw new Error('不能删除内置平台');
                }

                // 直接从平台列表移除（跳过反向同步）
                platformManager.platforms.delete(platformId);

                // 保存到存储
                await platformManager.savePlatforms();

                // 更新别名映射
                platformManager.updateAliasMap();

                return true;
            } catch (error) {
                console.error('直接删除平台失败:', error);
                throw error;
            }
        }

        // 刷新设置页面的平台列表UI（优化版：直接访问）
        refreshSettingsPlatformListUI() {
            try {
                // 直接访问已知的刷新方法路径
                const refreshMethod = window.app?.settingsManager?.refreshPlatformList;
                if (refreshMethod && typeof refreshMethod === 'function') {
                    console.log(`🔄 调用设置页面刷新方法`);
                    refreshMethod.call(window.app.settingsManager);
                    console.log(`✅ 设置页面平台列表UI已刷新`);
                } else {
                    console.log(`⚠️ 未找到设置页面刷新方法，UI可能需要手动刷新`);
                }
            } catch (error) {
                console.error('❌ 刷新设置页面平台列表UI失败:', error);
            }
        }

        // 交换两个快捷方式的位置
        swapShortcuts(id1, id2) {
            const index1 = this.shortcuts.findIndex(s => s.id == id1);
            const index2 = this.shortcuts.findIndex(s => s.id == id2);

            if (index1 === -1 || index2 === -1) {
                console.error('无法找到要交换的快捷方式');
                return false;
            }

            // 交换数组中的位置
            [this.shortcuts[index1], this.shortcuts[index2]] = [this.shortcuts[index2], this.shortcuts[index1]];

            // 重新计算order值
            this.shortcuts.forEach((shortcut, index) => {
                shortcut.order = index + 1;
            });

            // 保存并重新渲染
            this.saveShortcuts();
            this.render();

            console.log(`已交换 ${this.shortcuts[index2].name} 和 ${this.shortcuts[index1].name} 的位置`);
            return true;
        }

        // 将快捷方式重排序到指定索引位置
        reorderToIndex(shortcutId, targetIndex) {
            const currentIndex = this.shortcuts.findIndex(s => s.id == shortcutId);

            if (currentIndex === -1) {
                console.error('无法找到要重排序的快捷方式');
                return false;
            }

            // 边界检查
            targetIndex = Math.max(0, Math.min(targetIndex, this.shortcuts.length - 1));

            if (currentIndex === targetIndex) {
                console.log('位置未发生变化，无需重排序');
                return true;
            }

            // 移除元素并插入到新位置
            const [movedItem] = this.shortcuts.splice(currentIndex, 1);
            this.shortcuts.splice(targetIndex, 0, movedItem);

            // 重新计算order值
            this.shortcuts.forEach((shortcut, index) => {
                shortcut.order = index + 1;
            });

            // 保存数据
            this.saveShortcuts();

            // 使用快速DOM重排序而不是完整重新渲染
            this.fastReorderDOM(shortcutId, currentIndex, targetIndex);

            console.log(`已将 ${movedItem.name} 从位置 ${currentIndex} 移动到位置 ${targetIndex}`);
            return true;
        }

        /**
         * 快速DOM重排序，避免完整重新渲染
         * @param {string} id 快捷方式ID
         * @param {number} fromIndex 原始索引
         * @param {number} toIndex 目标索引（数组操作后的索引）
         */
        fastReorderDOM(id, fromIndex, toIndex) {
            if (!this.container) return;

            const items = [...this.container.querySelectorAll('.shortcut-item:not(.shortcut-add-button)')];
            const draggedElement = items.find(item => item.dataset.id === id);

            if (!draggedElement) {
                console.warn('未找到拖拽元素，回退到完整渲染');
                this.render();
                return;
            }

            // 移除拖拽元素
            draggedElement.remove();

            // 重新获取剩余元素列表（移除拖拽元素后）
            const remainingItems = [...this.container.querySelectorAll('.shortcut-item:not(.shortcut-add-button)')];

            // 计算正确的DOM插入位置
            let insertPosition = toIndex;

            // 如果向后移动，需要调整插入位置
            if (toIndex > fromIndex) {
                // 向后移动时，由于原元素已被移除，实际插入位置需要减1
                insertPosition = toIndex;
            } else {
                // 向前移动时，插入位置保持不变
                insertPosition = toIndex;
            }

            // 边界检查
            insertPosition = Math.max(0, Math.min(insertPosition, remainingItems.length));

            // 找到插入位置的目标元素
            const targetElement = remainingItems[insertPosition];

            if (targetElement) {
                // 插入到目标位置前
                this.container.insertBefore(draggedElement, targetElement);
            } else {
                // 插入到末尾（在添加按钮前）
                const addButton = this.container.querySelector('.shortcut-add-button');
                if (addButton) {
                    this.container.insertBefore(draggedElement, addButton);
                } else {
                    this.container.appendChild(draggedElement);
                }
            }

            console.log(`✅ 快速DOM重排序完成: ${fromIndex} -> ${toIndex} (DOM位置: ${insertPosition})`);
        }

        // 创建文件夹
        createFolder(draggedId, targetId) {
            const draggedShortcut = this.getShortcutById(draggedId);
            const targetShortcut = this.getShortcutById(targetId);

            if (!draggedShortcut || !targetShortcut) {
                console.error('无法找到要合并的快捷方式');
                return false;
            }

            // 生成文件夹ID和名称
            const folderId = Utils.generateId();
            const folderName = this.generateFolderName(draggedShortcut, targetShortcut);

            // 创建文件夹数据结构
            const newFolder = {
                id: folderId,
                type: 'folder',
                name: folderName,
                icon: '📁',
                children: [
                    { ...draggedShortcut, parentId: folderId },
                    { ...targetShortcut, parentId: folderId }
                ],
                order: Math.min(draggedShortcut.order, targetShortcut.order),
                isExpanded: false,
                url: '', // 文件夹没有直接URL
                faviconUrl: '',
                category: 'folder'
            };

            // 移除原始快捷方式
            this.shortcuts = this.shortcuts.filter(s => s.id !== draggedId && s.id !== targetId);

            // 添加新文件夹
            this.shortcuts.push(newFolder);

            // 重新排序
            this.shortcuts.forEach((shortcut, index) => {
                shortcut.order = index + 1;
            });

            // 保存并重新渲染
            this.saveShortcuts();
            this.render();

            console.log(`已创建文件夹 "${folderName}"，包含: ${draggedShortcut.name}, ${targetShortcut.name}`);
            return true;
        }

        // 智能生成文件夹名称
        generateFolderName(shortcut1, shortcut2) {
            // 预定义的分类关键词
            const categories = {
                'search': { keywords: ['Google', 'Bing', '百度', 'Yahoo', 'DuckDuckGo'], name: '搜索引擎' },
                'social': { keywords: ['微博', '知乎', 'Twitter', 'Facebook', 'Instagram', 'LinkedIn'], name: '社交媒体' },
                'work': { keywords: ['GitHub', 'Gmail', 'Slack', 'Notion', 'Trello', 'Asana'], name: '工作工具' },
                'entertainment': { keywords: ['YouTube', 'Netflix', 'Spotify', 'Bilibili', '爱奇艺'], name: '娱乐' },
                'shopping': { keywords: ['淘宝', '京东', 'Amazon', '天猫', '拼多多'], name: '购物' },
                'dev': { keywords: ['Stack Overflow', 'MDN', 'CodePen', 'JSFiddle'], name: '开发工具' }
            };

            // 检查是否匹配某个分类
            for (const [, config] of Object.entries(categories)) {
                const matchCount = config.keywords.filter(keyword =>
                    shortcut1.name.includes(keyword) || shortcut2.name.includes(keyword) ||
                    shortcut1.url.toLowerCase().includes(keyword.toLowerCase()) ||
                    shortcut2.url.toLowerCase().includes(keyword.toLowerCase())
                ).length;

                if (matchCount > 0) {
                    return config.name;
                }
            }

            // 如果没有匹配的分类，使用通用名称
            return `${shortcut1.name} & ${shortcut2.name}`;
        }

        // 创建文件夹
        createFolder(draggedId, targetId) {
            const draggedShortcut = this.getShortcutById(draggedId);
            const targetShortcut = this.getShortcutById(targetId);

            if (!draggedShortcut || !targetShortcut) {
                console.error('无法找到要合并的快捷方式');
                return false;
            }

            // 生成文件夹ID和名称
            const folderId = Utils.generateId();
            const folderName = this.generateFolderName(draggedShortcut, targetShortcut);

            // 创建文件夹数据结构
            const newFolder = {
                id: folderId,
                type: 'folder',
                name: folderName,
                icon: '📁',
                children: [
                    { ...draggedShortcut, parentId: folderId },
                    { ...targetShortcut, parentId: folderId }
                ],
                order: Math.min(draggedShortcut.order, targetShortcut.order),
                isExpanded: false,
                url: '', // 文件夹没有直接URL
                faviconUrl: '',
                category: 'folder'
            };

            // 移除原始快捷方式
            this.shortcuts = this.shortcuts.filter(s => s.id !== draggedId && s.id !== targetId);

            // 添加新文件夹
            this.shortcuts.push(newFolder);

            // 重新排序
            this.shortcuts.forEach((shortcut, index) => {
                shortcut.order = index + 1;
            });

            // 保存并重新渲染
            this.saveShortcuts();
            this.render();

            console.log(`已创建文件夹 "${folderName}"，包含: ${draggedShortcut.name}, ${targetShortcut.name}`);
            return true;
        }

        // 智能生成文件夹名称
        generateFolderName(shortcut1, shortcut2) {
            // 预定义的分类关键词
            const categories = {
                'search': { keywords: ['Google', 'Bing', '百度', 'Yahoo', 'DuckDuckGo'], name: '搜索引擎' },
                'social': { keywords: ['微博', '知乎', 'Twitter', 'Facebook', 'Instagram', 'LinkedIn'], name: '社交媒体' },
                'work': { keywords: ['GitHub', 'Gmail', 'Slack', 'Notion', 'Trello', 'Asana'], name: '工作工具' },
                'entertainment': { keywords: ['YouTube', 'Netflix', 'Spotify', 'Bilibili', '爱奇艺'], name: '娱乐' },
                'shopping': { keywords: ['淘宝', '京东', 'Amazon', '天猫', '拼多多'], name: '购物' },
                'dev': { keywords: ['Stack Overflow', 'MDN', 'CodePen', 'JSFiddle'], name: '开发工具' }
            };

            // 检查是否匹配某个分类
            for (const [category, config] of Object.entries(categories)) {
                const matchCount = config.keywords.filter(keyword =>
                    shortcut1.name.includes(keyword) || shortcut2.name.includes(keyword) ||
                    shortcut1.url.toLowerCase().includes(keyword.toLowerCase()) ||
                    shortcut2.url.toLowerCase().includes(keyword.toLowerCase())
                ).length;

                if (matchCount > 0) {
                    return config.name;
                }
            }

            // 如果没有匹配的分类，使用通用名称
            return `${shortcut1.name} & ${shortcut2.name}`;
        }











        exportShortcuts() {
            const data = {
                shortcuts: this.shortcuts,
                exportTime: new Date().toISOString(),
                version: '3.0.0'
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'moment-search-shortcuts.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        importShortcuts(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    if (data.shortcuts && Array.isArray(data.shortcuts)) {
                        if (confirm('确定要导入快捷方式吗？这将覆盖现有的快捷方式。')) {
                            this.shortcuts = data.shortcuts;
                            this.saveShortcuts();
                            this.render();
                            alert('导入成功！');
                        }
                    } else {
                        alert('无效的文件格式');
                    }
                } catch (error) {
                    alert('文件解析失败');
                    console.error('导入失败:', error);
                }
            };
            reader.readAsText(file);
        }
    }

    // 设置管理器
    class SettingsManager {
        constructor() {
            this.settings = this.getDefaultSettings();
            this.panel = document.getElementById('settingsPanel');
            this.content = document.getElementById('settingsContent');
            this.currentSection = 'appearance'; // 跟踪当前激活的页面
            this.initPromise = null; // 用于跟踪初始化状态

            this.init();
        }

        getDefaultSettings() {
            return {
                search: {
                    defaultPlatform: 'all',
                    smartMatch: true,
                    autoClear: true
                },
                appearance: {
                    theme: 'auto',
                    wallpaper: 'default',
                    sidebarAutoHide: false,
                    sidebarPosition: 'left'
                },
                time: {
                    showTime: true,
                    showDate: true,
                    showWeekday: true,
                    format24h: true,
                    showSeconds: false,
                    fontSize: 50, // 现在使用百分比，50%为默认值
                    color: '#ffffff',
                    fontFamily: 'arial', // 字体系列，默认使用Arial
                    fontWeight: 400 // 字体粗细
                },
                shortcuts: {
                    maxCount: 20,
                    showNames: true,
                    iconSize: 60, // 改为数值，默认60px
                    iconSpacing: 16, // 图标间距，默认16px
                    containerWidth: 80, // 展示区域宽度百分比，默认80%
                    textColor: '#ffffff', // 图标名称文字颜色
                    modeSwitch: 'Ctrl+Q', // 模式切换快捷键
                    platformSwitch: 'Tab' // 平台切换快捷键
                }
            };
        }

        // 字体系列映射配置
        getFontFamilyMap() {
            return {
                'arial': '"Arial", "Helvetica", sans-serif',
                'monospace': '"Consolas", "Monaco", "Courier New", monospace',
                'yahei': '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif',
                'times': '"Times New Roman", "Times", serif'
            };
        }

        // 字体粗细名称映射配置
        getFontWeightNames() {
            return {
                400: 'Normal',
                600: 'Semi Bold'
            };
        }

        init() {
            this.initPromise = this.performInit();
            return this.initPromise;
        }

        async performInit() {
            this.loadSettings();
            this.bindEvents();
            this.applySettings();
            console.log('✅ 设置管理器初始化完成');
        }

        // 等待初始化完成的方法
        async waitForInit() {
            if (this.initPromise) {
                await this.initPromise;
            }
        }

        loadSettings() {
            const saved = Storage.get('settings_v3');
            if (saved) {
                this.settings = this.mergeSettings(this.settings, saved);
                // 数据迁移：将旧的字符串图标大小转换为数字
                this.migrateIconSizeSettings();
            }
        }

        migrateIconSizeSettings() {
            const shortcuts = this.settings.shortcuts;
            if (!shortcuts || typeof shortcuts.iconSize !== 'string') return;

            // 迁移旧的字符串图标大小到数字
            const sizeMap = { 'small': 48, 'medium': 60, 'large': 72 };
            shortcuts.iconSize = sizeMap[shortcuts.iconSize] || 60;

            // 添加缺失的新设置项
            const defaults = {
                iconSpacing: 16,
                containerWidth: 80,
                textColor: '#ffffff'
            };

            Object.entries(defaults).forEach(([key, value]) => {
                if (!shortcuts[key]) shortcuts[key] = value;
            });

            this.saveSettings();
            console.log('图标设置已迁移到新格式');
        }

        mergeSettings(defaults, saved) {
            const result = { ...defaults };
            for (const key in saved) {
                if (typeof saved[key] === 'object' && saved[key] !== null) {
                    result[key] = { ...defaults[key], ...saved[key] };
                } else {
                    result[key] = saved[key];
                }
            }
            return result;
        }

        saveSettings() {
            Storage.set('settings_v3', this.settings);
        }

        bindEvents() {
            const settingsBtn = document.getElementById('settingsBtn');
            const closeBtn = document.getElementById('closeSettings');
            const overlay = document.getElementById('settingsOverlay');

            if (settingsBtn) {
                settingsBtn.addEventListener('click', () => this.showPanel());
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.hidePanel());
            }

            if (overlay) {
                overlay.addEventListener('click', () => this.hidePanel());
            }

            // ESC键关闭设置面板
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.panel?.classList.contains('show')) {
                    this.hidePanel();
                }
            });
        }

        showPanel() {
            const overlay = document.getElementById('settingsOverlay');
            if (this.panel) {
                this.panel.classList.add('show');
                this.renderSettingsContent();
            }
            if (overlay) {
                overlay.classList.add('show');
            }
        }

        hidePanel() {
            const overlay = document.getElementById('settingsOverlay');
            if (this.panel) {
                this.panel.classList.remove('show');
            }
            if (overlay) {
                overlay.classList.remove('show');
            }
        }

        renderSettingsContent() {
            if (!this.content) return;

            this.content.innerHTML = `
                ${this.generateNavigationHTML()}
                ${this.generateMainContentHTML()}
            `;

            // 恢复当前激活的页面状态
            this.switchSection(this.currentSection);

            // 每次重新渲染后都需要重新绑定事件，因为DOM元素被重新创建
            this.bindSettingsEvents();
        }

        generateNavigationHTML() {
            const navItems = [
                { section: 'appearance', label: '外观设置' },
                { section: 'time', label: '时间设置' },
                { section: 'shortcuts', label: '图标设置' },
                { section: 'search', label: '搜索设置' },
                { section: 'platforms', label: '搜索直达' },
                { section: 'advanced', label: '高级功能' },
                { section: 'data', label: '数据管理' },
                { section: 'about', label: '关于' }
            ];

            return `
                <div class="settings-nav">
                    ${navItems.map(item => `
                        <button class="settings-nav-item" data-section="${item.section}">
                            ${item.label}
                        </button>
                    `).join('')}
                </div>
            `;
        }

        generateMainContentHTML() {
            return `
                <div class="settings-main">
                    ${this.generateSearchSection()}
                    ${this.generateAppearanceSection()}
                    ${this.generateTimeSection()}
                    ${this.generateShortcutsSection()}
                    ${this.generatePlatformsSection()}
                    ${this.generateAdvancedSection()}
                    ${this.generateDataSection()}
                    ${this.generateAboutSection()}
                </div>
            `;
        }

        generateSearchSection() {
            return `
                <div class="settings-section" data-section="search">
                    <div class="setting-group">
                        <h4>搜索模式</h4>
                        ${this.generateSettingItem('模式切换快捷键', this.generateCustomShortcutInput())}
                        ${this.generateSettingItem('平台切换快捷键', this.generatePlatformSwitchShortcutInput())}
                    </div>
                    <div class="setting-group">
                        <h4>搜索行为</h4>
                        ${this.generateSettingItem('智能匹配', this.generateSwitch('search.smartMatch', this.settings.search?.smartMatch !== false))}
                        ${this.generateSettingItem('自动清空搜索框', this.generateSwitch('search.autoClear', this.settings.search?.autoClear !== false))}
                    </div>
                </div>
            `;
        }

        generateAppearanceSection() {
            // 检查是否有自定义壁纸
            const hasCustomWallpaper = this.settings.appearance?.wallpaper === 'custom';

            const wallpaperControls = hasCustomWallpaper
                ? `<div class="wallpaper-controls">
                     <button class="setting-btn secondary setting-inline-btn" data-action="wallpaper">选择新壁纸</button>
                     <button class="setting-btn secondary setting-inline-btn" data-action="edit-wallpaper">编辑当前壁纸</button>
                   </div>`
                : '<button class="setting-btn secondary setting-inline-btn" data-action="wallpaper">选择壁纸</button>';

            return `
                <div class="settings-section" data-section="appearance">
                    <div class="setting-group">
                        ${this.generateSettingItem('主题模式', this.generateBasicThemeSelect())}
                        ${this.generateSettingItem('侧边栏自动隐藏', this.generateSwitch('appearance.sidebarAutoHide', this.settings.appearance?.sidebarAutoHide === true))}
                        ${this.generateSettingItem('侧边栏位置', this.generateSidebarPositionSelect())}
                        ${this.generateSettingItem('自定义壁纸', wallpaperControls)}
                    </div>
                </div>
            `;
        }

        generateTimeSection() {
            return `
                <div class="settings-section" data-section="time">
                    <div class="setting-group">
                        <h4>显示控制</h4>
                        ${this.generateSettingItem('显示时间', this.generateSwitch('time.showTime', this.settings.time?.showTime !== false))}
                        ${this.generateSettingItem('月日显示', this.generateSwitch('time.showDate', this.settings.time?.showDate !== false))}
                        ${this.generateSettingItem('星期显示', this.generateSwitch('time.showWeekday', this.settings.time?.showWeekday !== false))}
                        ${this.generateSettingItem('24小时制', this.generateSwitch('time.format24h', this.settings.time?.format24h !== false))}
                        ${this.generateSettingItem('秒数显示', this.generateSwitch('time.showSeconds', this.settings.time?.showSeconds || false))}
                    </div>
                    <div class="setting-group">
                        <h4>样式自定义</h4>
                        ${this.generateSettingItem('大小', this.generateSizeSlider())}
                        ${this.generateSettingItem('颜色', this.generateColorPicker())}
                        ${this.generateSettingItem('字体', this.generateFontSelector())}
                        ${this.generateSettingItem('粗细', this.generateFontWeightSelector())}
                    </div>
                </div>
            `;
        }

        generateShortcutsSection() {
            return `
                <div class="settings-section" data-section="shortcuts">
                    <div class="setting-group">
                        ${this.generateSettingItem('图标大小', this.generateIconSizeSlider())}
                        ${this.generateSettingItem('图标间距', this.generateIconSpacingSlider())}
                        ${this.generateSettingItem('展示区域宽度', this.generateContainerWidthSlider())}
                        ${this.generateSettingItem('图标名称显示控制', this.generateSwitch('shortcuts.showNames', this.settings.shortcuts.showNames))}
                        ${this.generateSettingItem('文字颜色', this.generateTextColorPicker())}
                    </div>
                </div>
            `;
        }

        generatePlatformsSection() {
            return `
                <div class="settings-section" data-section="platforms">
                    <!-- 标签页切换 -->
                    <div class="platform-tabs">
                        <button class="platform-tab active" data-tab="url-parser">URL解析</button>
                        <button class="platform-tab" data-tab="platform-list">平台列表</button>
                    </div>

                    <!-- URL解析标签页 -->
                    <div class="platform-tab-content active" data-tab-content="url-parser">
                        <div class="setting-group">
                            <h4>智能URL解析</h4>
                            <p class="setting-description">粘贴完整的搜索结果URL，系统将自动识别搜索参数并生成搜索模板</p>

                            <div class="url-parser-form">
                                <div class="form-row">
                                    <label>搜索结果URL:</label>
                                    <input type="text" id="urlInput" placeholder="例如: https://github.com/search?q=javascript" class="setting-input">
                                </div>

                                <div class="form-row">
                                    <button id="parseUrlBtn" class="setting-btn primary">解析URL</button>
                                </div>
                            </div>

                            <!-- 解析结果显示 -->
                            <div id="parseResult" class="parse-result" style="display: none;">
                                <div class="result-header">
                                    <span class="result-status"></span>
                                    <span class="result-title"></span>
                                </div>
                                <div class="result-details"></div>

                                <!-- 平台配置 -->
                                <div class="platform-config" style="display: none;">
                                    <div class="form-row">
                                        <label>平台名称:</label>
                                        <input type="text" id="platformName" class="setting-input">
                                    </div>
                                    <div class="form-row">
                                        <label>别名 (用逗号分隔):</label>
                                        <input type="text" id="platformAliases" placeholder="例如: gh, github" class="setting-input">
                                    </div>
                                    <div class="form-row">
                                        <button id="savePlatformBtn" class="setting-btn success">保存平台</button>
                                        <button id="cancelParseBtn" class="setting-btn secondary">取消</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 平台列表标签页 -->
                    <div class="platform-tab-content" data-tab-content="platform-list">
                        <div class="setting-group">
                            <h4>已添加的平台</h4>
                            <div id="platformStats" class="platform-stats"></div>

                            <div id="settingsPlatformList" class="platform-list">
                                <!-- 平台列表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        generateDataSection() {
            return `
                <div class="settings-section" data-section="data">
                    <div class="setting-group">
                        <div class="button-group">
                            <button data-action="export-data" class="setting-btn secondary">📤 导出数据</button>
                            <button data-action="import-data" class="setting-btn secondary">📥 导入数据</button>
                        </div>
                        <input type="file" id="importFile" accept=".json" style="display: none;">
                    </div>
                </div>
            `;
        }

        generateAdvancedSection() {
            return `
                <div class="settings-section" data-section="advanced">
                    <div class="setting-group">
                        <h3>链接预览高级功能</h3>
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">高级功能配置</div>
                                <div class="setting-description">配置阅读模式、文本操作等高级功能</div>
                            </div>
                            <button class="advanced-config-btn" id="openAdvancedConfig">
                                <span class="btn-icon">⚙️</span>
                                <span class="btn-text">打开配置</span>
                            </button>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">功能状态</div>
                                <div class="setting-description">查看高级功能的当前状态</div>
                            </div>
                            <div class="feature-status" id="advancedFeatureStatus">
                                <div class="status-item">
                                    <span class="status-label">阅读模式:</span>
                                    <span class="status-value" id="readingModeStatus">检查中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">文本操作:</span>
                                    <span class="status-value" id="textOperationsStatus">检查中...</span>
                                </div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">快速操作</div>
                                <div class="setting-description">常用的高级功能操作</div>
                            </div>
                            <div class="quick-actions">
                                <button class="quick-action-btn" id="testLinkPreview">
                                    <span class="btn-icon">🔗</span>
                                    <span class="btn-text">测试链接预览</span>
                                </button>
                                <button class="quick-action-btn" id="resetAdvancedConfig">
                                    <span class="btn-icon">🔄</span>
                                    <span class="btn-text">重置配置</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        generateAboutSection() {
            return `
                <div class="settings-section" data-section="about">
                    <div class="setting-group">
                        <div class="about-content">
                            <div class="about-title">Moment Search V3.0.0</div>
                            <div class="about-description">简洁美观的新标签页扩展</div>
                            <a href="https://github.com/moment-search" target="_blank" class="about-link">
                                GitHub 项目主页
                            </a>
                        </div>
                    </div>
                </div>
            `;
        }

        // HTML生成辅助方法
        generateSettingItem(label, control) {
            return `
                <div class="setting-item">
                    <span class="setting-label">${label}</span>
                    <div class="setting-control">${control}</div>
                </div>
            `;
        }

        generateSwitch(togglePath, isActive) {
            return `<div class="switch ${isActive ? 'active' : ''}" data-toggle="${togglePath}"></div>`;
        }

        generateBasicThemeSelect() {
            const options = [
                { value: 'light', label: '浅色' },
                { value: 'dark', label: '深色' },
                { value: 'auto', label: '自动' }
            ];
            return this.generateSelect('appearance.theme', options, this.settings.appearance.theme);
        }

        generateSidebarPositionSelect() {
            const options = [
                { value: 'left', label: '左侧' },
                { value: 'right', label: '右侧' }
            ];
            return this.generateSelect('appearance.sidebarPosition', options, this.settings.appearance.sidebarPosition || 'left');
        }

        // 搜索设置相关生成方法
        generateCustomShortcutInput() {
            const currentShortcut = this.settings.shortcuts?.modeSwitch || 'Ctrl+Q';
            return `
                <div class="custom-shortcut-input">
                    <div class="shortcut-display" id="shortcutDisplay">
                        <span class="shortcut-keys">${currentShortcut}</span>
                        <button type="button" class="shortcut-edit-btn" id="shortcutEditBtn">修改</button>
                    </div>
                    <div class="shortcut-input-container" id="shortcutInputContainer" style="display: none;">
                        <input type="text"
                               class="shortcut-input"
                               id="shortcutInput"
                               placeholder="按下你想要的快捷键组合..."
                               readonly>
                        <div class="shortcut-actions">
                            <button type="button" class="shortcut-save-btn" id="shortcutSaveBtn" disabled>保存</button>
                            <button type="button" class="shortcut-cancel-btn" id="shortcutCancelBtn">取消</button>
                        </div>
                        <div class="shortcut-help">
                            <p>支持的修饰键：Ctrl, Alt, Shift</p>
                            <p>支持的主键：字母、数字、功能键等</p>
                        </div>
                        <div class="shortcut-error" id="shortcutError" style="display: none;"></div>
                    </div>
                </div>
            `;
        }

        // 平台切换快捷键输入组件
        generatePlatformSwitchShortcutInput() {
            const currentShortcut = this.settings.shortcuts?.platformSwitch || 'Tab';
            return `
                <div class="custom-shortcut-input">
                    <div class="shortcut-display" id="platformSwitchShortcutDisplay">
                        <span class="shortcut-keys">${currentShortcut}</span>
                        <button type="button" class="shortcut-edit-btn" id="platformSwitchShortcutEditBtn">修改</button>
                    </div>
                    <div class="shortcut-input-container" id="platformSwitchShortcutInputContainer" style="display: none;">
                        <input type="text"
                               class="shortcut-input"
                               id="platformSwitchShortcutInput"
                               placeholder="按下你想要的快捷键组合..."
                               readonly>
                        <div class="shortcut-actions">
                            <button type="button" class="shortcut-save-btn" id="platformSwitchShortcutSaveBtn" disabled>保存</button>
                            <button type="button" class="shortcut-cancel-btn" id="platformSwitchShortcutCancelBtn">取消</button>
                        </div>
                        <div class="shortcut-help">
                            <p>支持的修饰键：Ctrl, Alt, Shift</p>
                            <p>支持的主键：字母、数字、功能键等</p>
                            <p>注意：仅在搜索框获得焦点时生效</p>
                        </div>
                        <div class="shortcut-error" id="platformSwitchShortcutError" style="display: none;"></div>
                    </div>
                </div>
            `;
        }

        generateTimeFormatSelect() {
            const options = [
                { value: '24h', label: '24小时制' },
                { value: '12h', label: '12小时制' }
            ];
            return this.generateSelect('appearance.timeFormat', options, this.settings.appearance.timeFormat);
        }

        generateSelect(settingPath, options, currentValue) {
            return `
                <select class="setting-select" data-setting="${settingPath}">
                    ${options.map(option =>
                        `<option value="${option.value}" ${currentValue === option.value ? 'selected' : ''}>${option.label}</option>`
                    ).join('')}
                </select>
            `;
        }

        // 通用滑动条生成方法
        generateSlider(settingPath, min, max, defaultValue, unit = '%', id = null) {
            const keys = settingPath.split('.');
            let current = this.settings;
            for (const key of keys) {
                current = current?.[key];
            }
            const currentValue = current || defaultValue;
            let actualValue = currentValue;

            // 特殊处理：确保值在有效范围内
            if (settingPath === 'time.fontSize') {
                actualValue = Math.max(30, currentValue);
            }

            // 生成显示值
            let displayValue = `${actualValue}${unit}`;

            return `
                <div class="slider-container">
                    <input type="range" class="setting-slider"
                           data-setting="${settingPath}"
                           min="${min}" max="${max}" value="${actualValue}"
                           ${id ? `id="${id}"` : ''}>
                    <span class="slider-value">${displayValue}</span>
                </div>
            `;
        }

        generateSizeSlider() {
            return this.generateSlider('time.fontSize', 30, 100, 50, '%', 'timeFontSize');
        }

        // 通用颜色选择器生成方法
        generateColorPickerComponent(settingPath, defaultColor = '#ffffff') {
            const keys = settingPath.split('.');
            let current = this.settings;
            for (const key of keys) {
                current = current?.[key];
            }
            const currentColor = current || defaultColor;

            const presetColors = [
                '#ffffff', '#000000', '#ff0000', '#00ff00', '#0000ff',
                '#ffff00', '#ff00ff', '#00ffff', '#ffa500'
            ];

            return `
                <div class="color-picker-container">
                    <div class="preset-colors">
                        ${presetColors.map(color => `
                            <div class="color-preset ${currentColor === color ? 'active' : ''}"
                                 data-color="${color}"
                                 data-setting="${settingPath}"
                                 style="background-color: ${color}"></div>
                        `).join('')}
                        <div class="custom-color-picker">
                            <input type="color" class="custom-color-input"
                                   data-setting="${settingPath}"
                                   value="${currentColor}"
                                   title="自定义颜色">
                            <div class="custom-color-icon"></div>
                        </div>
                    </div>
                </div>
            `;
        }

        generateColorPicker() {
            return this.generateColorPickerComponent('time.color');
        }

        generateIconSizeSlider() {
            return this.generateSlider('shortcuts.iconSize', 50, 100, 60, 'px', 'iconSize');
        }

        generateIconSpacingSlider() {
            return this.generateSlider('shortcuts.iconSpacing', 8, 60, 16, 'px', 'iconSpacing');
        }

        generateContainerWidthSlider() {
            return this.generateSlider('shortcuts.containerWidth', 60, 85, 80, '%', 'containerWidth');
        }

        generateTextColorPicker() {
            return this.generateColorPickerComponent('shortcuts.textColor');
        }

        generateFontSelector() {
            const currentFont = this.settings.time?.fontFamily || 'arial';
            const fontMap = this.getFontFamilyMap();

            const fontOptions = [
                {
                    value: 'arial',
                    name: '系统默认',
                    family: fontMap['arial']
                },
                {
                    value: 'monospace',
                    name: '等宽字体',
                    family: fontMap['monospace']
                },
                {
                    value: 'yahei',
                    name: '微软雅黑',
                    family: fontMap['yahei']
                },
                {
                    value: 'times',
                    name: 'Times',
                    family: fontMap['times']
                }
            ];

            return `
                <div class="font-selector-container">
                    <div class="font-grid">
                        ${fontOptions.map(font => `
                            <div class="font-option ${currentFont === font.value ? 'selected' : ''}"
                                 data-font="${font.value}"
                                 data-setting="time.fontFamily"
                                 style="font-family: ${font.family}">
                                <div class="font-name">${font.name}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        generateFontWeightSelector() {
            const currentWeight = this.settings.time?.fontWeight || 400;

            const weightOptions = [
                {
                    value: 400,
                    name: '正常'
                },
                {
                    value: 600,
                    name: '粗体'
                }
            ];

            return `
                <div class="font-weight-selector-container">
                    <div class="font-weight-grid">
                        ${weightOptions.map(weight => `
                            <div class="font-weight-option ${currentWeight === weight.value ? 'selected' : ''}"
                                 data-weight="${weight.value}"
                                 data-setting="time.fontWeight"
                                 style="font-weight: ${weight.value}">
                                <div class="weight-name">${weight.name}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        updateSetting(path, value) {
            const keys = path.split('.');
            let current = this.settings;

            for (let i = 0; i < keys.length - 1; i++) {
                current = current[keys[i]];
            }

            current[keys[keys.length - 1]] = value;
            this.saveSettings();
            this.applySettings();

            console.log('设置已更新:', path, '=', value);
        }

        toggleSetting(path) {
            const keys = path.split('.');
            let current = this.settings;

            for (let i = 0; i < keys.length - 1; i++) {
                current = current[keys[i]];
            }

            const currentValue = current[keys[keys.length - 1]];
            const newValue = !currentValue;

            current[keys[keys.length - 1]] = newValue;
            this.saveSettings();
            this.applySettings();

            // 只更新开关的视觉状态，不重新渲染整个页面
            this.updateSwitchVisualState(path, newValue);

            console.log('设置已切换:', path, '=', newValue);
        }

        updateSwitchVisualState(path, value) {
            // 找到对应的开关并更新其视觉状态
            const switches = this.content.querySelectorAll('.switch');
            const targetSwitch = Array.from(switches).find(sw => sw.dataset.toggle === path);
            if (targetSwitch) {
                if (value) {
                    targetSwitch.classList.add('active');
                } else {
                    targetSwitch.classList.remove('active');
                }
            }
        }

        bindSettingsEvents() {
            if (!this.content) return;

            // 移除旧的事件监听器（如果存在）
            if (this.clickHandler) {
                this.content.removeEventListener('click', this.clickHandler);
            }
            if (this.changeHandler) {
                this.content.removeEventListener('change', this.changeHandler);
            }
            if (this.inputHandler) {
                this.content.removeEventListener('input', this.inputHandler);
            }

            // 创建新的事件处理器
            this.clickHandler = (e) => {
                const target = e.target;

                // 处理导航分类切换
                if (target.classList.contains('settings-nav-item') || target.closest('.settings-nav-item')) {
                    const navItem = target.classList.contains('settings-nav-item') ? target : target.closest('.settings-nav-item');
                    const section = navItem.dataset.section;
                    if (section) {
                        this.switchSection(section);
                    }
                    return;
                }

                // 处理开关切换
                if (target.classList.contains('switch')) {
                    const togglePath = target.dataset.toggle;
                    if (togglePath) {
                        this.toggleSetting(togglePath);
                    }
                }

                // 处理按钮点击
                if (target.dataset.action) {
                    this.handleAction(target.dataset.action);
                }

                // 处理高级功能按钮
                if (target.id === 'openAdvancedConfig' || target.closest('#openAdvancedConfig')) {
                    this.openAdvancedConfig();
                }
                if (target.id === 'testLinkPreview' || target.closest('#testLinkPreview')) {
                    this.testLinkPreview();
                }
                if (target.id === 'resetAdvancedConfig' || target.closest('#resetAdvancedConfig')) {
                    this.resetAdvancedConfig();
                }

                // 处理平台管理标签页切换
                if (target.classList.contains('platform-tab')) {
                    const tabName = target.dataset.tab;
                    if (tabName) {
                        // 切换标签页
                        document.querySelectorAll('.platform-tab').forEach(tab => tab.classList.remove('active'));
                        document.querySelectorAll('.platform-tab-content').forEach(content => content.classList.remove('active'));

                        target.classList.add('active');
                        const content = document.querySelector(`[data-tab-content="${tabName}"]`);
                        if (content) {
                            content.classList.add('active');

                            // 如果切换到平台列表标签页，刷新列表
                            if (tabName === 'platform-list') {
                                setTimeout(() => this.refreshPlatformList(), 100);
                            }
                        }
                    }
                }



                // 处理平台管理按钮
                if (target.id === 'parseUrlBtn') {
                    this.handlePlatformAction('parse-url');
                }

                // 处理颜色预设点击
                if (target.classList.contains('color-preset')) {
                    const color = target.dataset.color;
                    const settingPath = target.dataset.setting || 'time.color'; // 支持不同的设置路径
                    this.updateSetting(settingPath, color);
                    // 更新预设颜色的选中状态
                    target.parentElement.querySelectorAll('.color-preset').forEach(el => el.classList.remove('active'));
                    target.classList.add('active');
                    // 同步自定义颜色输入框
                    const colorInput = target.parentElement.parentElement.querySelector('.custom-color-input');
                    if (colorInput) colorInput.value = color;
                }

                // 处理字体选择器点击
                if (target.classList.contains('font-option') || target.closest('.font-option')) {
                    const fontOption = target.classList.contains('font-option') ? target : target.closest('.font-option');
                    const fontValue = fontOption.dataset.font;
                    const settingPath = fontOption.dataset.setting;
                    if (fontValue && settingPath) {
                        this.updateSetting(settingPath, fontValue);
                        // 更新字体选择器的选中状态
                        fontOption.parentElement.querySelectorAll('.font-option').forEach(el => el.classList.remove('selected'));
                        fontOption.classList.add('selected');
                    }
                }

                // 处理字体粗细选择器点击
                if (target.classList.contains('font-weight-option') || target.closest('.font-weight-option')) {
                    const weightOption = target.classList.contains('font-weight-option') ? target : target.closest('.font-weight-option');
                    const weightValue = parseInt(weightOption.dataset.weight);
                    const settingPath = weightOption.dataset.setting;
                    if (weightValue && settingPath) {
                        this.updateSetting(settingPath, weightValue);
                        // 更新字体粗细选择器的选中状态
                        weightOption.parentElement.querySelectorAll('.font-weight-option').forEach(el => el.classList.remove('selected'));
                        weightOption.classList.add('selected');
                    }
                }
            };

            // 处理选择框变化和滑动条拖动结束
            this.changeHandler = (e) => {
                const target = e.target;
                if (target.classList.contains('setting-select')) {
                    const settingPath = target.dataset.setting;
                    if (settingPath) {
                        this.updateSetting(settingPath, target.value);
                    }
                }



                // 处理自定义颜色输入
                if (target.classList.contains('custom-color-input')) {
                    const settingPath = target.dataset.setting;
                    if (settingPath) {
                        this.updateSetting(settingPath, target.value);
                        // 清除预设颜色的选中状态
                        const presetColors = target.parentElement.querySelector('.preset-colors');
                        if (presetColors) {
                            presetColors.querySelectorAll('.color-preset').forEach(el => el.classList.remove('active'));
                        }
                    }
                }
            };

            // 处理滑动条实时变化（拖动过程中）
            this.inputHandler = (e) => {
                const target = e.target;
                if (target.classList.contains('setting-slider')) {
                    const settingPath = target.dataset.setting;
                    if (settingPath) {
                        let value = parseInt(target.value);



                        this.updateSetting(settingPath, value);
                        // 更新显示的数值
                        const valueDisplay = target.parentElement.querySelector('.slider-value');
                        if (valueDisplay) {
                            const unit = settingPath.includes('iconSize') || settingPath.includes('iconSpacing') ? 'px' : '%';
                            const displayValue = `${value}${unit}`;
                            valueDisplay.textContent = displayValue;
                        }
                    }
                }
            };

            // 绑定事件监听器
            this.content.addEventListener('click', this.clickHandler);
            this.content.addEventListener('change', this.changeHandler);
            this.content.addEventListener('input', this.inputHandler);

            // 绑定自定义快捷键输入事件
            this.bindShortcutInputEvents('shortcut', 'modeSwitch', this.saveShortcut.bind(this), this.cancelShortcutEdit.bind(this));
            this.bindShortcutInputEvents('platformSwitchShortcut', 'platformSwitch', this.savePlatformSwitchShortcut.bind(this), this.cancelPlatformSwitchShortcutEdit.bind(this));

            // 处理文件输入
            const importFile = document.getElementById('importFile');
            if (importFile) {
                importFile.addEventListener('change', (e) => {
                    if (e.target.files[0]) {
                        this.importData(e.target.files[0]);
                    }
                });
            }
        }

        // 通用快捷键输入事件绑定
        bindShortcutInputEvents(idPrefix, settingKey, saveCallback, cancelCallback) {
            // 延迟绑定，确保DOM已渲染
            setTimeout(() => {
                const editBtn = document.getElementById(`${idPrefix}EditBtn`);
                const saveBtn = document.getElementById(`${idPrefix}SaveBtn`);
                const cancelBtn = document.getElementById(`${idPrefix}CancelBtn`);
                const input = document.getElementById(`${idPrefix}Input`);
                const display = document.getElementById(`${idPrefix}Display`);
                const container = document.getElementById(`${idPrefix}InputContainer`);

                if (!editBtn || !saveBtn || !cancelBtn || !input) return;

                let currentShortcut = '';
                let isRecording = false;

                // 编辑按钮点击
                editBtn.addEventListener('click', () => {
                    display.style.display = 'none';
                    container.style.display = 'block';
                    input.focus();
                    input.value = '';
                    input.placeholder = '按下你想要的快捷键组合...';
                    input.classList.add('recording');
                    isRecording = true;
                    saveBtn.disabled = true;
                    this.hideShortcutError(idPrefix);
                });

                // 取消按钮点击
                cancelBtn.addEventListener('click', () => {
                    cancelCallback();
                });

                // 保存按钮点击
                saveBtn.addEventListener('click', () => {
                    if (currentShortcut) {
                        saveCallback(currentShortcut);
                        cancelCallback();
                    }
                });

                // 快捷键录制
                input.addEventListener('keydown', (e) => {
                    if (!isRecording) return;

                    e.preventDefault();
                    e.stopPropagation();

                    const shortcut = this.captureShortcut(e, settingKey);
                    if (shortcut) {
                        currentShortcut = shortcut;
                        input.value = shortcut;
                        input.classList.remove('recording');

                        // 验证快捷键
                        const validation = this.validateShortcut(shortcut, settingKey);
                        if (validation.valid) {
                            saveBtn.disabled = false;
                            this.hideShortcutError(idPrefix);
                        } else {
                            saveBtn.disabled = true;
                            this.showShortcutError(validation.error, idPrefix);
                        }

                        isRecording = false;
                    }
                });

                // 防止输入框失去焦点时的默认行为
                input.addEventListener('blur', () => {
                    if (isRecording) {
                        setTimeout(() => input.focus(), 10);
                    }
                });
            }, 100);
        }



        captureShortcut(event, settingKey = 'modeSwitch') {
            const modifiers = [];
            const key = event.key;

            // 收集修饰键
            if (event.ctrlKey) modifiers.push('Ctrl');
            if (event.altKey) modifiers.push('Alt');
            if (event.shiftKey) modifiers.push('Shift');

            // 忽略单独的修饰键
            if (['Control', 'Alt', 'Shift', 'Meta'].includes(key)) {
                return null;
            }

            const keyName = this.normalizeKeyName(key);

            // 根据设置类型决定是否允许无修饰键
            if (settingKey === 'platformSwitch') {
                // 平台切换快捷键：允许无修饰键的单键（如Tab）
                if (modifiers.length === 0) {
                    const allowedSingleKeys = ['TAB', 'SPACE', 'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'];
                    if (!allowedSingleKeys.includes(keyName)) {
                        return null;
                    }
                    return keyName;
                }
                return modifiers.join('+') + '+' + keyName;
            } else {
                // 模式切换快捷键：必须有修饰键
                if (modifiers.length === 0) {
                    return null;
                }
                return modifiers.join('+') + '+' + keyName;
            }
        }

        normalizeKeyName(key) {
            // 标准化按键名称
            const keyMap = {
                ' ': 'Space',
                'ArrowUp': 'Up',
                'ArrowDown': 'Down',
                'ArrowLeft': 'Left',
                'ArrowRight': 'Right',
                'Escape': 'Esc'
            };

            return keyMap[key] || key.toUpperCase();
        }

        validateShortcut(shortcut, settingKey = 'modeSwitch') {
            // 系统保留的快捷键
            const reservedShortcuts = [
                'Ctrl+C', 'Ctrl+V', 'Ctrl+X', 'Ctrl+Z', 'Ctrl+Y',
                'Ctrl+A', 'Ctrl+S', 'Ctrl+O', 'Ctrl+N', 'Ctrl+P',
                'Ctrl+F', 'Ctrl+H', 'Ctrl+R', 'Ctrl+T', 'Ctrl+W',
                'Alt+F4', 'Ctrl+Alt+Delete', 'Ctrl+Shift+Esc'
            ];

            if (reservedShortcuts.includes(shortcut)) {
                return {
                    valid: false,
                    error: '此快捷键为系统保留，请选择其他组合'
                };
            }

            // 检查是否与现有快捷键冲突
            const existingShortcuts = {
                '模式切换': this.settings.shortcuts?.modeSwitch || 'Ctrl+Q',
                '平台切换': this.settings.shortcuts?.platformSwitch || 'Tab'
            };

            for (const [name, existing] of Object.entries(existingShortcuts)) {
                if (existing === shortcut &&
                    !((settingKey === 'modeSwitch' && name === '模式切换') ||
                      (settingKey === 'platformSwitch' && name === '平台切换'))) {
                    return {
                        valid: false,
                        error: `与${name}快捷键冲突`
                    };
                }
            }

            // 根据设置类型验证格式
            if (settingKey === 'platformSwitch') {
                // 平台切换快捷键：允许单键或组合键
                return { valid: true };
            } else {
                // 模式切换快捷键：必须有修饰键
                if (!shortcut.includes('+') || shortcut.split('+').length < 2) {
                    return {
                        valid: false,
                        error: '请使用修饰键+主键的组合'
                    };
                }
                return { valid: true };
            }
        }

        showShortcutError(message, idPrefix = 'shortcut') {
            const errorDiv = document.getElementById(`${idPrefix}Error`);
            if (errorDiv) {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }
        }

        hideShortcutError(idPrefix = 'shortcut') {
            const errorDiv = document.getElementById(`${idPrefix}Error`);
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        }

        saveShortcut(shortcut) {
            // 保存到设置
            this.updateSetting('shortcuts.modeSwitch', shortcut);

            // 更新显示
            const shortcutKeys = document.querySelector('.shortcut-keys');
            if (shortcutKeys) {
                shortcutKeys.textContent = shortcut;
            }

            // 通知SearchModeManager更新快捷键
            if (window.app?.searchModeManager) {
                window.app.searchModeManager.updateShortcutKey(shortcut);
            }

            // 通知快捷键帮助管理器更新
            if (window.app?.shortcutHelpManager) {
                window.app.shortcutHelpManager.updateShortcuts();
            }
        }

        // 平台切换快捷键特定方法

        savePlatformSwitchShortcut(shortcut) {
            // 保存到设置
            this.updateSetting('shortcuts.platformSwitch', shortcut);

            // 更新显示
            const shortcutKeys = document.querySelector('#platformSwitchShortcutDisplay .shortcut-keys');
            if (shortcutKeys) {
                shortcutKeys.textContent = shortcut;
            }

            // 通知SearchManager更新快捷键
            if (window.app?.searchManager) {
                window.app.searchManager.updatePlatformSwitchShortcut(shortcut);
            }

            // 通知快捷键帮助管理器更新
            if (window.app?.shortcutHelpManager) {
                window.app.shortcutHelpManager.updateShortcuts();
            }
        }

        cancelPlatformSwitchShortcutEdit() {
            const display = document.getElementById('platformSwitchShortcutDisplay');
            const container = document.getElementById('platformSwitchShortcutInputContainer');
            const input = document.getElementById('platformSwitchShortcutInput');

            if (display && container && input) {
                display.style.display = 'flex';
                container.style.display = 'none';
                input.classList.remove('recording');
                this.hideShortcutError('platformSwitchShortcut');
            }
        }

        cancelShortcutEdit() {
            const display = document.getElementById('shortcutDisplay');
            const container = document.getElementById('shortcutInputContainer');
            const input = document.getElementById('shortcutInput');

            if (display && container && input) {
                display.style.display = 'flex';
                container.style.display = 'none';
                input.classList.remove('recording');
                this.hideShortcutError();
            }
        }

        switchSection(sectionName) {
            // 更新当前激活的页面
            this.currentSection = sectionName;

            // 通用的状态切换方法
            this.updateActiveState('.settings-nav-item', sectionName);
            this.updateActiveState('.settings-section', sectionName);
        }

        updateActiveState(selector, activeSectionName) {
            const elements = this.content.querySelectorAll(selector);
            elements.forEach(element => {
                if (element.dataset.section === activeSectionName) {
                    element.classList.add('active');
                } else {
                    element.classList.remove('active');
                }
            });
        }



        handleAction(action) {
            switch (action) {
                case 'wallpaper':
                    if (window.app?.backgroundManager) {
                        window.app.backgroundManager.showWallpaperOptions();
                    } else {
                        console.error('backgroundManager未初始化');
                        alert('背景管理器尚未初始化，请稍后再试');
                    }
                    break;
                case 'edit-wallpaper':
                    if (window.app?.backgroundManager) {
                        window.app.backgroundManager.editCurrentWallpaper();
                    } else {
                        console.error('backgroundManager未初始化');
                        alert('背景管理器尚未初始化，请稍后再试');
                    }
                    break;
                case 'add-shortcut':
                    if (window.app.shortcutManager) {
                        window.app.shortcutManager.showAddModal();
                    }
                    break;
                case 'reset-shortcuts':
                    if (window.app.shortcutManager) {
                        window.app.shortcutManager.resetToDefault();
                    }
                    break;
                case 'export-data':
                    this.exportData();
                    break;
                case 'import-data':
                    document.getElementById('importFile').click();
                    break;
                default:
                    console.warn('未知的操作:', action);
            }
        }

        applySettings() {
            // 应用时间显示设置
            if (window.app?.timeManager) {
                window.app.timeManager.applySettings();
            }

            // 应用主题设置
            this.applyTheme();

            // 应用侧边栏设置
            this.applySidebarSettings();

            // 应用快捷方式设置
            this.applyShortcutSettings();
        }

        applyTheme() {
            const theme = this.settings.appearance.theme;
            const body = document.body;

            // 移除现有主题类
            body.classList.remove('light-theme', 'dark-theme');

            if (theme === 'auto') {
                // 根据系统主题自动切换
                const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
                const prefersDark = mediaQuery.matches;
                body.classList.add(prefersDark ? 'dark-theme' : 'light-theme');

                // 添加系统主题变化监听器
                if (!this.systemThemeListener) {
                    this.systemThemeListener = (e) => {
                        if (this.settings.appearance.theme === 'auto') {
                            body.classList.remove('light-theme', 'dark-theme');
                            body.classList.add(e.matches ? 'dark-theme' : 'light-theme');
                            console.log(`🎨 系统主题变化: ${e.matches ? '深色' : '浅色'}模式`);
                        }
                    };
                    mediaQuery.addEventListener('change', this.systemThemeListener);
                    console.log('✅ 系统主题变化监听器已添加');
                }
            } else {
                // 移除监听器（如果存在）
                if (this.systemThemeListener) {
                    window.matchMedia('(prefers-color-scheme: dark)')
                        .removeEventListener('change', this.systemThemeListener);
                    this.systemThemeListener = null;
                    console.log('🗑️ 系统主题变化监听器已移除');
                }
                body.classList.add(theme + '-theme');
            }
        }

        applySidebarSettings() {
            const body = document.body;
            const sidebarPosition = this.settings.appearance?.sidebarPosition || 'left';
            const sidebarAutoHide = this.settings.appearance?.sidebarAutoHide === true;

            // 移除现有位置类
            body.classList.remove('sidebar-left', 'sidebar-right');

            // 应用新的位置类
            body.classList.add(`sidebar-${sidebarPosition}`);

            // 应用自动隐藏设置
            if (sidebarAutoHide) {
                body.classList.add('sidebar-auto-hide');
                this.setupSidebarHoverDetection(sidebarPosition);
            } else {
                body.classList.remove('sidebar-auto-hide');
                this.removeSidebarHoverDetection();
            }

            console.log(`✅ 侧边栏设置已应用: 位置=${sidebarPosition}, 自动隐藏=${sidebarAutoHide}`);
        }

        setupSidebarHoverDetection(position) {
            // 移除现有的悬停检测区域
            this.removeSidebarHoverDetection();

            // 创建侧边栏悬停检测区域 - 扩大覆盖范围
            const hoverZone = document.createElement('div');
            hoverZone.id = 'sidebarHoverZone';
            hoverZone.style.cssText = `
                position: fixed;
                top: 0;
                ${position === 'right' ? 'right: 0;' : 'left: 0;'}
                width: 150px;
                height: 100vh;
                z-index: 98;
                pointer-events: auto;
                background: transparent;
            `;

            // 添加悬停事件 - 统一控制所有侧边栏元素
            hoverZone.addEventListener('mouseenter', () => {
                document.body.classList.add('sidebar-hover');
                console.log('🖱️ 鼠标进入侧边栏区域，显示所有侧边栏元素');
            });

            hoverZone.addEventListener('mouseleave', () => {
                document.body.classList.remove('sidebar-hover');
                console.log('🖱️ 鼠标离开侧边栏区域，隐藏所有侧边栏元素');
            });

            document.body.appendChild(hoverZone);

            // 为侧边栏元素添加额外的悬停检测
            this.addSidebarElementsHoverDetection();

            console.log(`✅ 侧边栏悬停检测区域已创建: 位置=${position}, 宽度=150px`);
        }

        addSidebarElementsHoverDetection() {
            // 获取所有侧边栏相关元素
            const sidebarElements = [
                document.querySelector('.sidebar'),
                document.querySelector('.shortcut-help-container'),
                document.querySelector('.page-sidebar')
            ].filter(el => el !== null);

            // 为每个元素添加悬停事件
            sidebarElements.forEach(element => {
                element.addEventListener('mouseenter', () => {
                    document.body.classList.add('sidebar-hover');
                });

                element.addEventListener('mouseleave', (e) => {
                    // 检查鼠标是否移动到其他侧边栏元素或悬停区域
                    setTimeout(() => {
                        const hoverZone = document.getElementById('sidebarHoverZone');
                        const isOverSidebarElement = sidebarElements.some(el =>
                            el.matches(':hover')
                        );
                        const isOverHoverZone = hoverZone && hoverZone.matches(':hover');

                        if (!isOverSidebarElement && !isOverHoverZone) {
                            document.body.classList.remove('sidebar-hover');
                        }
                    }, 10);
                });
            });
        }

        removeSidebarHoverDetection() {
            const existingZone = document.getElementById('sidebarHoverZone');
            if (existingZone) {
                existingZone.remove();
            }
            document.body.classList.remove('sidebar-hover');

            // 清理侧边栏元素的事件监听器（通过克隆元素来移除所有事件监听器）
            const sidebarElements = [
                document.querySelector('.sidebar'),
                document.querySelector('.shortcut-help-container'),
                document.querySelector('.page-sidebar')
            ].filter(el => el !== null);

            sidebarElements.forEach(element => {
                // 移除可能存在的悬停状态
                element.style.pointerEvents = '';
            });
        }



        applyShortcutSettings() {
            const grid = document.getElementById('shortcutsGrid');
            if (grid) {
                // 控制快捷方式名称显示
                if (this.settings.shortcuts.showNames) {
                    grid.classList.remove('hide-names');
                } else {
                    grid.classList.add('hide-names');
                }

                // 使用全局默认值，避免重复定义
                const defaults = window.SHORTCUT_DEFAULTS || { iconSize: 60, iconSpacing: 16, containerWidth: 80, textColor: '#ffffff' };
                const { iconSize = defaults.iconSize, iconSpacing = defaults.iconSpacing, containerWidth = defaults.containerWidth, textColor = defaults.textColor } = this.settings.shortcuts;

                // 一次性获取所有CSS变量值，提高效率
                const computedStyle = getComputedStyle(document.documentElement);
                const currentValues = {
                    iconSize: computedStyle.getPropertyValue('--icon-size').trim(),
                    iconSpacing: computedStyle.getPropertyValue('--icon-spacing').trim(),
                    containerWidth: computedStyle.getPropertyValue('--container-width').trim(),
                    textColor: computedStyle.getPropertyValue('--text-color').trim()
                };

                // 检查是否需要更新
                const expectedValues = {
                    iconSize: `${iconSize}px`,
                    iconSpacing: `${iconSpacing}px`,
                    containerWidth: `${containerWidth}%`,
                    textColor: textColor
                };

                const needsUpdate = Object.keys(expectedValues).some(key =>
                    currentValues[key] !== expectedValues[key]
                );

                if (needsUpdate) {
                    // 应用动态样式
                    const cssPropertyMap = {
                        iconSize: '--icon-size',
                        iconSpacing: '--icon-spacing',
                        containerWidth: '--container-width',
                        textColor: '--text-color'
                    };

                    Object.entries(expectedValues).forEach(([key, value]) => {
                        document.documentElement.style.setProperty(cssPropertyMap[key], value);
                    });

                    // 使用共享的计算函数
                    const columns = window.calculateGridColumns ?
                        window.calculateGridColumns(containerWidth, iconSize, iconSpacing) :
                        Math.max(1, Math.floor((window.innerWidth * (containerWidth / 100)) / (iconSize + iconSpacing)));

                    document.documentElement.style.setProperty('--grid-columns', columns);

                    console.log('快捷方式设置已更新');
                } else {
                    console.log('快捷方式设置无需更新，已是最新值');
                }
            }
        }

        exportData() {
            try {
                const dataStorageManager = this.getDataStorageManager();
                const data = dataStorageManager.getCompleteData();
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `moment-search-data-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                console.log('✅ 数据导出成功');
                this.showMessage('数据导出成功！', 'success');
            } catch (error) {
                console.error('❌ 数据导出失败:', error);
                this.showMessage('导出失败：' + error.message, 'error');
            }
        }

        async importData(file) {
            if (!file) return;

            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    const data = JSON.parse(e.target.result);

                    if (confirm('确定要导入数据吗？这将覆盖现有的所有设置和数据。系统会自动创建备份以防导入失败。')) {
                        const dataStorageManager = this.getDataStorageManager();

                        // 显示导入进度
                        this.showMessage('正在导入数据，请稍候...', 'info');

                        const importSuccess = await dataStorageManager.importData(data);

                        if (importSuccess) {
                            this.showMessage('导入成功！页面将在3秒后刷新以应用所有更改。', 'success');

                            // 延迟刷新页面以确保所有更改生效
                            setTimeout(() => {
                                location.reload();
                            }, 3000);
                        } else {
                            this.showMessage('导入失败，已自动恢复到导入前的状态', 'error');
                        }
                    }
                } catch (error) {
                    console.error('❌ 导入失败:', error);
                    this.showMessage('文件格式错误，导入失败：' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        }

        // 获取PlatformManager实例（避免重复代码）
        getPlatformManager() {
            const platformManager = window.app?.searchManager?.platformManager;
            if (!platformManager) {
                throw new Error('PlatformManager 未初始化');
            }
            return platformManager;
        }

        // 获取DataStorageManager实例（避免重复代码）
        getDataStorageManager() {
            const dataStorageManager = window.app?.dataStorageManager;
            if (!dataStorageManager) {
                throw new Error('数据存储管理器未初始化');
            }
            return dataStorageManager;
        }

        // 统一的错误处理方法
        handleError(error, operation = '操作') {
            console.error(`${operation}失败:`, error);
            const message = error.message || `${operation}失败，请重试`;
            // 注意：这里保留alert，因为SettingsManager的错误通常是系统级错误，需要用户立即知晓
            alert(message);
        }

        // 获取常用DOM元素（避免重复查询）
        getParseElements() {
            return {
                urlInput: document.getElementById('urlInput'),
                parseResult: document.getElementById('parseResult')
            };
        }

        // 平台管理相关方法
        async handlePlatformAction(action, data = {}) {
            try {
                switch (action) {
                    case 'parse-url':
                        await this.parseURL();
                        break;
                    case 'save-platform':
                        await this.savePlatform();
                        break;

                    case 'delete-platform':
                        await this.deletePlatform(data.id, data.source);
                        break;

                    default:
                        console.warn('未知的平台操作:', action);
                }
            } catch (error) {
                this.handleError(error, '平台操作');
            }
        }

        async parseURL() {
            const urlInput = document.getElementById('urlInput');
            const parseResult = document.getElementById('parseResult');

            if (!urlInput || !parseResult) return;

            const url = urlInput.value.trim();

            if (!url) {
                alert('请输入URL');
                return;
            }

            // 显示解析中状态
            parseResult.style.display = 'block';
            parseResult.innerHTML = `
                <div class="result-header">
                    <span class="result-status" style="background: #ff9800;"></span>
                    <span class="result-title">正在解析...</span>
                </div>
            `;

            try {
                const platformManager = this.getPlatformManager();
                const result = await platformManager.urlParser.parseURL(url);

                if (result.success) {
                    this.showParseSuccess(result);
                } else {
                    this.showParseError(result);
                }
            } catch (error) {
                this.showParseError({ error: error.message });
            }
        }

        showParseSuccess(result) {
            const parseResult = document.getElementById('parseResult');
            parseResult.innerHTML = `
                <div class="result-header">
                    <span class="result-status success"></span>
                    <span class="result-title">解析成功</span>
                </div>
                <div class="result-details">
                    <strong>网站名称:</strong> ${result.name}<br>
                    <strong>域名:</strong> ${result.domain}<br>
                    <strong>搜索模板:</strong> ${result.template}<br>
                    <strong>解析方法:</strong> ${result.method === 'parameter' ? 'URL参数' : 'URL路径'} ${result.searchParam ? `(${result.searchParam})` : ''}<br>
                    <strong>置信度:</strong> ${Math.round(result.confidence * 100)}%
                </div>
                <div class="platform-config">
                    <div class="form-row">
                        <label>平台名称:</label>
                        <input type="text" id="parsePlatformName" class="setting-input" value="${result.name}">
                    </div>
                    <div class="form-row">
                        <label>别名 (用逗号分隔):</label>
                        <input type="text" id="parsePlatformAliases" placeholder="例如: gh, github" class="setting-input">
                    </div>
                    <div class="form-row">
                        <button id="savePlatformBtn" class="setting-btn success">保存平台</button>
                        <button id="cancelParseBtn" class="setting-btn secondary">取消</button>
                    </div>
                </div>
            `;

            // 绑定按钮事件
            document.getElementById('savePlatformBtn')?.addEventListener('click', () => this.handlePlatformAction('save-platform'));
            document.getElementById('cancelParseBtn')?.addEventListener('click', () => this.cancelParse());
        }

        showParseError(result) {
            const parseResult = document.getElementById('parseResult');
            parseResult.innerHTML = `
                <div class="result-header">
                    <span class="result-status error"></span>
                    <span class="result-title">解析失败</span>
                </div>
                <div class="result-details">
                    <strong>错误:</strong> ${result.error}<br>
                    ${result.suggestion ? `<strong>建议:</strong> ${result.suggestion}` : ''}
                </div>
            `;
        }

        async savePlatform() {
            const urlInput = document.getElementById('urlInput');
            const platformName = document.getElementById('parsePlatformName');
            const platformAliases = document.getElementById('parsePlatformAliases');

            if (!urlInput || !platformName) return;

            const url = urlInput.value.trim();
            const name = platformName.value.trim();
            const aliases = platformAliases ? platformAliases.value.trim() : '';

            if (!name) {
                alert('请输入平台名称');
                return;
            }

            try {
                const platformManager = this.getPlatformManager();
                const platformData = await platformManager.addPlatformFromURL(url, {
                    name: name,
                    aliases: aliases
                });

                // 同步到当前页面的快捷方式（修复主页显示问题）
                await this.syncPlatformToCurrentPage(platformData);

                alert('平台添加成功！');
                this.cancelParse();
                this.refreshPlatformList();
            } catch (error) {
                this.handleError(error, '保存平台');
            }
        }

        // 将新添加的平台同步到搜索直达页面（修复版：同步到正确的页面）
        async syncPlatformToCurrentPage(platformData) {
            try {
                const pageManager = window.app?.pageManager;
                if (!pageManager) {
                    console.warn('PageManager不存在，无法同步平台');
                    return;
                }

                // 查找搜索直达模式的页面（mode: 'quick'）
                const quickSearchPage = pageManager.pages.find(page => page.mode === 'quick');
                if (!quickSearchPage) {
                    console.warn('未找到搜索直达页面，无法同步平台');
                    return;
                }

                // 检查平台是否已存在（避免重复添加）
                const existingShortcut = quickSearchPage.shortcuts.find(s =>
                    s.name === platformData.name || s.url === platformData.url
                );

                if (existingShortcut) {
                    console.log(`平台 ${platformData.name} 已存在于搜索直达页面，跳过同步`);
                    return;
                }

                // 创建新的快捷方式
                const newShortcut = {
                    id: Date.now() + Math.random(),
                    name: platformData.name,
                    icon: platformData.icon || '🔍',
                    url: platformData.url,
                    searchUrl: platformData.searchUrl || platformData.url,
                    searchMode: 'search',
                    isFromPlatformManager: true
                };

                // 添加到搜索直达页面
                quickSearchPage.shortcuts.push(newShortcut);

                // 保存页面数据
                await pageManager.savePages();

                // 如果当前显示的是搜索直达页面，刷新显示
                if (pageManager.currentPageIndex === pageManager.pages.indexOf(quickSearchPage)) {
                    pageManager.renderCurrentPage();
                }

                // 复用现有的图标获取逻辑（避免重复代码）
                this.fetchRealIconForSearchDirect(newShortcut, quickSearchPage, pageManager);

                console.log(`平台 ${platformData.name} 已同步到搜索直达页面`);
            } catch (error) {
                console.error('同步平台到搜索直达页面失败:', error);
            }
        }

        // 为搜索直达页面的新快捷方式异步获取真实图标（修复版：恢复正确的图标获取逻辑）
        async fetchRealIconForSearchDirect(shortcut, quickSearchPage, pageManager) {
            try {
                const faviconManager = window.app?.shortcutManager?.faviconManager;
                if (!faviconManager) {
                    console.warn('FaviconManager不存在，无法获取真实图标');
                    return;
                }

                console.log(`🔍 为搜索直达页面异步获取真实图标: ${shortcut.name}`);

                // 获取真实图标
                const faviconUrl = await faviconManager.getFavicon(shortcut.url);

                if (faviconUrl && faviconUrl !== faviconManager.getDefaultIcon()) {
                    // 更新快捷方式的图标URL
                    shortcut.faviconUrl = faviconUrl;

                    // 保存更新后的数据
                    await pageManager.savePages();

                    // 如果当前显示的是搜索直达页面，重新渲染
                    if (pageManager.currentPageIndex === pageManager.pages.indexOf(quickSearchPage)) {
                        pageManager.renderCurrentPage();
                    }

                    console.log(`✅ 已更新搜索直达页面的真实图标: ${shortcut.name} -> ${faviconUrl}`);
                } else {
                    console.log(`⚠️ 未能获取真实图标，使用默认图标: ${shortcut.name}`);
                }
            } catch (error) {
                console.warn(`获取搜索直达页面真实图标失败: ${shortcut.name}`, error);
            }
        }

        // 同步删除PageManager中对应的平台快捷方式（优化版：复用匹配逻辑）
        async syncDeletePlatformFromPageManager(platform) {
            try {
                const shortcutManager = window.app?.shortcutManager;
                if (!shortcutManager) {
                    console.warn('ShortcutManager不存在，无法同步删除');
                    return;
                }

                // 查找对应的快捷方式（内联匹配逻辑）
                const matchingShortcut = shortcutManager.shortcuts.find(s =>
                    s.name === platform.name ||
                    s.url === platform.url ||
                    (s.searchUrl && s.searchUrl === platform.searchUrl) ||
                    (platform.searchUrl && platform.searchUrl === s.url)
                );

                if (matchingShortcut) {
                    // 删除对应的快捷方式（如果是反向同步，跳过平台同步避免循环）
                    const success = shortcutManager.removeShortcut(matchingShortcut.id, { skipPlatformSync: true });
                    if (success) {
                        console.log(`✅ 已反向同步删除主页快捷方式: ${platform.name}`);
                    }
                } else {
                    console.log(`未找到对应的主页快捷方式: ${platform.name}`);
                }
            } catch (error) {
                console.error('同步删除主页快捷方式失败:', error);
            }
        }

        cancelParse() {
            const { urlInput, parseResult } = this.getParseElements();

            if (parseResult) parseResult.style.display = 'none';
            if (urlInput) urlInput.value = '';
        }

        async deletePlatform(id, source = 'platformManager') {
            if (!id) return;

            if (!confirm('确定要删除这个平台吗？')) return;

            try {
                if (source === 'pageManager') {
                    // 从页面管理器中删除搜索直达快捷方式
                    await this.deleteShortcutFromPageManager(id);
                } else {
                    // 从平台管理器中删除
                    const platformManager = this.getPlatformManager();
                    const platform = platformManager.platforms.get(id);

                    if (platform) {
                        // 双向同步：同时从PageManager中删除对应的快捷方式
                        await this.syncDeletePlatformFromPageManager(platform);

                        // 从PlatformManager中删除
                        await platformManager.deletePlatform(id);
                    }
                }
                alert('平台删除成功！');
                this.refreshPlatformList();
            } catch (error) {
                this.handleError(error, '删除平台');
            }
        }

        // 从页面管理器中删除搜索直达快捷方式
        async deleteShortcutFromPageManager(shortcutId) {
            if (window.app && window.app.pageManager && window.app.pageManager.pages) {
                for (const page of window.app.pageManager.pages) {
                    if (page.shortcuts) {
                        const shortcutIndex = page.shortcuts.findIndex(s => s.id === shortcutId);
                        if (shortcutIndex !== -1) {
                            page.shortcuts.splice(shortcutIndex, 1);
                            // 保存页面数据
                            if (window.app.pageManager.savePages) {
                                await window.app.pageManager.savePages();
                            }
                            // 重新渲染当前页面（这会同步数据并重新渲染）
                            if (window.app.pageManager.renderCurrentPage) {
                                await window.app.pageManager.renderCurrentPage();
                            }
                            return;
                        }
                    }
                }
            }
            throw new Error('未找到要删除的快捷方式');
        }

        refreshPlatformList() {
            const elements = this.getPlatformListElements();
            if (!elements) return;

            try {
                const platforms = this.getAllPlatformsData();
                this.updatePlatformStats(elements.platformStats, platforms.length);
                this.renderPlatformList(elements.platformList, platforms);

            } catch (error) {
                console.error('刷新平台列表失败:', error);
                this.renderErrorState(elements.platformList);
            }
        }

        // 获取平台列表相关DOM元素
        getPlatformListElements() {
            const platformList = document.getElementById('settingsPlatformList');
            const platformStats = document.getElementById('platformStats');

            if (!platformList || !platformStats) {
                console.warn('平台列表DOM元素未找到');
                return null;
            }

            return { platformList, platformStats };
        }

        // 获取所有平台数据
        getAllPlatformsData() {
            const platformManager = this.getPlatformManager();
            const platformManagerPlatforms = platformManager.getAllPlatforms();
            const pageManagerPlatforms = this.getSearchDirectShortcutsFromPageManager();

            return this.mergePlatformData(platformManagerPlatforms, pageManagerPlatforms);
        }

        // 更新统计信息
        updatePlatformStats(statsElement, count) {
            statsElement.textContent = `总计: ${count} 个平台`;
        }

        // 渲染平台列表
        renderPlatformList(listElement, platforms) {
            if (platforms.length === 0) {
                listElement.innerHTML = '<div class="platform-list-empty">没有找到平台</div>';
                return;
            }

            listElement.innerHTML = platforms.map(platform => this.createPlatformItemHTML(platform)).join('');
        }

        // 创建平台项HTML
        createPlatformItemHTML(platform) {
            const isBuiltin = platform.isBuiltin;
            const actionButton = isBuiltin
                ? '<span class="platform-builtin-label">内置平台</span>'
                : `<button class="platform-action-btn delete" onclick="window.app.settingsManager.handlePlatformAction('delete-platform', {id: '${platform.id}', source: '${platform.source || 'platformManager'}'})">删除</button>`;

            return `
                <div class="platform-item ${isBuiltin ? 'builtin' : ''}">
                    <div class="platform-info">
                        <div class="settings-platform-name">${platform.name}</div>
                        <div class="platform-details">
                            搜索直达: ${platform.searchUrl || platform.url || '内置搜索'}<br>
                            别名: ${platform.aliases ? platform.aliases.join(', ') : '无'}
                        </div>
                    </div>
                    <div class="platform-actions">
                        ${actionButton}
                    </div>
                </div>
            `;
        }

        // 渲染错误状态
        renderErrorState(listElement) {
            listElement.innerHTML = '<div class="platform-list-error">加载平台列表失败</div>';
        }

        // 从页面管理器获取搜索直达快捷方式
        getSearchDirectShortcutsFromPageManager() {
            const platforms = [];

            if (window.app && window.app.pageManager && window.app.pageManager.pages) {
                window.app.pageManager.pages.forEach(page => {
                    if (page.shortcuts) {
                        page.shortcuts.forEach(shortcut => {
                            // 判断是否为搜索直达快捷方式的条件：
                            // 1. 有searchUrl字段（包含搜索模板）
                            // 2. 或者URL包含{query}占位符
                            // 3. 或者searchMode为'search'且URL看起来像搜索URL
                            const isSearchDirect = this.isSearchDirectShortcut(shortcut);

                            if (isSearchDirect) {
                                platforms.push({
                                    id: shortcut.id,
                                    name: shortcut.name,
                                    url: shortcut.url,
                                    searchUrl: shortcut.searchUrl || this.extractSearchTemplate(shortcut.url),
                                    domain: this.extractDomain(shortcut.url),
                                    icon: '🔍',
                                    aliases: [],
                                    isCustom: true,
                                    isBuiltin: false,
                                    isFromShortcut: true,
                                    source: 'pageManager',
                                    createdAt: Date.now(),
                                    updatedAt: Date.now()
                                });
                            }
                        });
                    }
                });
            }

            return platforms;
        }

        // 判断快捷方式是否为搜索直达类型
        isSearchDirectShortcut(shortcut) {
            // 1. 有searchUrl字段且包含{query}占位符
            if (shortcut.searchUrl && shortcut.searchUrl.includes('{query}')) {
                return true;
            }

            // 2. URL本身包含{query}占位符
            if (shortcut.url && shortcut.url.includes('{query}')) {
                return true;
            }

            // 3. 检查URL是否包含常见搜索参数
            if (shortcut.url) {
                const searchParams = ['q=', 'query=', 'search=', 'keyword=', 'search_query=', 'wd='];
                const hasSearchParam = searchParams.some(param => shortcut.url.includes(param));

                // 如果searchMode为'search'，直接返回是否有搜索参数
                if (shortcut.searchMode === 'search') {
                    return hasSearchParam;
                }

                // 如果URL包含搜索参数，进一步检查是否为已知搜索域名
                if (hasSearchParam) {
                    const searchDomains = [
                        'github.com/search',
                        'stackoverflow.com/search',
                        'youtube.com/results',
                        'x.com/search',
                        'twitter.com/search',
                        'reddit.com/search',
                        'amazon.com/s',
                        'ebay.com/sch'
                    ];

                    return searchDomains.some(domain => shortcut.url.includes(domain));
                }
            }

            return false;
        }

        // 提取搜索模板
        extractSearchTemplate(url) {
            try {
                const urlObj = new URL(url);
                const params = new URLSearchParams(urlObj.search);

                // 常见的搜索参数
                const searchParams = ['q', 'query', 'search', 'keyword', 'search_query', 'wd'];

                for (const param of searchParams) {
                    if (params.has(param)) {
                        params.set(param, '{query}');
                        return urlObj.origin + urlObj.pathname + '?' + params.toString();
                    }
                }

                return url;
            } catch (error) {
                return url;
            }
        }

        // 提取域名
        extractDomain(url) {
            try {
                return new URL(url).hostname;
            } catch (error) {
                return 'unknown';
            }
        }

        // 合并平台数据，避免重复
        mergePlatformData(platformManagerPlatforms, pageManagerPlatforms) {
            const merged = [...platformManagerPlatforms];
            const existingDomains = new Set(platformManagerPlatforms.map(p => p.domain));

            // 添加页面管理器中不重复的平台
            pageManagerPlatforms.forEach(platform => {
                if (!existingDomains.has(platform.domain)) {
                    merged.push(platform);
                }
            });

            return merged.sort((a, b) => {
                if (a.isBuiltin && !b.isBuiltin) return -1;
                if (!a.isBuiltin && b.isBuiltin) return 1;
                return a.name.localeCompare(b.name);
            });
        }

        // 显示用户友好的消息提示
        showMessage(message, type = 'info') {
            // 获取或创建消息容器
            let messageContainer = document.getElementById('messageContainer');
            if (!messageContainer) {
                messageContainer = this.createMessageContainer();
            }

            // 创建消息元素
            const messageElement = document.createElement('div');
            const colors = {
                success: '#4CAF50',
                error: '#f44336',
                warning: '#ff9800',
                info: '#2196F3'
            };

            messageElement.style.cssText = `
                background: ${colors[type] || colors.info};
                color: white;
                padding: 12px 16px;
                margin-bottom: 10px;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                font-size: 14px;
                line-height: 1.4;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            messageElement.textContent = message;

            // 添加到容器
            messageContainer.appendChild(messageElement);

            // 显示动画
            setTimeout(() => {
                messageElement.style.opacity = '1';
                messageElement.style.transform = 'translateX(0)';
            }, 10);

            // 自动移除
            setTimeout(() => {
                messageElement.style.opacity = '0';
                messageElement.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (messageElement.parentNode) {
                        messageElement.parentNode.removeChild(messageElement);
                    }
                }, 300);
            }, type === 'error' ? 5000 : 3000); // 错误消息显示更长时间
        }

        // 创建消息容器
        createMessageContainer() {
            const messageContainer = document.createElement('div');
            messageContainer.id = 'messageContainer';
            messageContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                pointer-events: none;
            `;
            document.body.appendChild(messageContainer);
            return messageContainer;
        }

        // 高级功能相关方法
        openAdvancedConfig() {
            if (window.app?.linkPreviewManager) {
                window.app.linkPreviewManager.showAdvancedConfig();
            } else {
                this.showMessage('链接预览功能未初始化', 'error');
            }
        }

        testLinkPreview() {
            if (window.app?.linkPreviewManager) {
                // 创建一个测试链接
                const testUrl = 'https://www.baidu.com';
                window.app.linkPreviewManager.createPreview(testUrl, {
                    triggerType: 'test',
                    testMode: true
                });
                this.showMessage('测试链接预览已创建', 'success');
            } else {
                this.showMessage('链接预览功能未初始化', 'error');
            }
        }

        resetAdvancedConfig() {
            if (confirm('确定要重置所有高级功能配置吗？此操作不可撤销。')) {
                try {
                    // 清除高级配置
                    localStorage.removeItem('linkPreview_advancedConfig');
                    localStorage.removeItem('linkPreview_readingMode');
                    localStorage.removeItem('linkPreview_textOperations');
                    localStorage.removeItem('linkPreview_windowMemory');

                    this.showMessage('高级功能配置已重置', 'success');

                    // 更新状态显示
                    this.updateAdvancedFeatureStatus();
                } catch (error) {
                    console.error('重置高级配置失败:', error);
                    this.showMessage('重置配置失败', 'error');
                }
            }
        }

        updateAdvancedFeatureStatus() {
            const readingModeStatus = document.getElementById('readingModeStatus');
            const textOperationsStatus = document.getElementById('textOperationsStatus');

            if (readingModeStatus && textOperationsStatus) {
                if (window.app?.linkPreviewManager) {
                    const status = window.app.linkPreviewManager.getAdvancedFeaturesStatus();

                    readingModeStatus.textContent = status.textOperations.available ? '✅ 可用' : '❌ 不可用';
                    readingModeStatus.className = `status-value ${status.textOperations.available ? 'status-success' : 'status-error'}`;

                    textOperationsStatus.textContent = status.textOperations.available ?
                        (status.textOperations.enabled ? '✅ 已启用' : '⚠️ 已禁用') : '❌ 不可用';
                    textOperationsStatus.className = `status-value ${
                        status.textOperations.available ?
                            (status.textOperations.enabled ? 'status-success' : 'status-warning') :
                            'status-error'
                    }`;
                } else {
                    readingModeStatus.textContent = '❌ 未初始化';
                    readingModeStatus.className = 'status-value status-error';
                    textOperationsStatus.textContent = '❌ 未初始化';
                    textOperationsStatus.className = 'status-value status-error';
                }
            }
        }

        // 重写switchSection方法以支持高级功能页面
        switchSection(section) {
            // 调用原有的switchSection逻辑
            this.currentSection = section;

            // 更新导航状态
            const navItems = this.content.querySelectorAll('.settings-nav-item');
            navItems.forEach(item => {
                item.classList.toggle('active', item.dataset.section === section);
            });

            // 更新内容区域
            const sections = this.content.querySelectorAll('.settings-section');
            sections.forEach(sectionEl => {
                sectionEl.classList.toggle('active', sectionEl.dataset.section === section);
            });

            // 如果切换到高级功能页面，更新状态
            if (section === 'advanced') {
                setTimeout(() => {
                    this.updateAdvancedFeatureStatus();
                }, 100);
            }
        }


    }

    // 页面管理器
    class PageManager {
        constructor() {
            this.pages = [];
            this.currentPageIndex = 0;
            this.maxPages = 16; // 增加最大页面数以支持双模式

            // DOM元素
            this.normalPageList = document.getElementById('normalPageList');
            this.quickPageList = document.getElementById('quickPageList');
            this.addNormalPageBtn = document.getElementById('addNormalPageBtn');
            this.addQuickPageBtn = document.getElementById('addQuickPageBtn');
            this.currentPageName = document.getElementById('currentPageName');
            this.pageCounter = document.getElementById('pageCounter');
            this.mainContent = document.getElementById('mainContent');

            this.init();
        }

        init() {
            this.loadPages();
            // 确保数据加载完成后再渲染
            this.render();
            this.bindEvents();
            this.bindWheelEvents();
            console.log('✅ 页面管理器初始化完成');
        }

        loadPages() {
            // 加载保存的页面数据
            const saved = Storage.get('pages_v3');

            if (saved && Array.isArray(saved) && saved.length > 0) {
                // 迁移旧数据：为没有mode属性的页面添加默认mode
                this.pages = saved.map(page => ({
                    ...page,
                    mode: page.mode || 'normal' // 默认为常规搜索模式
                }));
            } else {
                // 创建默认页面（每种模式一个）
                // 如果ShortcutManager已经初始化，使用其数据；否则使用默认数据
                const currentShortcuts = window.app?.shortcutManager?.shortcuts || [];
                const hasCurrentShortcuts = currentShortcuts.length > 0;

                this.pages = [
                    {
                        id: Utils.generateId(),
                        name: '常规搜索',
                        icon: '🏠',
                        mode: 'normal',
                        shortcuts: hasCurrentShortcuts ?
                            this.convertShortcutsForMode(currentShortcuts, 'normal') :
                            this.getDefaultShortcutsForMode('normal'),
                        order: 1
                    },
                    {
                        id: Utils.generateId(),
                        name: '快捷搜索',
                        icon: '⚡',
                        mode: 'quick',
                        shortcuts: hasCurrentShortcuts ?
                            this.convertShortcutsForMode(currentShortcuts, 'quick') :
                            this.getDefaultShortcutsForMode('quick'),
                        order: 2
                    }
                ];
                this.savePages();
            }
        }

        savePages() {
            Storage.set('pages_v3', this.pages);
        }

        // 将现有快捷方式转换为特定模式的格式
        convertShortcutsForMode(shortcuts, mode) {
            return shortcuts.map(shortcut => ({
                ...shortcut,
                pageMode: mode,
                searchMode: mode === 'quick' ? 'search' : shortcut.searchMode || 'direct'
            }));
        }

        // 获取模式特定的默认快捷方式
        getDefaultShortcutsForMode(mode) {
            // 优先使用ConfigManager的数据
            const configManager = window.app?.configManager;
            if (configManager && configManager.shortcuts && configManager.shortcuts.length > 0) {
                console.log(`📋 PageManager使用ConfigManager的${configManager.shortcuts.length}个快捷方式作为默认数据`);
                return configManager.shortcuts.map(shortcut => ({
                    ...shortcut,
                    pageMode: mode,
                    searchMode: mode === 'quick' ? 'search' : 'direct'
                }));
            }

            // 备用方案：使用SearchPlatformConfig
            const searchPlatformConfig = new window.SearchPlatformConfig();

            if (mode === 'quick') {
                // 快捷搜索模式：创建支持搜索的快捷方式
                // 修复：使用搜索模板URL而不是常规URL，确保快捷搜索功能正常
                return searchPlatformConfig.platforms.map((platform, index) => ({
                    id: Utils.generateId(),
                    name: platform.name,
                    url: platform.urls.search, // 修复：使用搜索模板URL
                    searchUrl: platform.urls.search, // 保持一致
                    icon: platform.icon,
                    order: index + 1,
                    category: platform.category,
                    searchMode: 'search',
                    pageMode: 'quick'
                }));
            } else {
                // 常规搜索模式：创建直接跳转的快捷方式
                return searchPlatformConfig.platforms.map((platform, index) => ({
                    id: Utils.generateId(),
                    name: platform.name,
                    url: platform.urls.normal,
                    icon: platform.icon,
                    order: index + 1,
                    category: platform.category,
                    searchMode: 'direct',
                    pageMode: 'normal'
                }));
            }
        }

        // 根据模式获取页面
        getPagesByMode(mode) {
            return this.pages.filter(page => page.mode === mode);
        }

        render() {
            this.renderPageList();
            this.renderCurrentPage();
            this.updatePageIndicator();
        }

        renderPageList() {
            this.renderNormalPages();
            this.renderQuickPages();
            this.bindPageEvents();
        }

        renderNormalPages() {
            this.renderPagesByMode('normal', this.normalPageList);
        }

        renderQuickPages() {
            this.renderPagesByMode('quick', this.quickPageList);
        }

        // 通用的页面渲染方法，避免重复代码
        renderPagesByMode(mode, container) {
            if (!container) return;

            const pages = this.pages.filter(page => page.mode === mode);
            container.innerHTML = pages
                .sort((a, b) => a.order - b.order)
                .map((page) => {
                    const pageIndex = this.pages.findIndex(p => p.id === page.id);
                    return `
                        <div class="page-item ${pageIndex === this.currentPageIndex ? 'active' : ''}"
                             data-page-id="${page.id}"
                             data-page-index="${pageIndex}"
                             title="${page.name}">
                            ${page.icon}
                        </div>
                    `;
                }).join('');
        }

        async renderCurrentPage() {
            const currentPage = this.pages[this.currentPageIndex];
            if (!currentPage || !window.app?.shortcutManager) return;

            try {
                // 设置页面切换状态，避免不必要的预加载
                window.app.shortcutManager.isPageSwitching = true;

                // 更新快捷方式管理器的数据
                window.app.shortcutManager.shortcuts = currentPage.shortcuts || [];
                await window.app.shortcutManager.render();
            } catch (error) {
                console.error('渲染当前页面失败:', error);
            } finally {
                // 确保状态总是被清除，即使出现错误
                if (window.app?.shortcutManager) {
                    window.app.shortcutManager.isPageSwitching = false;
                }
            }
        }

        updatePageIndicator() {
            const currentPage = this.pages[this.currentPageIndex];
            if (this.currentPageName && currentPage) {
                this.currentPageName.textContent = currentPage.name;
            }
            if (this.pageCounter) {
                this.pageCounter.textContent = `${this.currentPageIndex + 1} / ${this.pages.length}`;
            }
        }

        bindEvents() {
            // 添加常规搜索页面按钮
            if (this.addNormalPageBtn) {
                this.addNormalPageBtn.addEventListener('click', () => this.showAddPageModal('normal'));
            }

            // 添加快捷搜索页面按钮
            if (this.addQuickPageBtn) {
                this.addQuickPageBtn.addEventListener('click', () => this.showAddPageModal('quick'));
            }

            // 页面模态框事件
            this.bindPageModalEvents();
        }

        bindPageEvents() {
            // 绑定常规搜索页面事件
            const normalPageItems = this.normalPageList?.querySelectorAll('.page-item');
            if (normalPageItems) {
                normalPageItems.forEach(item => {
                    this.bindSinglePageEvents(item);
                });
            }

            // 绑定快捷搜索页面事件
            const quickPageItems = this.quickPageList?.querySelectorAll('.page-item');
            if (quickPageItems) {
                quickPageItems.forEach(item => {
                    this.bindSinglePageEvents(item);
                });
            }
        }

        bindSinglePageEvents(item) {
            // 点击切换页面
            item.addEventListener('click', () => {
                const pageIndex = parseInt(item.dataset.pageIndex);
                this.switchToPage(pageIndex);
            });

            // 右键菜单
            item.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                const pageId = item.dataset.pageId;
                this.showPageContextMenu(e, pageId);
            });
        }

        bindWheelEvents() {
            // 鼠标滚轮切换页面
            if (this.mainContent) {
                this.mainContent.addEventListener('wheel', (e) => {
                    e.preventDefault();

                    if (e.deltaY > 0) {
                        // 向下滚动，切换到下一页
                        this.switchToNextPage();
                    } else {
                        // 向上滚动，切换到上一页
                        this.switchToPrevPage();
                    }
                }, { passive: false });
            }
        }

        bindPageModalEvents() {
            const modal = document.getElementById('pageModalOverlay');
            const closeBtn = document.getElementById('closePageModal');
            const cancelBtn = document.getElementById('cancelPageBtn');
            const confirmBtn = document.getElementById('confirmPageBtn');

            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.hidePageModal());
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => this.hidePageModal());
            }

            if (confirmBtn) {
                confirmBtn.addEventListener('click', () => this.handlePageModalConfirm());
            }

            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.hidePageModal();
                    }
                });
            }

            // 图标选择器事件
            this.bindIconSelectorEvents();

            // 页面右键菜单事件
            this.bindPageContextMenuEvents();
        }

        bindIconSelectorEvents() {
            // 使用事件委托避免重复绑定事件监听器
            const iconGrid = document.getElementById('iconGrid');
            const iconInput = document.getElementById('pageIcon');

            if (!iconGrid || iconGrid.dataset.eventsBound) return;

            iconGrid.addEventListener('click', (e) => {
                const option = e.target.closest('.icon-option');
                if (!option) return;

                e.stopPropagation();
                const selectedIconValue = option.dataset.icon;

                // 更新隐藏输入框的值
                if (iconInput) iconInput.value = selectedIconValue;

                // 更新选中状态
                const iconOptions = iconGrid.querySelectorAll('.icon-option');
                iconOptions.forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
            });

            // 标记事件已绑定，避免重复绑定
            iconGrid.dataset.eventsBound = 'true';
        }

        updateIconSelection(iconValue) {
            const iconOptions = document.querySelectorAll('.icon-option');
            iconOptions.forEach(option => {
                if (option.dataset.icon === iconValue) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });
        }

        bindPageContextMenuEvents() {
            const editItem = document.getElementById('editPage');
            const deleteItem = document.getElementById('deletePage');

            if (editItem) {
                editItem.addEventListener('click', () => {
                    this.editPage(this.contextMenuTargetId);
                    this.hidePageContextMenu();
                });
            }

            if (deleteItem) {
                deleteItem.addEventListener('click', () => {
                    this.deletePage(this.contextMenuTargetId);
                    this.hidePageContextMenu();
                });
            }

            // 使用全局事件管理器避免重复监听器
            this.pageGlobalClickHandler = () => {
                this.hidePageContextMenu();
            };
            GlobalEventManager.addClickHandler(this.pageGlobalClickHandler);
        }

        switchToPage(pageIndexOrId) {
            let pageIndex;

            // 支持通过页面ID或索引切换
            if (typeof pageIndexOrId === 'string') {
                // 通过页面ID查找索引
                pageIndex = this.pages.findIndex(page => page.id === pageIndexOrId);
                if (pageIndex === -1) return;
            } else {
                // 直接使用索引
                pageIndex = pageIndexOrId;
                if (pageIndex < 0 || pageIndex >= this.pages.length) return;
            }

            // 保存当前页面的快捷方式
            this.saveCurrentPageShortcuts();

            // 切换到新页面
            this.currentPageIndex = pageIndex;

            // 更新搜索管理器的模式
            const currentPage = this.pages[pageIndex];
            if (currentPage && window.app?.searchManager) {
                const searchMode = currentPage.mode === 'quick' ? 'quick' : 'normal';
                window.app.searchManager.setSearchMode(searchMode);
            }

            this.render();

            // 添加切换动画
            this.addSwitchAnimation();
        }

        switchToNextPage() {
            const nextIndex = (this.currentPageIndex + 1) % this.pages.length;
            this.switchToPage(nextIndex);
        }

        switchToPrevPage() {
            const prevIndex = (this.currentPageIndex - 1 + this.pages.length) % this.pages.length;
            this.switchToPage(prevIndex);
        }

        saveCurrentPageShortcuts() {
            if (window.app?.shortcutManager && this.pages[this.currentPageIndex]) {
                this.pages[this.currentPageIndex].shortcuts = [...window.app.shortcutManager.shortcuts];
                this.savePages();
            }
        }

        addSwitchAnimation() {
            const shortcutsGrid = document.getElementById('shortcutsGrid');
            if (shortcutsGrid) {
                shortcutsGrid.style.opacity = '0';
                shortcutsGrid.style.transform = 'translateX(-50%) translateY(10px)';

                setTimeout(() => {
                    shortcutsGrid.style.opacity = '1';
                    shortcutsGrid.style.transform = 'translateX(-50%) translateY(0)';
                }, 150);
            }
        }

        showAddPageModal(mode = 'normal') {
            const modeNames = {
                'normal': '常规搜索页面',
                'quick': '快捷搜索页面'
            };
            this.showPageModal(`添加${modeNames[mode]}`, null, mode);
        }

        showPageModal(title = '添加页面', page = null, mode = 'normal') {
            const modal = document.getElementById('pageModalOverlay');
            const modalTitle = document.getElementById('pageModalTitle');
            const nameInput = document.getElementById('pageName');
            const iconInput = document.getElementById('pageIcon');

            if (!modal) return;

            if (modalTitle) modalTitle.textContent = title;

            // 设置表单数据
            const iconValue = page ? page.icon : (mode === 'quick' ? '⚡' : '📄');
            const nameValue = page ? page.name : '';

            if (nameInput) nameInput.value = nameValue;
            if (iconInput) iconInput.value = iconValue;

            // 更新图标选中状态（统一处理）
            this.updateIconSelection(iconValue);

            this.currentEditingPageId = page ? page.id : null;
            this.currentEditingPageMode = page ? page.mode : mode; // 保存当前编辑的页面模式

            modal.style.display = 'flex';

            // 聚焦到名称输入框
            setTimeout(() => {
                if (nameInput) nameInput.focus();
            }, 100);
        }

        hidePageModal() {
            const modal = document.getElementById('pageModalOverlay');

            if (modal) {
                modal.style.display = 'none';
            }

            this.currentEditingPageId = null;
        }

        handlePageModalConfirm() {
            const nameInput = document.getElementById('pageName');
            const iconInput = document.getElementById('pageIcon');

            const name = nameInput?.value.trim();
            const icon = iconInput?.value.trim();

            if (!name || !icon) {
                alert('请填写完整信息');
                return;
            }

            if (this.currentEditingPageId) {
                // 编辑模式
                this.updatePage(this.currentEditingPageId, { name, icon });
            } else {
                // 添加模式
                this.addPage(name, icon, this.currentEditingPageMode || 'normal');
            }

            this.hidePageModal();
        }

        addPage(name, icon, mode = 'normal') {
            if (this.pages.length >= this.maxPages) {
                alert(`最多只能创建 ${this.maxPages} 个页面`);
                return false;
            }

            const newPage = {
                id: Utils.generateId(),
                name,
                icon,
                mode, // 添加模式信息
                shortcuts: [],
                order: this.pages.length + 1
            };

            this.pages.push(newPage);
            this.savePages();
            this.render();
            return true;
        }

        updatePage(id, data) {
            const index = this.pages.findIndex(p => p.id === id);
            if (index !== -1) {
                this.pages[index] = { ...this.pages[index], ...data };
                this.savePages();
                this.render();
            }
        }

        deletePage(id) {
            if (this.pages.length <= 1) {
                alert('至少需要保留一个页面');
                return;
            }

            if (confirm('确定要删除这个页面吗？页面中的所有快捷方式也会被删除。')) {
                const index = this.pages.findIndex(p => p.id === id);
                if (index !== -1) {
                    this.pages.splice(index, 1);

                    // 调整当前页面索引
                    if (this.currentPageIndex >= this.pages.length) {
                        this.currentPageIndex = this.pages.length - 1;
                    }

                    this.savePages();
                    this.render();
                }
            }
        }

        editPage(id) {
            const page = this.pages.find(p => p.id === id);
            if (page) {
                this.showPageModal('编辑页面', page);
            }
        }

        showPageContextMenu(event, pageId) {
            const contextMenu = document.getElementById('pageContextMenu');
            if (!contextMenu) return;

            this.contextMenuTargetId = pageId;

            contextMenu.style.display = 'block';
            contextMenu.style.left = event.pageX + 'px';
            contextMenu.style.top = event.pageY + 'px';
        }

        hidePageContextMenu() {
            const contextMenu = document.getElementById('pageContextMenu');
            if (contextMenu) {
                contextMenu.style.display = 'none';
            }
        }

        // 获取当前页面
        getCurrentPage() {
            return this.pages[this.currentPageIndex];
        }

        // 导出页面数据
        exportPages() {
            return {
                pages: this.pages,
                currentPageIndex: this.currentPageIndex
            };
        }

        // 导入页面数据
        importPages(data) {
            if (data.pages && Array.isArray(data.pages)) {
                this.pages = data.pages;
                this.currentPageIndex = data.currentPageIndex || 0;

                // 确保索引有效
                if (this.currentPageIndex >= this.pages.length) {
                    this.currentPageIndex = 0;
                }

                this.savePages();
                this.render();
                return true;
            }
            return false;
        }
    }

    // IndexedDB壁纸存储类
    class WallpaperDB {
        constructor() {
            this.dbName = 'MomentSearchWallpapers';
            this.version = 1;
            this.storeName = 'wallpapers';
        }

        async openDatabase() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(this.dbName, this.version);

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(this.storeName)) {
                        db.createObjectStore(this.storeName);
                    }
                };

                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        }

        async saveWallpaper(imageBlob) {
            const db = await this.openDatabase();
            return new Promise((resolve, reject) => {
                const transaction = db.transaction(this.storeName, "readwrite");
                const store = transaction.objectStore(this.storeName);

                store.put(imageBlob, "backgroundImage");
                store.put(new Date().toISOString(), "timestamp");

                transaction.oncomplete = () => resolve();
                transaction.onerror = () => reject(transaction.error);
            });
        }

        async loadWallpaper() {
            const db = await this.openDatabase();
            return new Promise((resolve) => {
                const transaction = db.transaction(this.storeName, "readonly");
                const store = transaction.objectStore(this.storeName);
                const request = store.get("backgroundImage");

                request.onsuccess = () => resolve(request.result);
                request.onerror = () => resolve(null);
            });
        }

        async clearWallpaper() {
            const db = await this.openDatabase();
            return new Promise((resolve, reject) => {
                const transaction = db.transaction(this.storeName, "readwrite");
                const store = transaction.objectStore(this.storeName);

                store.delete("backgroundImage");
                store.delete("timestamp");

                transaction.oncomplete = () => resolve();
                transaction.onerror = () => reject(transaction.error);
            });
        }
    }

    // 壁纸编辑器
    class WallpaperEditor {
        constructor() {
            this.modal = document.getElementById('wallpaperEditorModal');
            this.previewImage = document.getElementById('wallpaperPreviewImage');
            this.currentImageUrl = null;
            this.currentSettings = {
                positionX: 0,
                positionY: 0,
                scale: 100,
                rotation: 0
            };

            this.isDragging = false;
            this.dragStart = { x: 0, y: 0 };
            this.dragOffset = { x: 0, y: 0 };

            this.initializeControls();
            this.bindEvents();
        }

        initializeControls() {
            // 获取控制元素
            this.controls = {
                positionX: document.getElementById('positionX'),
                positionY: document.getElementById('positionY'),
                scale: document.getElementById('scale'),
                rotation: document.getElementById('rotation'),

                // 值显示元素
                positionXValue: document.getElementById('positionXValue'),
                positionYValue: document.getElementById('positionYValue'),
                scaleValue: document.getElementById('scaleValue'),
                rotationValue: document.getElementById('rotationValue'),

                // 快速操作按钮
                resetPosition: document.getElementById('resetPosition'),
                resetScale: document.getElementById('resetScale'),
                resetRotation: document.getElementById('resetRotation'),
                resetAll: document.getElementById('resetAll'),

                // 模态框控制
                closeBtn: document.getElementById('wallpaperEditorClose'),
                cancelBtn: document.getElementById('wallpaperEditorCancel'),
                applyBtn: document.getElementById('wallpaperEditorApply')
            };
        }

        bindEvents() {
            // 滑块控制事件
            this.controls.positionX.addEventListener('input', () => this.updatePosition());
            this.controls.positionY.addEventListener('input', () => this.updatePosition());
            this.controls.scale.addEventListener('input', () => this.updateScale());
            this.controls.rotation.addEventListener('input', () => this.updateRotation());

            // 快速操作按钮
            this.controls.resetPosition.addEventListener('click', () => this.resetPosition());
            this.controls.resetScale.addEventListener('click', () => this.resetScale());
            this.controls.resetRotation.addEventListener('click', () => this.resetRotation());
            this.controls.resetAll.addEventListener('click', () => this.resetAll());

            // 模态框控制
            this.controls.closeBtn.addEventListener('click', () => this.close());
            this.controls.cancelBtn.addEventListener('click', () => this.close());
            this.controls.applyBtn.addEventListener('click', () => this.apply());

            // 预览区域交互事件
            this.bindPreviewEvents();

            // 模态框背景点击关闭
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal || e.target.classList.contains('wallpaper-editor-overlay')) {
                    this.close();
                }
            });
        }

        bindPreviewEvents() {
            // 拖拽事件（在所有模式下都支持）
            this.previewImage.addEventListener('mousedown', (e) => this.startDrag(e));
            document.addEventListener('mousemove', (e) => this.drag(e));
            document.addEventListener('mouseup', () => this.endDrag());

            // 鼠标滚轮缩放
            this.previewImage.addEventListener('wheel', (e) => this.handleWheel(e));

            // 双击重置全部参数
            this.previewImage.addEventListener('dblclick', () => this.resetAll());

            // 鼠标悬停效果
            this.previewImage.addEventListener('mouseenter', () => this.showInteractionHint());
            this.previewImage.addEventListener('mouseleave', () => this.hideInteractionHint());
        }

        open(imageUrl, existingSettings = null) {
            // 清理之前的图片URL
            this.cleanupImageUrl();

            this.currentImageUrl = imageUrl;

            // 如果有现有设置，使用它们；否则使用默认设置
            if (existingSettings) {
                this.currentSettings = { ...existingSettings };
            } else {
                this.resetSettings();
            }

            // 设置预览图片
            this.previewImage.src = imageUrl;

            // 等待图片加载完成后应用设置
            this.previewImage.onload = () => {
                // 更新控制器值
                this.updateControlValues();

                // 应用当前设置到预览
                this.applyPreviewSettings();
            };

            // 如果图片已经加载完成，立即应用设置
            if (this.previewImage.complete) {
                this.updateControlValues();
                this.applyPreviewSettings();
            }

            // 显示模态框
            this.modal.style.display = 'flex';

            // 添加动画类
            setTimeout(() => {
                this.modal.classList.add('show');
            }, 10);
        }

        close() {
            this.modal.classList.remove('show');
            setTimeout(() => {
                this.modal.style.display = 'none';
            }, 300);
        }

        // 清理图片URL
        cleanupImageUrl() {
            if (this.currentImageUrl) {
                try {
                    URL.revokeObjectURL(this.currentImageUrl);
                    console.log('已清理编辑器图片内存URL');
                } catch (error) {
                    console.warn('清理编辑器图片URL失败:', error);
                }
                this.currentImageUrl = null;
            }
        }

        // 清理所有资源
        cleanup() {
            this.cleanupImageUrl();
        }

        updatePosition() {
            this.currentSettings.positionX = parseInt(this.controls.positionX.value);
            this.currentSettings.positionY = parseInt(this.controls.positionY.value);

            this.controls.positionXValue.textContent = `${this.currentSettings.positionX}%`;
            this.controls.positionYValue.textContent = `${this.currentSettings.positionY}%`;

            this.applyPreviewSettings();
        }

        updateScale() {
            this.currentSettings.scale = parseInt(this.controls.scale.value);
            this.controls.scaleValue.textContent = `${this.currentSettings.scale}%`;
            this.applyPreviewSettings();
        }

        updateRotation() {
            this.currentSettings.rotation = parseInt(this.controls.rotation.value);
            this.controls.rotationValue.textContent = `${this.currentSettings.rotation}°`;
            this.applyPreviewSettings();
        }



        applyPreviewSettings() {
            const { positionX, positionY, scale, rotation } = this.currentSettings;

            // 计算图片的尺寸和位置（使用Cover模式）
            const containerWidth = this.previewImage.parentElement.clientWidth;
            const containerHeight = this.previewImage.parentElement.clientHeight;

            // Cover模式：图片覆盖整个容器
            const imageAspect = this.previewImage.naturalWidth / this.previewImage.naturalHeight;
            const containerAspect = containerWidth / containerHeight;

            let imageWidth, imageHeight;
            if (imageAspect > containerAspect) {
                imageHeight = containerHeight * (scale / 100);
                imageWidth = imageHeight * imageAspect;
            } else {
                imageWidth = containerWidth * (scale / 100);
                imageHeight = imageWidth / imageAspect;
            }

            // 计算位置偏移
            const offsetX = (positionX / 100) * (containerWidth / 2);
            const offsetY = (positionY / 100) * (containerHeight / 2);

            // 应用变换
            const transform = `translate(-50%, -50%) translate(${offsetX}px, ${offsetY}px) rotate(${rotation}deg)`;

            this.previewImage.style.width = `${imageWidth}px`;
            this.previewImage.style.height = `${imageHeight}px`;
            this.previewImage.style.transform = transform;
        }

        startDrag(e) {
            // 在所有模式下都支持拖拽
            this.isDragging = true;
            this.dragStart.x = e.clientX;
            this.dragStart.y = e.clientY;
            this.dragOffset.x = this.currentSettings.positionX;
            this.dragOffset.y = this.currentSettings.positionY;

            this.previewImage.style.cursor = 'grabbing';
            this.showDragIndicator();
            e.preventDefault();
        }

        drag(e) {
            if (this.isDragging) {
                const deltaX = e.clientX - this.dragStart.x;
                const deltaY = e.clientY - this.dragStart.y;

                // 转换像素移动到百分比
                const containerRect = this.previewImage.parentElement.getBoundingClientRect();
                const percentX = (deltaX / containerRect.width) * 200; // 200% 范围 (-100% 到 +100%)
                const percentY = (deltaY / containerRect.height) * 200;

                const newX = Math.max(-100, Math.min(100, this.dragOffset.x + percentX));
                const newY = Math.max(-100, Math.min(100, this.dragOffset.y + percentY));

                this.currentSettings.positionX = Math.round(newX);
                this.currentSettings.positionY = Math.round(newY);

                this.updateControlValues();
                this.applyPreviewSettings();
            }
        }

        endDrag() {
            if (this.isDragging) {
                this.isDragging = false;
                this.previewImage.style.cursor = 'move';
                this.hideDragIndicator();
            }
        }

        // 鼠标滚轮缩放
        handleWheel(e) {
            e.preventDefault();

            const delta = e.deltaY > 0 ? -5 : 5; // 向上滚动放大，向下滚动缩小
            const newScale = Math.max(50, Math.min(200, this.currentSettings.scale + delta));

            if (newScale !== this.currentSettings.scale) {
                this.currentSettings.scale = newScale;
                this.updateControlValues();
                this.applyPreviewSettings();
            }
        }

        // 显示交互提示
        showInteractionHint() {
            if (!this.isDragging) {
                this.previewImage.style.cursor = 'move';
                // 可以添加更多视觉提示
            }
        }

        // 隐藏交互提示
        hideInteractionHint() {
            if (!this.isDragging) {
                this.previewImage.style.cursor = 'default';
            }
        }

        // 显示拖拽指示器
        showDragIndicator() {
            // 可以添加视觉反馈，比如改变预览区域的边框
            const viewport = this.previewImage.parentElement;
            if (viewport) {
                viewport.style.boxShadow = '0 0 0 2px #1976d2';
            }
        }

        // 隐藏拖拽指示器
        hideDragIndicator() {
            const viewport = this.previewImage.parentElement;
            if (viewport) {
                viewport.style.boxShadow = '';
            }
        }

        resetPosition() {
            this.currentSettings.positionX = 0;
            this.currentSettings.positionY = 0;
            this.updateControlValues();
            this.applyPreviewSettings();
        }

        resetScale() {
            this.currentSettings.scale = 100;
            this.updateControlValues();
            this.applyPreviewSettings();
        }

        resetRotation() {
            this.currentSettings.rotation = 0;
            this.updateControlValues();
            this.applyPreviewSettings();
        }

        resetAll() {
            this.resetSettings();
            this.updateControlValues();
            this.applyPreviewSettings();
        }

        resetSettings() {
            this.currentSettings = {
                positionX: 0,
                positionY: 0,
                scale: 100,
                rotation: 0
            };
        }

        updateControlValues() {
            this.controls.positionX.value = this.currentSettings.positionX;
            this.controls.positionY.value = this.currentSettings.positionY;
            this.controls.scale.value = this.currentSettings.scale;
            this.controls.rotation.value = this.currentSettings.rotation;

            this.controls.positionXValue.textContent = `${this.currentSettings.positionX}%`;
            this.controls.positionYValue.textContent = `${this.currentSettings.positionY}%`;
            this.controls.scaleValue.textContent = `${this.currentSettings.scale}%`;
            this.controls.rotationValue.textContent = `${this.currentSettings.rotation}°`;
        }

        apply() {
            // 触发应用事件，让BackgroundManager处理
            const event = new CustomEvent('wallpaperEdited', {
                detail: {
                    imageUrl: this.currentImageUrl,
                    settings: { ...this.currentSettings }
                }
            });
            document.dispatchEvent(event);

            this.close();
        }

        getSettings() {
            return { ...this.currentSettings };
        }
    }

    // 背景管理器
    class BackgroundManager {
        constructor() {
            this.backgroundLayer = document.getElementById('backgroundLayer');
            this.backgroundImage = document.getElementById('backgroundImage');
            this.wallpaperDB = new WallpaperDB(); // 添加IndexedDB支持
            this.wallpaperEditor = new WallpaperEditor(); // 添加壁纸编辑器
            this.currentImageUrl = null; // 用于内存清理
        }

        async init() {
            await this.applyBackground();
            this.bindEvents();
        }

        bindEvents() {
            // 监听壁纸编辑完成事件
            document.addEventListener('wallpaperEdited', (e) => {
                this.handleWallpaperEdited(e.detail);
            });

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', () => {
                this.cleanupImageUrl();
            });

            // 页面隐藏时清理资源（移动端兼容）
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.cleanupImageUrl();
                }
            });
        }

        async applyBackground() {
            const settings = Storage.get('settings_v3', {});
            const wallpaperType = settings.appearance?.wallpaper || 'default';

            try {
                if (wallpaperType === 'default') {
                    this.applyDefaultBackground();
                } else if (wallpaperType === 'custom') {
                    // 预加载自定义壁纸
                    const success = await this.preloadAndApplyCustomWallpaper();
                    if (!success) {
                        console.warn('自定义壁纸加载失败，使用默认背景');
                        this.applyDefaultBackground();
                    }
                } else if (wallpaperType.startsWith('data:') || wallpaperType.startsWith('http')) {
                    // 兼容旧版本：预加载后应用
                    await this.preloadAndApplyLegacyWallpaper(wallpaperType);
                }
            } catch (error) {
                console.error('背景应用失败:', error);
                this.applyDefaultBackground();
            }

            console.log('✅ 背景管理器初始化完成');
        }

        // 预加载并应用自定义壁纸
        async preloadAndApplyCustomWallpaper() {
            try {
                const imageBlob = await this.wallpaperDB.loadWallpaper();
                if (imageBlob) {
                    // 清理之前的图片URL
                    this.cleanupImageUrl();

                    const imageUrl = URL.createObjectURL(imageBlob);
                    this.currentImageUrl = imageUrl;

                    // 使用共享的预加载工具，增加超时时间到3秒
                    await ImagePreloader.preloadImage(imageUrl, 3000);

                    // 获取保存的壁纸设置
                    const settings = Storage.get('settings_v3', {});
                    const wallpaperSettings = settings.appearance?.wallpaperSettings || null;

                    // 预加载完成后应用壁纸
                    this.applyWallpaper(imageUrl, wallpaperSettings);
                    return true;
                }
                return false;
            } catch (error) {
                console.error('预加载自定义壁纸失败:', error);
                // 预加载失败时也要清理URL
                this.cleanupImageUrl();
                return false;
            }
        }

        // 预加载并应用旧版壁纸
        async preloadAndApplyLegacyWallpaper(wallpaperType) {
            try {
                // 使用共享的预加载工具，增加超时时间到3秒
                await ImagePreloader.preloadImage(wallpaperType, 3000);

                // 预加载完成后应用壁纸
                this.applyLegacyWallpaper(wallpaperType);
                await this.migrateOldWallpaper(wallpaperType);
            } catch (error) {
                console.error('预加载旧版壁纸失败:', error);
                this.applyDefaultBackground();
            }
        }

        applyDefaultBackground() {
            if (this.backgroundLayer) {
                // 应用纯白色背景，而不是彩色渐变
                this.backgroundLayer.style.setProperty('background', '#ffffff', 'important');
                this.backgroundLayer.style.setProperty('background-image', 'none', 'important');
            }
        }

        applyLegacyWallpaper(wallpaperType) {
            if (this.backgroundLayer) {
                this.backgroundLayer.style.backgroundImage = `url(${wallpaperType})`;
                this.backgroundLayer.style.background = '';
            }
        }

        async migrateOldWallpaper(dataUrl) {
            await this.handleAsyncOperation(async () => {
                const response = await fetch(dataUrl);
                const blob = await response.blob();
                await this.wallpaperDB.saveWallpaper(blob);
                this.updateWallpaperSetting('custom');
            }, '壁纸迁移失败');
        }

        updateWallpaperSetting(wallpaperType) {
            const settings = Storage.get('settings_v3', {});
            settings.appearance = settings.appearance || {};
            settings.appearance.wallpaper = wallpaperType;
            Storage.set('settings_v3', settings);
        }

        showWallpaperOptions() {
            // 防止重复调用
            if (this.isSelectingWallpaper) {
                return;
            }
            this.isSelectingWallpaper = true;

            try {
                const input = this.createFileInput();
                this.setupFileInputEvents(input);
                this.triggerFileSelection(input);
            } catch (error) {
                console.error('创建文件选择器失败:', error);
                this.isSelectingWallpaper = false;
                this.showError('无法打开文件选择器，请重试');
            }
        }

        async editCurrentWallpaper() {
            try {
                // 检查是否有自定义壁纸
                const settings = Storage.get('settings_v3', {});
                if (settings.appearance?.wallpaper !== 'custom') {
                    this.showError('当前没有自定义壁纸可编辑');
                    return;
                }

                // 从IndexedDB加载当前壁纸
                const imageBlob = await this.wallpaperDB.loadWallpaper();
                if (!imageBlob) {
                    this.showError('无法加载当前壁纸，请重新上传');
                    return;
                }

                // 清理之前的编辑器图片URL（如果有）
                this.cleanupImageUrl();

                // 创建图片URL
                const imageUrl = URL.createObjectURL(imageBlob);
                this.currentImageUrl = imageUrl;

                // 获取现有的壁纸设置
                const existingSettings = settings.appearance?.wallpaperSettings || null;

                // 打开壁纸编辑器，使用现有设置
                this.wallpaperEditor.open(imageUrl, existingSettings);

            } catch (error) {
                console.error('编辑壁纸失败:', error);
                this.showError('编辑壁纸失败，请重试');
            }
        }

        createFileInput() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = false;

            // 设置样式确保不可见但可交互
            input.style.position = 'absolute';
            input.style.left = '-9999px';
            input.style.opacity = '0';
            input.style.pointerEvents = 'none';

            return input;
        }

        setupFileInputEvents(input) {
            const cleanup = () => {
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            };

            const handleChange = (e) => {
                this.isSelectingWallpaper = false;
                const file = e.target.files[0];
                if (file) {
                    this.handleWallpaperUpload(file);
                }
                cleanup();
            };

            const handleCancel = () => {
                this.isSelectingWallpaper = false;
                cleanup();
            };

            input.addEventListener('change', handleChange);
            input.addEventListener('cancel', handleCancel);

            // 安全清理机制
            setTimeout(() => {
                if (this.isSelectingWallpaper) {
                    this.isSelectingWallpaper = false;
                    cleanup();
                }
            }, 30000);
        }

        triggerFileSelection(input) {
            document.body.appendChild(input);
            input.click();
        }

        async handleWallpaperUpload(file) {
            try {
                // 检查文件大小限制
                const maxSize = 50 * 1024 * 1024; // 50MB
                if (file.size > maxSize) {
                    this.showError('图片文件过大，请选择小于50MB的图片');
                    return;
                }

                // 压缩图片（如果需要）
                const compressedFile = await this.compressImage(file);
                console.log(`图片压缩: ${Math.round(file.size/1024)}KB → ${Math.round(compressedFile.size/1024)}KB`);

                // 保存到IndexedDB
                await this.wallpaperDB.saveWallpaper(compressedFile);

                // 创建图片URL并打开编辑器
                const imageUrl = URL.createObjectURL(compressedFile);

                // 清理之前的图片URL
                this.cleanupImageUrl();
                this.currentImageUrl = imageUrl;

                // 新上传的壁纸始终使用默认设置，不使用之前的自定义设置
                // 这样用户可以从干净的默认状态开始编辑新壁纸
                this.wallpaperEditor.open(imageUrl, null);

            } catch (error) {
                console.error('保存壁纸失败:', error);
                this.showError('壁纸保存失败，请重试');
            }
        }

        // 处理壁纸编辑完成事件
        handleWallpaperEdited(data) {
            const { imageUrl, settings } = data;

            // 保存编辑设置
            this.saveWallpaperSettings(settings);

            // 应用壁纸
            this.applyWallpaper(imageUrl, settings);

            // 更新设置
            this.updateWallpaperSetting('custom');
        }

        // 保存壁纸编辑设置
        saveWallpaperSettings(settings) {
            const currentSettings = Storage.get('settings_v3', {});
            currentSettings.appearance = currentSettings.appearance || {};
            currentSettings.appearance.wallpaperSettings = settings;
            Storage.set('settings_v3', currentSettings);
        }

        showError(message) {
            // 统一的错误提示方法
            alert(message);
        }

        async handleAsyncOperation(operation, errorMessage) {
            try {
                return await operation();
            } catch (error) {
                console.error(errorMessage, error);
                this.showError(errorMessage);
                return null;
            }
        }

        // 图片压缩方法
        async compressImage(file, maxSize = 1024 * 1024, initialQuality = 0.8) {
            // 如果文件已经小于限制大小，直接返回
            if (file.size <= maxSize) {
                return file;
            }

            // 只压缩JPEG和PNG格式，其他格式直接返回
            if (!file.type.match(/^image\/(jpeg|png)$/)) {
                console.log('非JPEG/PNG格式，跳过压缩:', file.type);
                return file;
            }

            // 使用循环而不是递归，避免栈溢出
            let quality = initialQuality;
            const minQuality = 0.3;
            const qualityStep = 0.2;

            while (quality >= minQuality) {
                try {
                    const compressedBlob = await this.compressImageWithQuality(file, maxSize, quality);

                    if (compressedBlob.size <= maxSize || quality <= minQuality) {
                        return compressedBlob;
                    }

                    console.log(`压缩后仍过大(${Math.round(compressedBlob.size/1024)}KB)，降低质量重试...`);
                    quality -= qualityStep;

                } catch (error) {
                    throw error;
                }
            }

            // 如果所有质量都尝试过了，返回最后一次压缩的结果
            return this.compressImageWithQuality(file, maxSize, minQuality);
        }

        // 单次压缩方法
        compressImageWithQuality(file, maxSize, quality) {
            return new Promise((resolve, reject) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    try {
                        // 计算压缩后的尺寸
                        let { width, height } = this.calculateCompressedSize(img.width, img.height, maxSize);

                        canvas.width = width;
                        canvas.height = height;

                        // 绘制压缩后的图片
                        ctx.drawImage(img, 0, 0, width, height);

                        // 转换为Blob
                        canvas.toBlob((blob) => {
                            if (blob) {
                                resolve(blob);
                            } else {
                                reject(new Error('图片压缩失败'));
                            }
                        }, file.type, quality);

                    } catch (error) {
                        reject(error);
                    }
                };

                img.onerror = () => reject(new Error('图片加载失败'));
                img.src = URL.createObjectURL(file);
            });
        }

        // 计算压缩后的尺寸
        calculateCompressedSize(originalWidth, originalHeight, maxSize) {
            // 根据文件大小目标计算合适的尺寸
            // 假设每像素约4字节（RGBA），加上JPEG压缩比约10:1
            const targetPixels = maxSize / 4 * 10;
            const originalPixels = originalWidth * originalHeight;

            if (originalPixels <= targetPixels) {
                return { width: originalWidth, height: originalHeight };
            }

            // 按比例缩放
            const scale = Math.sqrt(targetPixels / originalPixels);
            const width = Math.floor(originalWidth * scale);
            const height = Math.floor(originalHeight * scale);

            // 确保最小尺寸
            const minSize = 800;
            if (width < minSize || height < minSize) {
                const aspectRatio = originalWidth / originalHeight;
                if (aspectRatio > 1) {
                    return { width: minSize, height: Math.floor(minSize / aspectRatio) };
                } else {
                    return { width: Math.floor(minSize * aspectRatio), height: minSize };
                }
            }

            return { width, height };
        }

        // 内存清理方法
        cleanupImageUrl() {
            if (this.currentImageUrl) {
                try {
                    URL.revokeObjectURL(this.currentImageUrl);
                    console.log('已清理图片内存URL');
                } catch (error) {
                    console.warn('清理图片URL失败:', error);
                }
                this.currentImageUrl = null;
            }
        }





        applyWallpaper(imageUrl, customSettings = null) {
            if (!this.backgroundLayer || !this.backgroundImage) {
                console.error('backgroundLayer或backgroundImage不存在');
                return;
            }

            // 清除默认背景，使用img元素
            this.backgroundLayer.style.background = 'none';
            this.backgroundImage.src = imageUrl;
            this.backgroundImage.style.display = 'block';

            // 等待图片加载完成后应用设置
            this.backgroundImage.onload = () => {
                this.applyImageSettings(customSettings);
            };

            // 如果图片已经加载完成，立即应用设置
            if (this.backgroundImage.complete) {
                this.applyImageSettings(customSettings);
            }
        }

        applyImageSettings(customSettings = null) {
            if (!this.backgroundImage) return;

            const containerWidth = this.backgroundLayer.clientWidth;
            const containerHeight = this.backgroundLayer.clientHeight;

            if (customSettings) {
                // 使用自定义设置
                const { positionX, positionY, scale, rotation } = customSettings;

                // Cover模式：图片覆盖整个容器
                const imageAspect = this.backgroundImage.naturalWidth / this.backgroundImage.naturalHeight;
                const containerAspect = containerWidth / containerHeight;

                let imageWidth, imageHeight;
                if (imageAspect > containerAspect) {
                    imageHeight = containerHeight * (scale / 100);
                    imageWidth = imageHeight * imageAspect;
                } else {
                    imageWidth = containerWidth * (scale / 100);
                    imageHeight = imageWidth / imageAspect;
                }

                // 计算位置偏移
                const offsetX = (positionX / 100) * (containerWidth / 2);
                const offsetY = (positionY / 100) * (containerHeight / 2);

                // 应用变换（包括旋转）
                const transform = `translate(-50%, -50%) translate(${offsetX}px, ${offsetY}px) rotate(${rotation}deg)`;

                this.backgroundImage.style.width = `${imageWidth}px`;
                this.backgroundImage.style.height = `${imageHeight}px`;
                this.backgroundImage.style.transform = transform;
            } else {
                // 使用默认设置：cover模式，居中显示，无旋转
                const imageAspect = this.backgroundImage.naturalWidth / this.backgroundImage.naturalHeight;
                const containerAspect = containerWidth / containerHeight;

                let imageWidth, imageHeight;
                if (imageAspect > containerAspect) {
                    imageHeight = containerHeight;
                    imageWidth = imageHeight * imageAspect;
                } else {
                    imageWidth = containerWidth;
                    imageHeight = imageWidth / imageAspect;
                }

                this.backgroundImage.style.width = `${imageWidth}px`;
                this.backgroundImage.style.height = `${imageHeight}px`;
                this.backgroundImage.style.transform = 'translate(-50%, -50%)';
            }
        }

        applyWallpaperFallback(imageUrl, customSettings = null) {
            // 备用方案：使用background-image方式
            console.warn('使用background-image备用方案');

            // 如果img元素存在，隐藏它
            if (this.backgroundImage) {
                this.backgroundImage.style.display = 'none';
            }

            // 计算背景样式
            let backgroundSize = 'cover';
            let backgroundPosition = 'center';

            if (customSettings) {
                const { positionX, positionY, scale } = customSettings;
                backgroundPosition = `${50 + positionX}% ${50 + positionY}%`;

                // 使用Cover模式，根据缩放调整背景大小
                if (scale !== 100) {
                    backgroundSize = `${scale}%`;
                }
            }

            // 应用背景样式
            this.backgroundLayer.setAttribute('style',
                `position: fixed !important; ` +
                `top: 0 !important; ` +
                `left: 0 !important; ` +
                `width: 100% !important; ` +
                `height: 100% !important; ` +
                `background-image: url("${imageUrl}") !important; ` +
                `background-size: ${backgroundSize} !important; ` +
                `background-position: ${backgroundPosition} !important; ` +
                `background-repeat: no-repeat !important; ` +
                `filter: blur(1px) !important; ` +
                `z-index: -1 !important;`
            );
        }

        async resetToDefault() {
            await this.handleAsyncOperation(async () => {
                await this.wallpaperDB.clearWallpaper();
                this.updateWallpaperSetting('default');
                await this.applyBackground();
            }, '重置壁纸失败，请重试');
        }
    }

    // 暴露到全局作用域供调试和测试使用
    window.MomentSearch = {
        Utils,
        Storage,
        URLParser,
        PlatformManager
    };

})();

// 初始化链接预览功能
document.addEventListener('DOMContentLoaded', () => {
    // 等待主应用初始化完成
    setTimeout(() => {
        if (typeof LinkPreviewManager !== 'undefined') {
            try {
                // 从设置中获取链接预览配置
                const settings = window.MomentSearch?.Storage?.get('settings_v3', {}) || {};
                const linkPreviewConfig = settings.advanced?.linkPreview || {};

                // 如果功能未被明确禁用，则启用
                if (linkPreviewConfig.enabled !== false) {
                    window.app = window.app || {};
                    window.app.linkPreviewManager = new LinkPreviewManager({
                        hostEnvironment: 'moment-search',
                        ...linkPreviewConfig
                    });

                    console.log('✅ 链接预览功能已启用');
                    console.log('💡 使用方法: 按住 Alt 键点击任意链接即可预览');
                    console.log('🔧 调试命令: debugLinkPreview()');
                } else {
                    console.log('⏸️ 链接预览功能已在设置中禁用');
                }
            } catch (error) {
                console.error('❌ 链接预览功能初始化失败:', error);
            }
        } else {
            console.warn('⚠️ LinkPreviewManager 未找到，链接预览功能不可用');
        }
    }, 2000); // 延迟2秒确保主应用完全加载
});
