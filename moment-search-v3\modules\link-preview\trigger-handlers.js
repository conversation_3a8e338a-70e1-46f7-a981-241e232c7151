/**
 * 触发处理器
 * 负责处理多种触发方式：右键菜单、悬停延迟、长按、拖拽等
 */
class TriggerHandlers {
    constructor(linkPreviewManager) {
        this.linkPreviewManager = linkPreviewManager;
        this.config = linkPreviewManager.config.triggers;
        
        // 悬停状态管理
        this.hoverState = {
            timer: null,
            currentLink: null,
            delay: 1000 // 默认1秒延迟
        };
        
        // 长按状态管理
        this.longPressState = {
            timer: null,
            currentLink: null,
            duration: 800, // 默认800ms长按
            startTime: 0,
            threshold: 10 // 移动阈值（像素）
        };
        
        // 拖拽状态管理
        this.dragState = {
            isDragging: false,
            startX: 0,
            startY: 0,
            currentLink: null,
            dropZone: null
        };
        
        // 右键菜单
        this.contextMenu = null;
        
        this.init();
    }

    /**
     * 初始化触发处理器
     */
    init() {
        this.bindTriggerEvents();
        this.createDropZone();
        console.log('✅ 触发处理器初始化完成');
    }

    /**
     * 绑定触发事件
     */
    bindTriggerEvents() {
        // 右键菜单
        if (this.config.rightClickMenu) {
            this.bindContextMenu();
        }
        
        // 悬停延迟
        if (this.config.hoverDelay) {
            this.bindHoverEvents();
        }
        
        // 长按
        if (this.config.longPress) {
            this.bindLongPressEvents();
        }
        
        // 拖拽
        if (this.config.dragLink) {
            this.bindDragEvents();
        }
    }

    /**
     * 绑定右键菜单事件
     */
    bindContextMenu() {
        document.addEventListener('contextmenu', (e) => {
            const link = e.target.closest('a[href]');
            if (link && LinkUtils.isValidLink(link)) {
                e.preventDefault();
                this.showContextMenu(e, link);
            }
        });

        // 点击其他地方时隐藏菜单
        document.addEventListener('click', () => {
            this.hideContextMenu();
        });
    }

    /**
     * 显示右键菜单
     */
    showContextMenu(event, link) {
        this.hideContextMenu(); // 先隐藏现有菜单
        
        this.contextMenu = document.createElement('div');
        this.contextMenu.className = 'link-preview-context-menu';
        this.contextMenu.innerHTML = `
            <div class="context-menu-item" data-action="preview">
                <span class="menu-icon">👁️</span>
                <span class="menu-text">预览链接</span>
            </div>
            <div class="context-menu-item" data-action="preview-new">
                <span class="menu-icon">🔗</span>
                <span class="menu-text">在新窗口预览</span>
            </div>
            <div class="context-menu-separator"></div>
            <div class="context-menu-item" data-action="copy">
                <span class="menu-icon">📋</span>
                <span class="menu-text">复制链接</span>
            </div>
        `;
        
        // 设置菜单位置
        this.contextMenu.style.cssText = `
            position: fixed;
            left: ${event.clientX}px;
            top: ${event.clientY}px;
            z-index: 99999;
        `;
        
        // 绑定菜单事件
        this.contextMenu.addEventListener('click', (e) => {
            e.stopPropagation();
            const action = e.target.closest('.context-menu-item')?.dataset.action;
            
            switch (action) {
                case 'preview':
                    this.linkPreviewManager.createPreview(link.href, {
                        triggerElement: link,
                        triggerEvent: event
                    });
                    break;
                case 'preview-new':
                    this.linkPreviewManager.createPreview(link.href, {
                        triggerElement: link,
                        triggerEvent: event,
                        forceNew: true
                    });
                    break;
                case 'copy':
                    this.copyToClipboard(link.href);
                    break;
            }
            
            this.hideContextMenu();
        });
        
        document.body.appendChild(this.contextMenu);
        
        // 确保菜单在视口内
        this.adjustMenuPosition();
    }

    /**
     * 隐藏右键菜单
     */
    hideContextMenu() {
        if (this.contextMenu) {
            this.contextMenu.remove();
            this.contextMenu = null;
        }
    }

    /**
     * 调整菜单位置
     */
    adjustMenuPosition() {
        if (!this.contextMenu) return;
        
        const rect = this.contextMenu.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        let left = parseInt(this.contextMenu.style.left);
        let top = parseInt(this.contextMenu.style.top);
        
        // 调整水平位置
        if (left + rect.width > viewportWidth) {
            left = viewportWidth - rect.width - 10;
        }
        
        // 调整垂直位置
        if (top + rect.height > viewportHeight) {
            top = viewportHeight - rect.height - 10;
        }
        
        this.contextMenu.style.left = `${left}px`;
        this.contextMenu.style.top = `${top}px`;
    }

    /**
     * 绑定悬停事件
     */
    bindHoverEvents() {
        document.addEventListener('mouseover', (e) => {
            const link = e.target.closest('a[href]');
            if (link && LinkUtils.isValidLink(link)) {
                this.startHoverTimer(link, e);
            }
        });

        document.addEventListener('mouseout', (e) => {
            const link = e.target.closest('a[href]');
            if (link === this.hoverState.currentLink) {
                this.clearHoverTimer();
            }
        });
    }

    /**
     * 开始悬停计时器
     */
    startHoverTimer(link, event) {
        this.clearHoverTimer();
        
        this.hoverState.currentLink = link;
        this.hoverState.timer = setTimeout(() => {
            this.linkPreviewManager.createPreview(link.href, {
                triggerElement: link,
                triggerEvent: event,
                triggerType: 'hover'
            });
        }, this.hoverState.delay);
    }

    /**
     * 清除悬停计时器
     */
    clearHoverTimer() {
        if (this.hoverState.timer) {
            clearTimeout(this.hoverState.timer);
            this.hoverState.timer = null;
        }
        this.hoverState.currentLink = null;
    }

    /**
     * 绑定长按事件
     */
    bindLongPressEvents() {
        document.addEventListener('mousedown', (e) => {
            const link = e.target.closest('a[href]');
            if (link && LinkUtils.isValidLink(link)) {
                this.startLongPress(link, e);
            }
        });

        document.addEventListener('mouseup', () => {
            this.endLongPress();
        });

        document.addEventListener('mousemove', (e) => {
            if (this.longPressState.timer) {
                this.checkLongPressMovement(e);
            }
        });
    }

    /**
     * 开始长按
     */
    startLongPress(link, event) {
        this.endLongPress(); // 清除之前的长按
        
        this.longPressState.currentLink = link;
        this.longPressState.startTime = Date.now();
        this.longPressState.startX = event.clientX;
        this.longPressState.startY = event.clientY;
        
        this.longPressState.timer = setTimeout(() => {
            this.linkPreviewManager.createPreview(link.href, {
                triggerElement: link,
                triggerEvent: event,
                triggerType: 'longPress'
            });
            this.endLongPress();
        }, this.longPressState.duration);
    }

    /**
     * 检查长按移动
     */
    checkLongPressMovement(event) {
        const deltaX = Math.abs(event.clientX - this.longPressState.startX);
        const deltaY = Math.abs(event.clientY - this.longPressState.startY);
        
        if (deltaX > this.longPressState.threshold || deltaY > this.longPressState.threshold) {
            this.endLongPress(); // 移动太多，取消长按
        }
    }

    /**
     * 结束长按
     */
    endLongPress() {
        if (this.longPressState.timer) {
            clearTimeout(this.longPressState.timer);
            this.longPressState.timer = null;
        }
        this.longPressState.currentLink = null;
    }

    /**
     * 绑定拖拽事件
     */
    bindDragEvents() {
        document.addEventListener('dragstart', (e) => {
            const link = e.target.closest('a[href]');
            if (link && LinkUtils.isValidLink(link)) {
                this.startDrag(link, e);
            }
        });

        document.addEventListener('dragend', () => {
            this.endDrag();
        });
    }

    /**
     * 开始拖拽
     */
    startDrag(link, event) {
        this.dragState.isDragging = true;
        this.dragState.currentLink = link;
        this.dragState.startX = event.clientX;
        this.dragState.startY = event.clientY;
        
        // 设置拖拽数据
        event.dataTransfer.setData('text/uri-list', link.href);
        event.dataTransfer.setData('text/plain', link.href);
        
        // 显示拖拽区域
        this.showDropZone();
    }

    /**
     * 结束拖拽
     */
    endDrag() {
        this.dragState.isDragging = false;
        this.dragState.currentLink = null;
        this.hideDropZone();
    }

    /**
     * 创建拖拽区域
     */
    createDropZone() {
        this.dragState.dropZone = document.createElement('div');
        this.dragState.dropZone.className = 'link-preview-drop-zone';
        this.dragState.dropZone.innerHTML = `
            <div class="drop-zone-content">
                <div class="drop-zone-icon">🔗</div>
                <div class="drop-zone-text">拖拽链接到此处预览</div>
            </div>
        `;
        
        this.dragState.dropZone.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 100px;
            background: rgba(0, 123, 255, 0.1);
            border: 2px dashed #007bff;
            border-radius: 10px;
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 99998;
            pointer-events: none;
        `;
        
        // 绑定拖拽区域事件
        this.dragState.dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.dragState.dropZone.style.background = 'rgba(0, 123, 255, 0.2)';
        });
        
        this.dragState.dropZone.addEventListener('dragleave', () => {
            this.dragState.dropZone.style.background = 'rgba(0, 123, 255, 0.1)';
        });
        
        this.dragState.dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            const url = e.dataTransfer.getData('text/uri-list') || e.dataTransfer.getData('text/plain');
            if (url && this.dragState.currentLink) {
                this.linkPreviewManager.createPreview(url, {
                    triggerElement: this.dragState.currentLink,
                    triggerEvent: e,
                    triggerType: 'drag'
                });
            }
            this.hideDropZone();
        });
        
        document.body.appendChild(this.dragState.dropZone);
    }

    /**
     * 显示拖拽区域
     */
    showDropZone() {
        if (this.dragState.dropZone) {
            this.dragState.dropZone.style.display = 'flex';
        }
    }

    /**
     * 隐藏拖拽区域
     */
    hideDropZone() {
        if (this.dragState.dropZone) {
            this.dragState.dropZone.style.display = 'none';
            this.dragState.dropZone.style.background = 'rgba(0, 123, 255, 0.1)';
        }
    }

    /**
     * 复制到剪贴板
     */
    async copyToClipboard(text) {
        const success = await LinkUtils.copyToClipboard(text);
        if (success) {
            LinkUtils.showToast('链接已复制到剪贴板', 2000, 'success');
        } else {
            LinkUtils.showToast('复制失败', 2000, 'error');
        }
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };

        // 重新绑定事件
        this.unbindAllEvents();
        this.bindTriggerEvents();

        console.log('🔄 触发处理器配置已更新:', this.config);
    }

    /**
     * 设置悬停延迟
     */
    setHoverDelay(delay) {
        this.hoverState.delay = Math.max(100, Math.min(delay, 5000)); // 限制在100ms-5s之间
        console.log(`🔄 悬停延迟已设置为: ${this.hoverState.delay}ms`);
    }

    /**
     * 设置长按持续时间
     */
    setLongPressDuration(duration) {
        this.longPressState.duration = Math.max(300, Math.min(duration, 3000)); // 限制在300ms-3s之间
        console.log(`🔄 长按持续时间已设置为: ${this.longPressState.duration}ms`);
    }

    /**
     * 启用/禁用特定触发方式
     */
    setTriggerEnabled(triggerType, enabled) {
        this.config[triggerType] = enabled;

        // 重新绑定事件
        this.unbindAllEvents();
        this.bindTriggerEvents();

        console.log(`🔄 触发方式 ${triggerType} 已${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 解绑所有事件
     */
    unbindAllEvents() {
        // 清除计时器
        this.clearHoverTimer();
        this.endLongPress();
        this.endDrag();

        // 隐藏菜单和区域
        this.hideContextMenu();
        this.hideDropZone();

        // 注意：这里简化处理，实际应该保存事件处理器引用以便正确解绑
        console.log('🧹 触发处理器事件已解绑');
    }

    /**
     * 获取触发统计信息
     */
    getStats() {
        return {
            enabledTriggers: Object.keys(this.config).filter(key => this.config[key]),
            hoverDelay: this.hoverState.delay,
            longPressDuration: this.longPressState.duration,
            isDragging: this.dragState.isDragging,
            hasContextMenu: !!this.contextMenu
        };
    }

    /**
     * 销毁触发处理器
     */
    destroy() {
        this.unbindAllEvents();

        // 移除DOM元素
        if (this.dragState.dropZone && this.dragState.dropZone.parentNode) {
            this.dragState.dropZone.parentNode.removeChild(this.dragState.dropZone);
        }

        console.log('💥 触发处理器已销毁');
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.TriggerHandlers = TriggerHandlers;
}
