// 全局快捷方式设置默认值配置
window.SHORTCUT_DEFAULTS = {
    iconSize: 60,
    iconSpacing: 16,
    containerWidth: 80,
    textColor: '#ffffff'
};

// 网格列数计算函数
window.calculateGridColumns = function(containerWidth, iconSize, iconSpacing) {
    const containerWidthPx = window.innerWidth * (containerWidth / 100);
    const totalIconWidth = iconSize + iconSpacing;
    return Math.max(1, Math.floor(containerWidthPx / totalIconWidth));
};

// 在页面加载前预设CSS变量，避免默认值不一致导致的视觉跳跃
(function() {
    // 从localStorage读取设置
    const settings = JSON.parse(localStorage.getItem('settings_v3') || '{}');
    const shortcuts = settings.shortcuts || {};

    // 使用全局默认值
    const iconSize = shortcuts.iconSize || window.SHORTCUT_DEFAULTS.iconSize;
    const iconSpacing = shortcuts.iconSpacing || window.SHORTCUT_DEFAULTS.iconSpacing;
    const containerWidth = shortcuts.containerWidth || window.SHORTCUT_DEFAULTS.containerWidth;
    const textColor = shortcuts.textColor || window.SHORTCUT_DEFAULTS.textColor;

    // 使用共享的计算函数
    const columns = window.calculateGridColumns(containerWidth, iconSize, iconSpacing);

    // 创建样式标签并设置CSS变量
    const style = document.createElement('style');
    style.textContent = `
        :root {
            --icon-size: ${iconSize}px;
            --icon-spacing: ${iconSpacing}px;
            --container-width: ${containerWidth}%;
            --text-color: ${textColor};
            --grid-columns: ${columns};
        }
    `;
    document.head.appendChild(style);
})();
