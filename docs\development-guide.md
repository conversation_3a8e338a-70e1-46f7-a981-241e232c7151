# Moment Search V3 - 开发指南

> 完整的开发环境搭建、编码规范和最佳实践指南

## 🚀 快速开始

### 环境要求

| 工具 | 版本要求 | 用途 |
|------|---------|------|
| Chrome | 88+ | 主要开发和测试浏览器 |
| VS Code | 最新版 | 推荐代码编辑器 |
| Git | 2.0+ | 版本控制 |
| Node.js | 16+ (可选) | 开发工具支持 |

### 开发环境搭建

#### 1. 克隆项目

```bash
# 克隆仓库
git clone https://github.com/your-repo/moment-search-v3.git
cd moment-search-v3

# 查看项目结构
tree moment-search-v3/
```

#### 2. VS Code配置

推荐安装以下扩展：

```json
{
    "recommendations": [
        "ms-vscode.vscode-json",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "ms-vscode.vscode-eslint",
        "ritwickdey.liveserver"
    ]
}
```

创建 `.vscode/settings.json`：

```json
{
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": true
    },
    "files.associations": {
        "*.js": "javascript"
    },
    "emmet.includeLanguages": {
        "javascript": "javascriptreact"
    }
}
```

#### 3. Chrome扩展加载

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `moment-search-v3` 文件夹

#### 4. 开发工具配置

```bash
# 可选：安装开发依赖
npm init -y
npm install --save-dev eslint prettier

# 创建ESLint配置
echo '{
  "env": {
    "browser": true,
    "es6": true,
    "webextensions": true
  },
  "extends": ["eslint:recommended"],
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "module"
  },
  "rules": {
    "no-console": "warn",
    "no-unused-vars": "warn"
  }
}' > .eslintrc.json
```

## 📋 项目结构详解

### 核心文件说明

```
moment-search-v3/
├── manifest.json         # 扩展配置文件
├── index.html           # 主页面入口
├── script.js            # 核心业务逻辑 (5000+ 行)
├── style.css            # 样式系统 (2800+ 行)
├── background.js        # 后台服务脚本
├── INSTALL.md          # 安装指南
└── js/                 # 辅助模块
    ├── DataStorageManager.js    # 数据存储管理
    └── SearchPlatformConfig.js  # 搜索平台配置
```

### 代码组织原则

1. **单一职责**: 每个文件负责特定功能
2. **模块化**: 使用ES6类和模块化设计
3. **可读性**: 清晰的命名和注释
4. **可维护性**: 避免深层嵌套和复杂逻辑

## 🔧 开发工作流

### 日常开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发和测试
# 修改代码 → 刷新扩展 → 测试功能

# 3. 代码检查
npm run lint  # 如果配置了ESLint

# 4. 提交代码
git add .
git commit -m "feat: 添加新功能"

# 5. 推送和创建PR
git push origin feature/new-feature
```

### 调试技巧

#### 1. Chrome DevTools调试

```javascript
// 在代码中添加断点
debugger;

// 使用console进行调试
console.log('调试信息:', data);
console.table(shortcuts); // 表格形式显示数组
console.time('performance'); // 性能测试开始
// ... 代码执行
console.timeEnd('performance'); // 性能测试结束
```

#### 2. 扩展调试

```javascript
// 在background.js中调试
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('收到消息:', message);
    // 调试逻辑
});

// 在content script中调试
window.addEventListener('error', (event) => {
    console.error('页面错误:', event.error);
});
```

#### 3. 性能调试

```javascript
// 性能监控
class PerformanceMonitor {
    static measure(name, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        console.log(`${name} 耗时: ${end - start}ms`);
        return result;
    }
}

// 使用示例
PerformanceMonitor.measure('搜索执行', () => {
    searchManager.executeSearch(query);
});
```

## 📝 编码规范

### JavaScript规范

#### 1. 命名规范

```javascript
// 类名：PascalCase
class SearchManager {}

// 函数和变量：camelCase
const searchQuery = 'example';
function executeSearch() {}

// 常量：UPPER_SNAKE_CASE
const MAX_SHORTCUTS = 60;
const API_ENDPOINTS = {};

// 私有方法：下划线前缀
class MyClass {
    _privateMethod() {}
    publicMethod() {}
}
```

#### 2. 函数规范

```javascript
// 函数应该简短且职责单一
function addShortcut(name, url, icon) {
    // 参数验证
    if (!name || !url) {
        throw new Error('名称和URL是必需的');
    }
    
    // 业务逻辑
    const shortcut = {
        id: Utils.generateId(),
        name,
        url,
        icon: icon || '🔗',
        createdAt: Date.now()
    };
    
    // 返回结果
    return shortcut;
}

// 异步函数使用async/await
async function loadShortcuts() {
    try {
        const data = await Storage.get('shortcuts', []);
        return data;
    } catch (error) {
        console.error('加载快捷方式失败:', error);
        return [];
    }
}
```

#### 3. 类设计规范

```javascript
class ShortcutManager {
    constructor() {
        // 初始化属性
        this.shortcuts = [];
        this.maxShortcuts = 60;
        this.container = null;
        
        // 绑定方法上下文
        this.handleClick = this.handleClick.bind(this);
        
        // 初始化
        this.init();
    }
    
    // 公共方法
    async addShortcut(shortcutData) {
        // 实现逻辑
    }
    
    // 私有方法
    _validateShortcut(shortcut) {
        // 验证逻辑
    }
    
    // 事件处理方法
    handleClick(event) {
        // 事件处理逻辑
    }
}
```

### CSS规范

#### 1. 选择器规范

```css
/* 使用CSS变量 */
:root {
    --primary-color: #667eea;
    --spacing-md: 16px;
}

/* BEM命名规范 */
.shortcut-item {}
.shortcut-item__icon {}
.shortcut-item__name {}
.shortcut-item--active {}

/* 避免深层嵌套 */
.shortcuts-grid .shortcut-item .icon {
    /* 不推荐：嵌套过深 */
}

.shortcut-icon {
    /* 推荐：扁平化选择器 */
}
```

#### 2. 属性组织

```css
.shortcut-item {
    /* 定位 */
    position: relative;
    top: 0;
    left: 0;
    
    /* 盒模型 */
    display: flex;
    width: 60px;
    height: 60px;
    padding: var(--spacing-sm);
    margin: var(--spacing-xs);
    
    /* 视觉效果 */
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    
    /* 动画 */
    transition: all var(--duration-normal) ease;
}
```

### HTML规范

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moment Search</title>
    <!-- 样式文件 -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 语义化标签 -->
    <main class="main-content">
        <section class="search-section">
            <h2 class="visually-hidden">搜索功能</h2>
            <!-- 搜索组件 -->
        </section>
        
        <section class="shortcuts-section">
            <h2 class="visually-hidden">快捷方式</h2>
            <!-- 快捷方式网格 -->
        </section>
    </main>
    
    <!-- 脚本文件放在body底部 -->
    <script src="script.js"></script>
</body>
</html>
```

## 🧪 测试策略

### 手动测试清单

#### 功能测试

```javascript
// 测试脚本模板
const TestSuite = {
    async runAllTests() {
        console.log('🧪 开始运行测试套件...');
        
        await this.testSearchFunction();
        await this.testShortcutManagement();
        await this.testSettingsPanel();
        await this.testDataPersistence();
        
        console.log('✅ 所有测试完成');
    },
    
    async testSearchFunction() {
        console.log('测试搜索功能...');
        
        // 测试别名匹配
        const result = searchManager.matchAlias('g JavaScript');
        console.assert(result.platform === 'google', '别名匹配正常');
        
        // 测试搜索执行
        await searchManager.executeSearch('test query', 'google');
        console.log('✓ 搜索功能正常');
    },
    
    async testShortcutManagement() {
        console.log('测试快捷方式管理...');
        
        // 测试添加快捷方式
        const shortcut = await shortcutManager.addShortcut({
            name: 'Test Site',
            url: 'https://example.com'
        });
        console.assert(shortcut.id, '快捷方式创建成功');
        
        // 测试删除快捷方式
        await shortcutManager.removeShortcut(shortcut.id);
        console.log('✓ 快捷方式管理正常');
    }
};

// 运行测试
TestSuite.runAllTests();
```

#### 性能测试

```javascript
// 性能测试工具
class PerformanceTester {
    static async testLoadTime() {
        const start = performance.now();
        
        // 模拟应用启动
        await new Promise(resolve => {
            window.addEventListener('load', resolve);
        });
        
        const loadTime = performance.now() - start;
        console.log(`页面加载时间: ${loadTime}ms`);
        
        // 断言加载时间小于300ms
        console.assert(loadTime < 300, '加载时间符合要求');
    }
    
    static testMemoryUsage() {
        if (performance.memory) {
            const memory = performance.memory;
            console.log('内存使用情况:', {
                used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB'
            });
        }
    }
}
```

### 自动化测试 (可选)

```javascript
// 简单的单元测试框架
class SimpleTest {
    static tests = [];
    
    static test(name, fn) {
        this.tests.push({ name, fn });
    }
    
    static async run() {
        let passed = 0;
        let failed = 0;
        
        for (const test of this.tests) {
            try {
                await test.fn();
                console.log(`✓ ${test.name}`);
                passed++;
            } catch (error) {
                console.error(`✗ ${test.name}:`, error.message);
                failed++;
            }
        }
        
        console.log(`测试结果: ${passed} 通过, ${failed} 失败`);
    }
}

// 使用示例
SimpleTest.test('Utils.generateId 应该生成唯一ID', () => {
    const id1 = Utils.generateId();
    const id2 = Utils.generateId();
    if (id1 === id2) {
        throw new Error('ID不唯一');
    }
});

SimpleTest.test('Storage.set/get 应该正常工作', () => {
    Storage.set('test-key', 'test-value');
    const value = Storage.get('test-key');
    if (value !== 'test-value') {
        throw new Error('存储功能异常');
    }
});

// 运行测试
SimpleTest.run();
```

## 🔍 调试和故障排除

### 常见问题解决

#### 1. 扩展无法加载

```javascript
// 检查manifest.json格式
try {
    const manifest = JSON.parse(manifestContent);
    console.log('Manifest格式正确');
} catch (error) {
    console.error('Manifest格式错误:', error);
}

// 检查文件路径
const requiredFiles = ['index.html', 'script.js', 'style.css', 'background.js'];
requiredFiles.forEach(file => {
    fetch(file)
        .then(() => console.log(`✓ ${file} 存在`))
        .catch(() => console.error(`✗ ${file} 缺失`));
});
```

#### 2. 功能异常调试

```javascript
// 全局错误捕获
window.addEventListener('error', (event) => {
    console.error('全局错误:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
    });
});

// Promise错误捕获
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise错误:', event.reason);
});
```

#### 3. 性能问题诊断

```javascript
// 性能监控
class PerformanceMonitor {
    static startMonitoring() {
        // 监控长任务
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (entry.duration > 50) {
                        console.warn('长任务检测:', entry);
                    }
                });
            });
            observer.observe({ entryTypes: ['longtask'] });
        }
        
        // 监控内存使用
        setInterval(() => {
            if (performance.memory) {
                const used = performance.memory.usedJSHeapSize / 1024 / 1024;
                if (used > 50) {
                    console.warn('内存使用过高:', used + 'MB');
                }
            }
        }, 30000);
    }
}

PerformanceMonitor.startMonitoring();
```

## 📚 最佳实践

### 1. 代码组织

- 使用模块化设计，避免全局变量污染
- 保持函数简短，单一职责
- 使用有意义的命名
- 添加适当的注释和文档

### 2. 性能优化

- 使用事件委托减少事件监听器
- 避免频繁的DOM操作
- 使用requestAnimationFrame进行动画
- 实现适当的缓存机制

### 3. 错误处理

- 使用try-catch包装可能出错的代码
- 提供用户友好的错误信息
- 记录详细的错误日志
- 实现优雅的降级处理

### 4. 安全考虑

- 验证所有用户输入
- 使用CSP防止XSS攻击
- 最小化权限申请
- 定期更新依赖

---

**文档版本**: V1.0  
**创建时间**: 2025-01-22  
**维护团队**: Moment Search Development Team
