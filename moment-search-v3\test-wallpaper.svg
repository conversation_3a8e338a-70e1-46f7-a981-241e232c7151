<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad1)" />
  <circle cx="200" cy="150" r="80" fill="rgba(255,255,255,0.3)" />
  <circle cx="600" cy="450" r="120" fill="rgba(255,255,255,0.2)" />
  <text x="400" y="300" font-family="Arial, sans-serif" font-size="48" fill="white" text-anchor="middle">测试壁纸</text>
  <text x="400" y="350" font-family="Arial, sans-serif" font-size="24" fill="rgba(255,255,255,0.8)" text-anchor="middle">Wallpaper Editor Test</text>
</svg>
