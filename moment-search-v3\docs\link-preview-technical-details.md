# 链接预览功能技术实现细节

## 第一阶段详细实现指南

### 1. LinkPreviewManager 主控制器实现

```javascript
class LinkPreviewManager {
    constructor(options = {}) {
        this.config = this.mergeConfig(DEFAULT_LINK_PREVIEW_CONFIG, options);
        this.previewWindows = new Map();
        this.eventManager = new LinkPreviewEventManager(this);
        this.windowManager = new PreviewWindowManager(this);
        this.isEnabled = this.config.enabled;
        this.init();
    }

    init() {
        if (!this.isEnabled) return;
        
        // 绑定全局事件
        this.eventManager.bindGlobalEvents();
        
        // 注册到全局应用
        if (window.app) {
            window.app.linkPreviewManager = this;
        }
        
        console.log('✅ 链接预览管理器初始化完成');
    }

    async createPreview(url, options = {}) {
        try {
            // 检查窗口数量限制
            if (this.previewWindows.size >= this.config.window.maxWindows) {
                this.closeOldestWindow();
            }

            // 创建预览窗口
            const windowId = Utils.generateId();
            const previewWindow = new PreviewWindow(url, {
                id: windowId,
                ...this.config.window,
                ...options
            });

            // 渲染窗口
            await previewWindow.render();
            
            // 注册窗口
            this.previewWindows.set(windowId, previewWindow);
            
            // 绑定窗口事件
            this.bindWindowEvents(previewWindow);
            
            return previewWindow;
        } catch (error) {
            console.error('创建预览窗口失败:', error);
            throw error;
        }
    }

    destroyPreview(windowId) {
        const window = this.previewWindows.get(windowId);
        if (window) {
            window.destroy();
            this.previewWindows.delete(windowId);
        }
    }

    handleLinkClick(event) {
        // 检查是否按下修饰键
        const modifierPressed = this.eventManager.isModifierPressed(event);
        if (!modifierPressed) return;

        // 查找链接元素
        const link = event.target.closest('a[href]');
        if (!link) return;

        // 阻止默认行为
        event.preventDefault();
        event.stopPropagation();

        // 获取链接URL
        const url = link.href;
        if (!url || url.startsWith('javascript:')) return;

        // 创建预览
        this.createPreview(url, {
            triggerElement: link,
            triggerEvent: event
        });
    }

    closeOldestWindow() {
        const oldestWindow = Array.from(this.previewWindows.values())[0];
        if (oldestWindow) {
            this.destroyPreview(oldestWindow.id);
        }
    }

    bindWindowEvents(previewWindow) {
        // 窗口关闭事件
        previewWindow.on('close', () => {
            this.destroyPreview(previewWindow.id);
        });

        // 窗口焦点事件
        previewWindow.on('focus', () => {
            this.bringToFront(previewWindow.id);
        });
    }

    bringToFront(windowId) {
        const window = this.previewWindows.get(windowId);
        if (window) {
            window.bringToFront();
        }
    }

    mergeConfig(defaultConfig, userConfig) {
        return Utils.deepMerge(defaultConfig, userConfig);
    }

    // 配置更新方法
    updateConfig(newConfig) {
        this.config = this.mergeConfig(this.config, newConfig);
        this.isEnabled = this.config.enabled;
        
        if (!this.isEnabled) {
            this.destroyAllPreviews();
        }
    }

    destroyAllPreviews() {
        this.previewWindows.forEach((window, id) => {
            this.destroyPreview(id);
        });
    }
}
```

### 2. PreviewWindow 组件实现

```javascript
class PreviewWindow {
    constructor(url, options = {}) {
        this.url = url;
        this.id = options.id || Utils.generateId();
        this.options = options;
        this.element = null;
        this.iframe = null;
        this.state = 'initializing';
        this.eventListeners = new Map();
        this.zIndex = options.zIndex || 1000;
    }

    async render() {
        try {
            this.state = 'rendering';
            
            // 创建窗口容器
            this.createElement();
            
            // 设置样式和位置
            this.applyStyles();
            this.calculatePosition();
            
            // 添加到DOM
            document.body.appendChild(this.element);
            
            // 创建iframe并加载内容
            await this.loadContent();
            
            // 绑定事件
            this.bindEvents();
            
            // 显示动画
            this.showWithAnimation();
            
            this.state = 'rendered';
            this.emit('rendered');
            
        } catch (error) {
            this.state = 'error';
            this.emit('error', error);
            throw error;
        }
    }

    createElement() {
        this.element = document.createElement('div');
        this.element.className = 'link-preview-window';
        this.element.dataset.windowId = this.id;
        
        this.element.innerHTML = `
            <div class="preview-header">
                <div class="preview-title">
                    <span class="preview-url">${this.getDisplayUrl()}</span>
                </div>
                <div class="preview-controls">
                    <button class="preview-btn reading-mode-btn" title="阅读模式">
                        📖
                    </button>
                    <button class="preview-btn minimize-btn" title="最小化">
                        ➖
                    </button>
                    <button class="preview-btn close-btn" title="关闭">
                        ✕
                    </button>
                </div>
            </div>
            <div class="preview-content">
                <div class="preview-loading">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在加载...</div>
                </div>
                <iframe class="preview-iframe" sandbox="allow-scripts allow-same-origin allow-forms"></iframe>
            </div>
            <div class="preview-resize-handle"></div>
        `;

        // 获取关键元素引用
        this.iframe = this.element.querySelector('.preview-iframe');
        this.loadingElement = this.element.querySelector('.preview-loading');
        this.headerElement = this.element.querySelector('.preview-header');
        this.resizeHandle = this.element.querySelector('.preview-resize-handle');
    }

    applyStyles() {
        const size = this.options.sizes[this.options.defaultSize] || this.options.sizes.medium;
        
        Object.assign(this.element.style, {
            position: 'fixed',
            width: `${size.width}px`,
            height: `${size.height}px`,
            zIndex: this.zIndex,
            backgroundColor: 'var(--bg-color, #ffffff)',
            border: '1px solid var(--border-color, #e0e0e0)',
            borderRadius: `${this.options.borderRadius || 8}px`,
            boxShadow: this.options.shadow ? '0 8px 32px rgba(0,0,0,0.12)' : 'none',
            overflow: 'hidden',
            opacity: '0',
            transform: 'scale(0.9)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        });
    }

    calculatePosition() {
        const position = this.options.defaultPosition;
        const rect = this.element.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        let left, top;
        
        switch (position) {
            case 'left':
                left = 50;
                top = (viewportHeight - rect.height) / 2;
                break;
            case 'right':
                left = viewportWidth - rect.width - 50;
                top = (viewportHeight - rect.height) / 2;
                break;
            case 'mouse':
                if (this.options.triggerEvent) {
                    left = this.options.triggerEvent.clientX + 10;
                    top = this.options.triggerEvent.clientY + 10;
                } else {
                    left = (viewportWidth - rect.width) / 2;
                    top = (viewportHeight - rect.height) / 2;
                }
                break;
            default: // center
                left = (viewportWidth - rect.width) / 2;
                top = (viewportHeight - rect.height) / 2;
        }
        
        // 确保窗口在视口内
        left = Math.max(10, Math.min(left, viewportWidth - rect.width - 10));
        top = Math.max(10, Math.min(top, viewportHeight - rect.height - 10));
        
        this.element.style.left = `${left}px`;
        this.element.style.top = `${top}px`;
    }

    async loadContent() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('加载超时'));
            }, 10000);

            this.iframe.onload = () => {
                clearTimeout(timeout);
                this.hideLoading();
                this.state = 'loaded';
                this.emit('loaded');
                resolve();
            };

            this.iframe.onerror = () => {
                clearTimeout(timeout);
                this.showError('加载失败');
                reject(new Error('iframe加载失败'));
            };

            // 设置iframe源
            this.iframe.src = this.url;
        });
    }

    bindEvents() {
        // 关闭按钮
        const closeBtn = this.element.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => this.close());

        // 最小化按钮
        const minimizeBtn = this.element.querySelector('.minimize-btn');
        minimizeBtn.addEventListener('click', () => this.minimize());

        // 阅读模式按钮
        const readingModeBtn = this.element.querySelector('.reading-mode-btn');
        readingModeBtn.addEventListener('click', () => this.toggleReadingMode());

        // 拖拽功能
        this.bindDragEvents();

        // 调整大小功能
        this.bindResizeEvents();

        // 键盘事件
        this.bindKeyboardEvents();
    }

    bindDragEvents() {
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        this.headerElement.addEventListener('mousedown', (e) => {
            if (e.target.closest('.preview-controls')) return;
            
            isDragging = true;
            const rect = this.element.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            
            this.element.style.cursor = 'grabbing';
            this.headerElement.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const left = e.clientX - dragOffset.x;
            const top = e.clientY - dragOffset.y;
            
            this.element.style.left = `${left}px`;
            this.element.style.top = `${top}px`;
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                this.element.style.cursor = '';
                this.headerElement.style.cursor = '';
            }
        });
    }

    bindResizeEvents() {
        let isResizing = false;
        let startSize = { width: 0, height: 0 };
        let startMouse = { x: 0, y: 0 };

        this.resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            const rect = this.element.getBoundingClientRect();
            startSize.width = rect.width;
            startSize.height = rect.height;
            startMouse.x = e.clientX;
            startMouse.y = e.clientY;
            
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isResizing) return;
            
            const deltaX = e.clientX - startMouse.x;
            const deltaY = e.clientY - startMouse.y;
            
            const newWidth = Math.max(300, startSize.width + deltaX);
            const newHeight = Math.max(200, startSize.height + deltaY);
            
            this.element.style.width = `${newWidth}px`;
            this.element.style.height = `${newHeight}px`;
        });

        document.addEventListener('mouseup', () => {
            isResizing = false;
        });
    }

    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            if (!this.isFocused()) return;
            
            switch (e.key) {
                case 'Escape':
                    this.close();
                    break;
                case 'F11':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
            }
        });
    }

    showWithAnimation() {
        requestAnimationFrame(() => {
            this.element.style.opacity = '1';
            this.element.style.transform = 'scale(1)';
        });
    }

    hideLoading() {
        if (this.loadingElement) {
            this.loadingElement.style.display = 'none';
        }
    }

    showError(message) {
        this.element.querySelector('.preview-content').innerHTML = `
            <div class="preview-error">
                <div class="error-icon">⚠️</div>
                <div class="error-message">${message}</div>
                <button class="retry-btn">重试</button>
            </div>
        `;
    }

    close() {
        this.element.style.opacity = '0';
        this.element.style.transform = 'scale(0.9)';
        
        setTimeout(() => {
            this.destroy();
        }, 300);
        
        this.emit('close');
    }

    destroy() {
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        // 清理事件监听器
        this.eventListeners.clear();
        
        this.emit('destroyed');
    }

    minimize() {
        // 实现最小化逻辑
        this.element.style.transform = 'scale(0.1)';
        this.element.style.opacity = '0.5';
    }

    toggleReadingMode() {
        // 实现阅读模式切换
        this.emit('toggleReadingMode');
    }

    toggleFullscreen() {
        // 实现全屏切换
        this.element.classList.toggle('fullscreen');
    }

    bringToFront() {
        this.zIndex = Math.max(this.zIndex, 1000) + 1;
        this.element.style.zIndex = this.zIndex;
    }

    isFocused() {
        return document.activeElement === this.element || 
               this.element.contains(document.activeElement);
    }

    getDisplayUrl() {
        try {
            const url = new URL(this.url);
            return url.hostname + url.pathname;
        } catch {
            return this.url;
        }
    }

    // 事件系统
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => callback(data));
        }
    }
}
```

### 3. LinkPreviewEventManager 实现

```javascript
class LinkPreviewEventManager {
    constructor(manager) {
        this.manager = manager;
        this.keyboardState = {
            alt: false,
            ctrl: false,
            shift: false
        };
        this.boundHandlers = new Map();
    }

    bindGlobalEvents() {
        // 键盘状态跟踪
        this.bindKeyboardEvents();
        
        // 链接点击事件
        this.bindLinkClickEvents();
        
        // 全局快捷键
        this.bindGlobalShortcuts();
    }

    bindKeyboardEvents() {
        const keydownHandler = (e) => {
            this.keyboardState.alt = e.altKey;
            this.keyboardState.ctrl = e.ctrlKey;
            this.keyboardState.shift = e.shiftKey;
        };

        const keyupHandler = (e) => {
            this.keyboardState.alt = e.altKey;
            this.keyboardState.ctrl = e.ctrlKey;
            this.keyboardState.shift = e.shiftKey;
        };

        document.addEventListener('keydown', keydownHandler);
        document.addEventListener('keyup', keyupHandler);
        
        this.boundHandlers.set('keydown', keydownHandler);
        this.boundHandlers.set('keyup', keyupHandler);
    }

    bindLinkClickEvents() {
        const clickHandler = (e) => {
            this.manager.handleLinkClick(e);
        };

        document.addEventListener('click', clickHandler, true);
        this.boundHandlers.set('click', clickHandler);
    }

    bindGlobalShortcuts() {
        const shortcutHandler = (e) => {
            // ESC 关闭所有预览窗口
            if (e.key === 'Escape') {
                this.manager.destroyAllPreviews();
            }
        };

        document.addEventListener('keydown', shortcutHandler);
        this.boundHandlers.set('shortcuts', shortcutHandler);
    }

    isModifierPressed(event) {
        const modifier = this.manager.config.shortcuts.modifier;
        
        switch (modifier) {
            case 'alt':
                return event.altKey;
            case 'ctrl':
                return event.ctrlKey || event.metaKey;
            case 'shift':
                return event.shiftKey;
            default:
                return event.altKey; // 默认使用 Alt
        }
    }

    unbindAllEvents() {
        this.boundHandlers.forEach((handler, event) => {
            document.removeEventListener(event, handler, true);
        });
        this.boundHandlers.clear();
    }
}
```

### 4. CSS 样式实现

```css
/* 链接预览窗口基础样式 */
.link-preview-window {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    flex-direction: column;
    background: var(--preview-bg, #ffffff);
    border: 1px solid var(--preview-border, #e0e0e0);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    backdrop-filter: blur(10px);
    user-select: none;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .link-preview-window {
        --preview-bg: #2d2d2d;
        --preview-border: #404040;
        --preview-text: #ffffff;
        --preview-text-secondary: #cccccc;
    }
}

/* 预览窗口头部 */
.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--preview-header-bg, #f8f9fa);
    border-bottom: 1px solid var(--preview-border, #e0e0e0);
    cursor: move;
    min-height: 40px;
}

.preview-title {
    flex: 1;
    min-width: 0;
}

.preview-url {
    font-size: 12px;
    color: var(--preview-text-secondary, #666);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.preview-controls {
    display: flex;
    gap: 4px;
}

.preview-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: background-color 0.2s;
}

.preview-btn:hover {
    background: var(--preview-btn-hover, rgba(0, 0, 0, 0.1));
}

.close-btn:hover {
    background: #ff5f56;
    color: white;
}

/* 预览内容区域 */
.preview-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.preview-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

/* 加载状态 */
.preview-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--preview-bg, #ffffff);
    z-index: 10;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--preview-border, #e0e0e0);
    border-top: 3px solid var(--primary-color, #1976d2);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 12px;
    font-size: 14px;
    color: var(--preview-text-secondary, #666);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 */
.preview-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px;
    text-align: center;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.error-message {
    font-size: 14px;
    color: var(--preview-text-secondary, #666);
    margin-bottom: 16px;
}

.retry-btn {
    padding: 8px 16px;
    background: var(--primary-color, #1976d2);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

/* 调整大小手柄 */
.preview-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 16px;
    height: 16px;
    cursor: nw-resize;
    background: linear-gradient(-45deg, transparent 30%, var(--preview-border, #e0e0e0) 30%, var(--preview-border, #e0e0e0) 70%, transparent 70%);
}

/* 全屏模式 */
.link-preview-window.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    border-radius: 0 !important;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .link-preview-window {
        width: calc(100vw - 20px) !important;
        height: calc(100vh - 20px) !important;
        top: 10px !important;
        left: 10px !important;
    }
}
```

## 集成到现有项目的具体步骤

### 1. 修改 SettingsManager 类

在 `generateNavigationHTML()` 方法中添加高级功能导航项：

```javascript
// 在 navItems 数组中添加
{ section: 'advanced', label: '高级功能' }
```

在 `generateMainContentHTML()` 方法中添加高级功能内容：

```javascript
${this.generateAdvancedSection()}
```

添加 `generateAdvancedSection()` 方法：

```javascript
generateAdvancedSection() {
    const linkPreviewEnabled = this.settings.advanced?.linkPreview?.enabled !== false;

    return `
        <div class="settings-section" data-section="advanced">
            <div class="setting-group">
                <h4>链接预览</h4>
                ${this.generateSettingItem('启用链接预览', this.generateSwitch('advanced.linkPreview.enabled', linkPreviewEnabled))}
                ${this.generateSettingItem('触发快捷键', this.generateTriggerKeySelector())}
                ${this.generateSettingItem('预览窗口大小', this.generateWindowSizeSelector())}
                ${this.generateSettingItem('预览窗口位置', this.generateWindowPositionSelector())}
                ${this.generateSettingItem('最大窗口数量', this.generateSlider('advanced.linkPreview.maxWindows', 1, 10, 5, '个'))}
            </div>
        </div>
    `;
}

generateTriggerKeySelector() {
    const currentKey = this.settings.advanced?.linkPreview?.shortcuts?.modifier || 'alt';
    const options = [
        { value: 'alt', label: 'Alt 键' },
        { value: 'ctrl', label: 'Ctrl 键' },
        { value: 'shift', label: 'Shift 键' }
    ];
    return this.generateSelect('advanced.linkPreview.shortcuts.modifier', options, currentKey);
}

generateWindowSizeSelector() {
    const currentSize = this.settings.advanced?.linkPreview?.window?.defaultSize || 'medium';
    const options = [
        { value: 'small', label: '小 (400×300)' },
        { value: 'medium', label: '中 (800×600)' },
        { value: 'large', label: '大 (1200×800)' }
    ];
    return this.generateSelect('advanced.linkPreview.window.defaultSize', options, currentSize);
}

generateWindowPositionSelector() {
    const currentPosition = this.settings.advanced?.linkPreview?.window?.defaultPosition || 'center';
    const options = [
        { value: 'center', label: '居中' },
        { value: 'left', label: '左侧' },
        { value: 'right', label: '右侧' },
        { value: 'mouse', label: '跟随鼠标' }
    ];
    return this.generateSelect('advanced.linkPreview.window.defaultPosition', options, currentPosition);
}
```

### 2. 修改主 script.js 文件

在文件末尾的应用初始化部分添加：

```javascript
// 在 window.MomentSearch 对象中添加链接预览相关工具
window.MomentSearch = {
    Utils,
    Storage,
    URLParser,
    PlatformManager,
    LinkPreviewManager: window.LinkPreviewManager || null
};

// 在应用初始化完成后初始化链接预览
document.addEventListener('DOMContentLoaded', () => {
    // 等待主应用初始化完成
    setTimeout(() => {
        if (window.LinkPreviewManager && window.app?.settingsManager) {
            const settings = Storage.get('settings_v3', {});
            const linkPreviewConfig = settings.advanced?.linkPreview || {};

            if (linkPreviewConfig.enabled !== false) {
                window.app.linkPreviewManager = new LinkPreviewManager({
                    hostEnvironment: 'moment-search',
                    ...linkPreviewConfig
                });
                console.log('✅ 链接预览功能已启用');
            }
        }
    }, 1000);
});
```

### 3. 创建模块文件结构

创建以下文件结构：

```
moment-search-v3/
├── modules/
│   └── link-preview/
│       ├── link-preview-manager.js
│       ├── preview-window.js
│       ├── event-manager.js
│       └── link-preview.css
```

### 4. 在 HTML 中引入模块

在主 HTML 文件的 `<head>` 部分添加：

```html
<!-- 链接预览功能样式 -->
<link rel="stylesheet" href="modules/link-preview/link-preview.css">

<!-- 链接预览功能脚本 -->
<script src="modules/link-preview/event-manager.js"></script>
<script src="modules/link-preview/preview-window.js"></script>
<script src="modules/link-preview/link-preview-manager.js"></script>
```

### 5. 默认配置集成

在 `getDefaultSettings()` 方法中添加高级功能的默认配置：

```javascript
getDefaultSettings() {
    return {
        // ... 现有配置
        advanced: {
            linkPreview: {
                enabled: true,
                triggers: {
                    altClick: true,
                    rightClickMenu: false,
                    hoverDelay: false,
                    longPress: false,
                    dragLink: false
                },
                shortcuts: {
                    modifier: 'alt',
                    closeKey: 'Escape'
                },
                window: {
                    defaultSize: 'medium',
                    defaultPosition: 'center',
                    maxWindows: 5,
                    resizable: true,
                    draggable: true
                }
            }
        }
    };
}
```

## 开发测试指南

### 1. 功能测试清单

**基础功能测试：**
- [ ] Alt+点击链接能正确弹出预览窗口
- [ ] 预览窗口能正确加载目标页面内容
- [ ] ESC 键能关闭预览窗口
- [ ] 点击关闭按钮能关闭窗口
- [ ] 窗口支持拖拽移动
- [ ] 窗口支持调整大小

**配置功能测试：**
- [ ] 设置页面能正确显示高级功能选项
- [ ] 开关功能能正确启用/禁用链接预览
- [ ] 快捷键设置能正确生效
- [ ] 窗口大小设置能正确应用
- [ ] 窗口位置设置能正确应用

**兼容性测试：**
- [ ] 与现有功能无冲突
- [ ] 不影响原有的链接点击行为
- [ ] 样式与现有主题兼容
- [ ] 在不同屏幕尺寸下正常工作

### 2. 性能测试

- [ ] 快速连续点击链接不会创建过多窗口
- [ ] 内存使用合理，无明显泄漏
- [ ] 动画流畅，无卡顿现象
- [ ] 大量链接页面下性能稳定

### 3. 错误处理测试

- [ ] 无效链接的错误处理
- [ ] 跨域限制的处理
- [ ] 网络错误的处理
- [ ] 加载超时的处理

这个技术实现细节文档提供了第一阶段开发所需的完整代码框架和集成指南。每个步骤都有详细的实现指导，确保功能能够顺利集成到现有项目中。
