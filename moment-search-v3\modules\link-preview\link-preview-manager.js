/**
 * 链接预览主管理器
 * 负责协调所有预览窗口和功能模块
 */
class LinkPreviewManager {
    constructor(options = {}) {
        // 配置管理
        this.config = LinkUtils.deepMerge(this.getDefaultConfig(), options);
        this.hostEnvironment = options.hostEnvironment || 'standalone';
        
        // 核心组件
        this.previewWindows = new Map();
        this.eventManager = new LinkPreviewEventManager(this);
        this.windowManager = new PreviewWindowManager(this);
        this.triggerHandlers = new TriggerHandlers(this);

        // 高级功能组件
        this.textOperationsHandler = null;
        this.advancedConfigDialog = null;
        
        // 状态管理
        this.isEnabled = this.config.enabled;
        this.maxWindows = this.config.window?.maxWindows || 5;
        this.focusedWindowId = null;
        
        // 统计信息
        this.stats = {
            totalCreated: 0,
            totalDestroyed: 0,
            currentCount: 0
        };
        
        this.init();
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            enabled: true,
            triggers: {
                altClick: true,
                rightClickMenu: false,
                hoverDelay: false,
                longPress: false,
                dragLink: false
            },
            shortcuts: {
                modifier: 'alt',
                closeKey: 'Escape',
                nextWindow: 'Tab',
                prevWindow: 'Shift+Tab'
            },
            window: {
                defaultSize: 'medium',
                defaultPosition: 'center',
                maxWindows: 5,
                resizable: true,
                draggable: true,
                sizes: {
                    small: { width: 400, height: 300 },
                    medium: { width: 800, height: 600 },
                    large: { width: 1200, height: 800 }
                }
            },
            readingMode: {
                enabled: true,
                autoDetect: true,
                removeAds: true,
                cleanLayout: true
            },
            textActions: {
                search: {
                    enabled: false, // 第三阶段实现
                    engines: ['bing', 'google', 'baidu'],
                    defaultEngine: 'bing'
                },
                translation: {
                    enabled: false, // 第三阶段实现
                    services: ['google', 'bing', 'baidu'],
                    defaultService: 'google',
                    targetLanguage: 'zh-CN'
                }
            },
            styling: {
                theme: 'auto',
                borderRadius: 8,
                shadow: true,
                backdrop: false,
                animation: true
            }
        };
    }

    /**
     * 初始化管理器
     */
    init() {
        if (!this.isEnabled) {
            console.log('⏸️ 链接预览功能已禁用');
            return;
        }

        try {
            // 绑定全局事件
            this.eventManager.bindGlobalEvents();
            
            // 注册到全局应用
            this.registerToGlobalApp();
            
            // 监听配置变化
            this.bindConfigEvents();

            // 初始化高级功能
            this.initializeAdvancedFeatures();

            console.log('✅ 链接预览管理器初始化完成');
            console.log('📊 当前配置:', this.config);
            
        } catch (error) {
            console.error('❌ 链接预览管理器初始化失败:', error);
        }
    }

    /**
     * 注册到全局应用
     */
    registerToGlobalApp() {
        if (typeof window !== 'undefined') {
            if (!window.app) {
                window.app = {};
            }
            window.app.linkPreviewManager = this;
        }
    }

    /**
     * 绑定配置事件
     */
    bindConfigEvents() {
        // 监听设置变化（如果在 moment-search 环境中）
        if (this.hostEnvironment === 'moment-search') {
            document.addEventListener('settingsChanged', (e) => {
                if (e.detail?.advanced?.linkPreview) {
                    this.updateConfig(e.detail.advanced.linkPreview);
                }
            });
        }
    }

    /**
     * 创建预览窗口
     */
    async createPreview(url, options = {}) {
        try {
            // 检查是否启用
            if (!this.isEnabled) {
                console.log('⏸️ 链接预览功能已禁用，跳过创建');
                return null;
            }

            // 检查URL有效性
            if (!LinkUtils.isValidUrl(url)) {
                console.warn('⚠️ 无效的URL:', url);
                return null;
            }

            // 使用窗口管理器创建窗口
            const previewWindow = await this.windowManager.createWindow(url, options);

            // 同步到主管理器的窗口映射
            if (previewWindow) {
                this.previewWindows.set(previewWindow.id, previewWindow);
                this.focusedWindowId = previewWindow.id;

                // 更新统计
                this.stats.totalCreated++;
                this.stats.currentCount = this.previewWindows.size;

                console.log(`✅ 预览窗口已创建: ${url} (${previewWindow.id})`);
                console.log(`📊 当前窗口数: ${this.stats.currentCount}/${this.maxWindows}`);
            }

            return previewWindow;

        } catch (error) {
            console.error('❌ 创建预览窗口失败:', error);
            throw error;
        }
    }

    /**
     * 准备窗口选项
     */
    prepareWindowOptions(options) {
        const sizeConfig = this.config.window.sizes[this.config.window.defaultSize];
        
        return {
            width: sizeConfig.width,
            height: sizeConfig.height,
            position: this.config.window.defaultPosition,
            resizable: this.config.window.resizable,
            draggable: this.config.window.draggable,
            zIndex: 10000 + this.previewWindows.size,
            ...options
        };
    }

    /**
     * 绑定窗口事件
     */
    bindWindowEvents(previewWindow) {
        // 窗口关闭事件
        previewWindow.on('close', () => {
            this.destroyPreview(previewWindow.id);
        });

        // 窗口焦点事件
        previewWindow.on('focus', () => {
            this.focusedWindowId = previewWindow.id;
        });

        // 窗口失焦事件
        previewWindow.on('blur', () => {
            if (this.focusedWindowId === previewWindow.id) {
                this.focusedWindowId = null;
            }
        });

        // 窗口销毁事件
        previewWindow.on('destroyed', () => {
            this.stats.totalDestroyed++;
            this.stats.currentCount = this.previewWindows.size;
        });

        // 阅读模式切换事件
        previewWindow.on('toggleReadingMode', () => {
            // 第三阶段实现
            console.log('阅读模式功能将在第三阶段实现');
        });
    }

    /**
     * 销毁预览窗口
     */
    destroyPreview(windowId) {
        // 使用窗口管理器销毁窗口
        this.windowManager.destroyWindow(windowId);

        // 同步主管理器的窗口映射
        this.previewWindows.delete(windowId);

        // 更新焦点窗口
        if (this.focusedWindowId === windowId) {
            this.focusedWindowId = this.windowManager.focusedWindowId;
        }

        console.log(`🗑️ 预览窗口已销毁: ${windowId}`);
    }

    /**
     * 销毁所有预览窗口
     */
    destroyAllPreviews() {
        this.windowManager.destroyAllWindows();
        this.previewWindows.clear();
        this.focusedWindowId = null;

        console.log('🧹 所有预览窗口已关闭');
    }

    /**
     * 关闭最旧的窗口
     */
    closeOldestWindow() {
        const oldestWindow = Array.from(this.previewWindows.values())[0];
        if (oldestWindow) {
            this.destroyPreview(oldestWindow.id);
            console.log('🔄 已关闭最旧的窗口以释放空间');
        }
    }

    /**
     * 聚焦下一个窗口
     */
    focusNextWindow() {
        this.windowManager.focusNextWindow();
        this.focusedWindowId = this.windowManager.focusedWindowId;
    }

    /**
     * 聚焦上一个窗口
     */
    focusPrevWindow() {
        this.windowManager.focusPrevWindow();
        this.focusedWindowId = this.windowManager.focusedWindowId;
    }

    /**
     * 关闭当前焦点窗口
     */
    closeFocusedWindow() {
        if (this.focusedWindowId) {
            this.destroyPreview(this.focusedWindowId);
        }
    }

    /**
     * 聚焦下一个可用窗口
     */
    focusNextAvailableWindow() {
        this.windowManager.focusNextAvailableWindow();
        this.focusedWindowId = this.windowManager.focusedWindowId;
    }

    /**
     * 根据URL查找窗口
     */
    findWindowByUrl(url) {
        for (const window of this.previewWindows.values()) {
            if (window.url === url) {
                return window;
            }
        }
        return null;
    }



    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        const oldEnabled = this.isEnabled;

        this.config = LinkUtils.deepMerge(this.config, newConfig);
        this.isEnabled = this.config.enabled;
        this.maxWindows = this.config.window?.maxWindows || 5;

        // 如果功能被禁用，关闭所有窗口
        if (oldEnabled && !this.isEnabled) {
            this.destroyAllPreviews();
            this.eventManager.disable();
        } else if (!oldEnabled && this.isEnabled) {
            this.eventManager.enable();
        }

        // 更新所有组件的配置
        this.eventManager.updateConfig(this.config);
        this.triggerHandlers.updateConfig(this.config.triggers);
        this.windowManager.setMaxWindows(this.maxWindows);

        // 如果窗口数量超过新的限制，关闭多余的窗口
        while (this.previewWindows.size > this.maxWindows) {
            this.closeOldestWindow();
        }

        console.log('🔄 链接预览配置已更新:', this.config);
    }

    /**
     * 启用功能
     */
    enable() {
        this.isEnabled = true;
        this.eventManager.enable();
        console.log('✅ 链接预览功能已启用');
    }

    /**
     * 禁用功能
     */
    disable() {
        this.isEnabled = false;
        this.destroyAllPreviews();
        this.eventManager.disable();
        console.log('⏸️ 链接预览功能已禁用');
    }

    /**
     * 获取所有窗口信息
     */
    getAllWindows() {
        return Array.from(this.previewWindows.values()).map(window => window.getInfo());
    }

    /**
     * 获取当前焦点窗口
     */
    getFocusedWindow() {
        return this.focusedWindowId ? this.previewWindows.get(this.focusedWindowId) : null;
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            currentCount: this.previewWindows.size,
            maxWindows: this.maxWindows,
            isEnabled: this.isEnabled
        };
    }

    /**
     * 获取配置信息
     */
    getConfig() {
        return JSON.parse(JSON.stringify(this.config));
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            totalCreated: 0,
            totalDestroyed: 0,
            currentCount: this.previewWindows.size
        };
        console.log('📊 统计信息已重置');
    }

    /**
     * 导出配置
     */
    exportConfig() {
        return {
            version: '1.0.0',
            timestamp: Date.now(),
            config: this.getConfig(),
            stats: this.getStats()
        };
    }

    /**
     * 导入配置
     */
    importConfig(exportedData) {
        try {
            if (exportedData.config) {
                this.updateConfig(exportedData.config);
                console.log('✅ 配置导入成功');
                return true;
            }
        } catch (error) {
            console.error('❌ 配置导入失败:', error);
            return false;
        }
    }

    /**
     * 调试信息
     */
    debug() {
        console.group('🔍 链接预览管理器调试信息');
        console.log('配置:', this.config);
        console.log('统计:', this.getStats());
        console.log('当前窗口:', this.getAllWindows());
        console.log('焦点窗口ID:', this.focusedWindowId);
        console.log('事件管理器状态:', this.eventManager.isEnabled);
        console.groupEnd();
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.destroyAllPreviews();
        this.eventManager.destroy();
        this.windowManager.destroy();
        this.triggerHandlers.destroy();

        // 清理高级功能
        if (this.textOperationsHandler) {
            this.textOperationsHandler.destroy();
            this.textOperationsHandler = null;
        }
        if (this.advancedConfigDialog) {
            this.advancedConfigDialog.destroy();
            this.advancedConfigDialog = null;
        }

        // 清理全局引用
        if (typeof window !== 'undefined' && window.app?.linkPreviewManager === this) {
            delete window.app.linkPreviewManager;
        }

        console.log('🧹 链接预览管理器已清理');
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.cleanup();

        // 清理所有引用
        this.previewWindows.clear();
        this.eventManager = null;
        this.windowManager = null;
        this.triggerHandlers = null;
        this.config = null;

        console.log('💥 链接预览管理器已销毁');
    }

    /**
     * 处理链接点击（由事件管理器调用）
     */
    handleLinkClick(event) {
        // 检查是否按下修饰键
        const modifierPressed = this.eventManager.isModifierPressed(event);
        if (!modifierPressed) return;

        // 查找链接元素
        const link = event.target.closest('a[href]');
        if (!link) return;

        // 阻止默认行为
        event.preventDefault();
        event.stopPropagation();

        // 获取链接URL
        const url = link.href;
        if (!url || url.startsWith('javascript:')) return;

        // 创建预览
        this.createPreview(url, {
            triggerElement: link,
            triggerEvent: event
        });
    }

    /**
     * 获取版本信息
     */
    getVersion() {
        return {
            version: '1.0.0',
            name: 'Link Preview Manager',
            description: '链接预览功能管理器',
            author: 'Moment Search Team',
            buildDate: '2025-08-20'
        };
    }

    /**
     * 检查浏览器兼容性
     */
    checkCompatibility() {
        const features = {
            iframe: typeof HTMLIFrameElement !== 'undefined',
            customElements: typeof customElements !== 'undefined',
            intersectionObserver: typeof IntersectionObserver !== 'undefined',
            resizeObserver: typeof ResizeObserver !== 'undefined',
            css3: typeof CSS !== 'undefined' && CSS.supports('transform', 'scale(1)'),
            es6: typeof Symbol !== 'undefined'
        };

        const isCompatible = Object.values(features).every(Boolean);

        if (!isCompatible) {
            console.warn('⚠️ 浏览器兼容性检查失败:', features);
        }

        return { isCompatible, features };
    }

    /**
     * 性能监控
     */
    getPerformanceMetrics() {
        const windows = this.getAllWindows();
        const memoryUsage = performance.memory ? {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        } : null;

        return {
            windowCount: windows.length,
            memoryUsage,
            averageLoadTime: 0, // 将在后续版本中实现
            stats: this.getStats()
        };
    }

    /**
     * 初始化高级功能
     */
    initializeAdvancedFeatures() {
        try {
            // 初始化文本操作处理器
            if (typeof TextOperationsHandler !== 'undefined') {
                this.textOperationsHandler = new TextOperationsHandler(this);
            }

            // 初始化高级配置弹窗
            if (typeof AdvancedConfigDialog !== 'undefined') {
                this.advancedConfigDialog = new AdvancedConfigDialog(this);
            }

            console.log('✅ 高级功能初始化完成');
        } catch (error) {
            console.error('❌ 高级功能初始化失败:', error);
        }
    }

    /**
     * 显示高级配置弹窗
     */
    showAdvancedConfig() {
        if (this.advancedConfigDialog) {
            this.advancedConfigDialog.show();
        } else {
            LinkUtils.showToast('高级配置功能未加载', 2000, 'warning');
        }
    }

    /**
     * 获取文本操作处理器
     */
    getTextOperationsHandler() {
        return this.textOperationsHandler;
    }

    /**
     * 获取高级配置弹窗
     */
    getAdvancedConfigDialog() {
        return this.advancedConfigDialog;
    }

    /**
     * 启用/禁用文本操作功能
     */
    setTextOperationsEnabled(enabled) {
        if (this.textOperationsHandler) {
            this.textOperationsHandler.setEnabled(enabled);
        }
    }

    /**
     * 获取高级功能状态
     */
    getAdvancedFeaturesStatus() {
        return {
            textOperations: {
                available: !!this.textOperationsHandler,
                enabled: this.textOperationsHandler ? this.textOperationsHandler.isEnabled : false,
                config: this.textOperationsHandler ? this.textOperationsHandler.getConfig() : null
            },
            advancedConfig: {
                available: !!this.advancedConfigDialog,
                visible: this.advancedConfigDialog ? this.advancedConfigDialog.isVisible : false
            }
        };
    }
}

// 暴露到全局作用域
if (typeof window !== 'undefined') {
    window.LinkPreviewManager = LinkPreviewManager;

    // 添加调试工具
    window.debugLinkPreview = function() {
        if (window.app?.linkPreviewManager) {
            window.app.linkPreviewManager.debug();
        } else {
            console.log('链接预览管理器未初始化');
        }
    };
}
